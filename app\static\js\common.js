// create a function to update the date and time
function updateTime() {
  // create a new `Date` object
  const now = new Date();

  // Reduce the date by 2 days
  now.setDate(now.getDate() - 2);

  const currentDate = now.getDate() + "/" + (now.getMonth() + 1) + "/" + now.getFullYear();
  document.querySelector('#date').textContent = currentDate;

  // get the current time as a string
  const currentTime = now.getHours() + ":" + now.getMinutes() + ":" + now.getSeconds();

  // update the `textContent` property of the `span` element with the `id` of `datetime`
  document.querySelector('#time').textContent = currentTime;
}

// call the `updateDateTime` function every second
setInterval(updateTime, 1000);

// Year dynamic
$('#year').html(new Date().getFullYear());


// WebSocket Handler Class
class WebSocketHandler {
    constructor(url) {
        this.url = url;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
        this.heartbeatInterval = 30000; // 10 seconds
        this.heartbeatTimer = null;
        this.init();
    }

    init() {
        this.ws = new WebSocket(this.url);
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.reconnectAttempts = 0;
            // this.startHeartbeat(); // Heartbeat disabled
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket disconnected');
            console.log('Close event:', event);
            if (event.code === 1005) {
                console.error('WebSocket closed with code 1005 (No Status Received). This usually means the server did not send a close frame.');
                // Optionally, do not attempt to reconnect:
                // return;
            }
            // this.stopHeartbeat(); // Heartbeat disabled
            this.reconnect();
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };

        this.ws.onmessage = (event) => {
            const data = event.data;
            if (data === 'pong') {
                return; // Ignore heartbeat responses
            }
            try {
                // Dispatch event for other scripts to handle
                window.dispatchEvent(new CustomEvent('websocket-message', { 
                    detail: data 
                }));
            } catch (e) {
                console.error('Failed to parse WebSocket message:', e);
            }
        };
    }

    reconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }

        setTimeout(() => {
            console.log('Attempting to reconnect...');
            this.reconnectAttempts++;
            this.init();
        }, this.reconnectInterval);
    }

    // startHeartbeat() {
    //     this.heartbeatTimer = setInterval(() => {
    //         if (this.ws.readyState === WebSocket.OPEN) {
    //             this.ws.send('ping');
    //         }
    //     }, this.heartbeatInterval);
    // }

    // stopHeartbeat() {
    //     if (this.heartbeatTimer) {
    //         clearInterval(this.heartbeatTimer);
    //         this.heartbeatTimer = null;
    //     }
    // }

    send(data) {
        if (this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(typeof data === 'string' ? data : JSON.stringify(data));
        } else {
            console.error('WebSocket is not connected');
        }
    }

    close() {
        // this.stopHeartbeat(); // Heartbeat disabled
        if (this.ws) {
            this.ws.close();
        }
    }
}

// Initialize WebSocket connection
const wsHandler = new WebSocketHandler('ws://127.0.0.1:8000/ws');

// Handle page unload
window.addEventListener('beforeunload', () => {
    wsHandler.close();
});

// Export wsHandler for other scripts to use
window.wsHandler = wsHandler;
