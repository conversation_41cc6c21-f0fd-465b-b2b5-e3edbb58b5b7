import base64
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from scipy.linalg import lstsq
from scipy.spatial import ConvexHull
from starlette.websockets import WebSocketState
import inspect
import signal
import math
import time
import cv2

import queue
from sklearn.cluster import DBSCAN
from sklearn.linear_model import LinearRegression
import json
import random
import threading
import os
import pickle
import asyncio
from collections import deque
import numpy as np
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from starlette.responses import JSONResponse
import os
import sys

# Add the project root directory to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)

from general.camera.CaptureCameraImage import DualCameraOperation
from general.common import calibration, utils
from general.common.tracker_object import TriangleTracker, pixel_to_point

from general.camera.CameraParams_header import MV_FRAME_OUT_INFO_EX
from ctypes import c_ubyte, byref, memset, sizeof
from xarm.wrapper import XArmAPI
from scipy.optimize import least_squares
from pydantic import BaseModel
from general.common.utils import SetplanningInputs
import joblib
import csv
from datetime import datetime

current_dir = os.path.dirname(os.path.abspath(__file__))
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount(
    "/static",
    StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")),
    name="static",
)
templates = Jinja2Templates(
    directory=os.path.join(os.path.dirname(__file__), "templates")
)

framecount = 0  # Track how many points are registered
variable_dict = {}  # Store registered points persistently
AutoAlign = False
MixAxialPlane = os.path.abspath(
    os.path.join(current_dir, "..", "..", "registration_data", "mid_Axial_skip.pickle")
)

from collections import Counter


def distance(p1, p2):
    return np.linalg.norm(p1 - p2)


arm_ip = "*************"  # Replace with your xArm6 IP
USE_ROBOT = True
ROBOT_SPEED = 50
page = None  # Global variable to track the current page
page_lock = asyncio.Lock()  # Lock for thread safety

USE_BURR = False
if USE_BURR:
    from general.common.motor_control import start_motor, stop_motor


async def is_same_page(current_page: str) -> bool:
    """Checks if the global page matches the current page. If not, terminates execution."""
    async with page_lock:
        if page != current_page:
            print(f"Terminating process: Expected {current_page}, but got {page}")
            return False
    return True


class CheckboxState(BaseModel):
    id: str  # Checkbox ID
    state: bool  # True if checked, False if unchecked


def calculate_contact_point(led1, led2, led3, surface_normal, point, radius=6.28):
    """
    Calculate 3D contact coordinates of a tilted hemispherical-tipped pointer.

    Parameters:
    led1, led2, led3 (np.array): 3D coordinates of the three base LEDs
    center_point (np.array): 3D coordinates of pointer tip center (C)
    radius (float): Hemisphere radius in mm

    Returns:
    np.array: Corrected 3D contact coordinates
    """
    # Ensure numpy arrays
    led1, led2, led3, surface_normal, point = map(
        np.array, (led1, led2, led3, surface_normal, point)
    )
    #
    # # Compute surface normal from the three LED positions
    # surface_normal = np.cross(led2 - led1, led3 - led1)
    # surface_normal /= np.linalg.norm(surface_normal)  # Normalize

    # Compute midpoint of LED B and C
    midpoint_bc = (led1 + led2) / 2.0

    # Compute pointer direction vector (from LED A to midpoint of LED B and C)
    v_pointer = midpoint_bc - led3
    v_pointer /= np.linalg.norm(v_pointer)  # Normalize

    surface_normal /= np.linalg.norm(surface_normal)  # Normalize

    print(f'v_pointer  {v_pointer}  surface_normal {surface_normal}')
    # Angle between pointer and surface normal
    cos_theta = np.dot(v_pointer, surface_normal)
    cos_theta = np.clip(cos_theta, -1.0, 1.0)  # Avoid numerical issues
    theta = np.arccos(cos_theta)

    # Compute displacement
    displacement = radius * np.sin(theta) * v_pointer

    # Compute contact point
    contact_point = point - displacement
    print(
        f'displacement {displacement}  theta {math.degrees(theta)}  cos_theta {cos_theta} point{point} contact_point {contact_point}')
    return contact_point


def compute_rigid_transform(A, B):
    centroid_A = np.mean(A, axis=0)
    centroid_B = np.mean(B, axis=0)
    AA = A - centroid_A
    BB = B - centroid_B
    H = AA.T @ BB
    U, _, Vt = np.linalg.svd(H)
    R = Vt.T @ U.T
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = Vt.T @ U.T
    t = centroid_B - R @ centroid_A
    return R, t


def pivot_calibration(led_frames):
    model = led_frames[0]
    A, b = [], []
    for current in led_frames:
        R, t = compute_rigid_transform(model, current)
        A.append(R)
        b.append(-t)
    A_block, b_block = [], []
    for R, t_vec in zip(A, b):
        A_block.append(np.hstack((R, -np.eye(3))))
        b_block.append(t_vec)
    A_stacked = np.vstack(A_block)
    b_stacked = np.hstack(b_block).reshape(-1, 1)
    x, _, _, _ = lstsq(A_stacked, b_stacked)
    tip_local = x[:3].flatten()
    return tip_local, model


def load_calibration(filename=None):
    CALIBRATION_FILE = os.path.join(
        current_dir, "..", "..", "registration_data", f"knee_tooltip_calibration{filename}.json"
    )
    with open(CALIBRATION_FILE, 'r') as f:
        data = json.load(f)
    tip_local = np.array(data['tip_local'])
    model_leds = np.array(data['model_leds'])
    return tip_local, model_leds


def estimate_tip_position(current_leds, model_leds, tip_local):
    R, t = compute_rigid_transform(model_leds, current_leds)
    return R @ tip_local + t


def save_calibration(tip_local, model_leds, filename=None):
    tip_local = np.array(tip_local)
    model_leds = np.array(model_leds)

    data = {
        'tip_local': tip_local.tolist(),
        'model_leds': model_leds.tolist()
    }
    CALIBRATION_FILE = os.path.join(
        current_dir, "..", "..", "registration_data", f"knee_tooltip_calibration{filename}.json"
    )
    with open(CALIBRATION_FILE, 'w') as f:
        json.dump(data, f, indent=2)


def load_data(file_path):
    led_points = []
    robot_points = []

    with open(file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line:
                led_part = line.split("LED [")[1].split("]")[0].split()
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")

                # Convert to float
                led_x, led_y, led_z = map(float, led_part)
                robot_x, robot_y, robot_z = map(float, robot_part)

                led_points.append([led_x, led_y, led_z])
                robot_points.append([robot_x, robot_y, robot_z])

    return np.array(led_points), np.array(robot_points)


def get_size(dFCSIZE):
    size_ranges = {
        (float("-inf"), 52.4): "SIZE A",
        (52.5, 54.3): "SIZE B",
        (54.4, 58): "SIZE C",
        (58.1, 59.4): "SIZE D",
        (59.5, 63.2): "SIZE E",
        (63.3, 66.4): "SIZE F",
        (66.5, 70.4): "SIZE G",
        (70.5, float("inf")): "SIZE H",
    }

    for size_range, size in size_ranges.items():
        if size_range[0] <= dFCSIZE <= size_range[1]:
            return size


def get_offset(size_label):
    size_offsets = {
        "SIZE A": 10,
        "SIZE B": 20,
        "SIZE C": 30,
        "SIZE D": 35,
        "SIZE E": 40,
        "SIZE F": 44,
        "SIZE G": 50,
        "SIZE H": 55,
    }
    return size_offsets.get(size_label, None)  # Return None if size not found


def GetRoboticArmInitialData(scalling=False):
    model = LinearRegression()
    robot_calib_data = os.path.join(
        current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
    )
    led_points, robot_points = load_data(file_path=robot_calib_data)
    led_points[:, [1, 2]] = led_points[:, [2, 1]]

    model.fit(led_points, robot_points)

    modelR = LinearRegression()
    modelR.fit(robot_points, led_points)
    if scalling:
        model_path = os.path.join(current_dir, "..", "..", "robotic_calib_data", "robot_to_led_model.pkl")
        modelR_path = os.path.join(current_dir, "..", "..", "robotic_calib_data", "led_to_robot_model.pkl")

        joblib.dump(model, model_path)
        joblib.dump(modelR, modelR_path)
        return
    robot_tracker = np.array(
        # [
        #     [496.65827452, 506.41708047, 341.23199054],
        #     [504.38122954, 506.24729182, 341.30220591],
        #     [500.58689031, 495.2857382, 345.21779137],
        # ]
        [
            [498.29005385, 508.1604091 , 339.18956681],
            [505.9869674, 507.896732, 337.99552637],
            [502.36602205, 497.03040245, 342.71460758],
        ]
    )
    if robot_tracker[1][1] >= robot_tracker[0][1]:
        robot_tracker_plane = [robot_tracker[2], robot_tracker[0], robot_tracker[1]]
    else:
        robot_tracker_plane = [robot_tracker[2], robot_tracker[1], robot_tracker[0]]

    C = [0, 0, 0]
    C = np.array(C).reshape(1, -1)
    old_mark_point = modelR.predict(C)
    old_mark_point[0][1], old_mark_point[0][2] = (
        old_mark_point[0][2],
        old_mark_point[0][1],
    )
    # Step 1: Compute centroid
    centroid = np.mean(robot_tracker_plane, axis=0)

    # Step 2: Compute translation vector
    translation_vector = old_mark_point[0] - centroid

    # Step 3: Translate the plane
    translated_plane = robot_tracker_plane + translation_vector

    data = {
        "robot_tracker_plane": robot_tracker_plane,
        "old_mark_point": old_mark_point[0],
        "translated_plane": translated_plane,
    }
    # print(f'data***************** {data}')
    return data


def GetRoboticArmInitialDataPartial():
    model = LinearRegression()
    robot_calib_data = os.path.join(
        current_dir, "..", "..", "robotic_calib_data", "partialKneeClibration.txt"
    )
    led_points, robot_points = load_data(file_path=robot_calib_data)
    led_points[:, [1, 2]] = led_points[:, [2, 1]]

    model.fit(led_points, robot_points)

    modelR = LinearRegression()
    modelR.fit(robot_points, led_points)
    # robot_tracker = np.array(
    #     [
    #         [496.65827452, 506.41708047, 341.23199054],
    #         [504.38122954, 506.24729182, 341.30220591],
    #         [500.58689031, 495.2857382, 345.21779137],
    #     ]
    robot_tracker = np.array(
        [
            [498.29005385, 508.1604091 , 339.18956681],
            [505.9869674, 507.896732, 337.99552637],
            [502.36602205, 497.03040245, 342.71460758],
        ]

    )
    if robot_tracker[1][1] >= robot_tracker[0][1]:
        robot_tracker_plane = [robot_tracker[2], robot_tracker[0], robot_tracker[1]]
    else:
        robot_tracker_plane = [robot_tracker[2], robot_tracker[1], robot_tracker[0]]

    C = [0, 0, 0]
    C = np.array(C).reshape(1, -1)
    old_mark_point = modelR.predict(C)
    old_mark_point[0][1], old_mark_point[0][2] = (
        old_mark_point[0][2],
        old_mark_point[0][1],
    )
    # Step 1: Compute centroid
    centroid = np.mean(robot_tracker_plane, axis=0)

    # Step 2: Compute translation vector
    translation_vector = old_mark_point[0] - centroid

    # Step 3: Translate the plane
    translated_plane = robot_tracker_plane + translation_vector

    data = {
        "robot_tracker_plane": robot_tracker_plane,
        "old_mark_point": old_mark_point[0],
        "translated_plane": translated_plane,
    }
    # print(f'data***************** {data}')
    return data


class HipMarking:
    """Manages camera operations and multi-pipeline processing."""

    def __init__(self):
        self.running = False
        self.frame_queue = queue.Queue(
            maxsize=1
        )
        self.lock = threading.Lock()
        self.num_workers = os.cpu_count() * 4
        self.QUEUE_SIZE = 15
        self.result_queue = deque(maxlen=self.QUEUE_SIZE)
        self.clients = set()
        self.lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=self.num_workers)  # for rectification + processing

        self.dual_cam = DualCameraOperation()
        self.dual_cam.initialize_cameras()
        self.dual_cam.start_grabbing()

        self.Camera_left = self.dual_cam.cam
        self.Camera_right = self.dual_cam.cam2
        self.nPayloadSize_left = self.dual_cam.nPayloadSize
        self.nPayloadSize_right = self.dual_cam.nPayloadSize2
        self.data_buf_left = byref((c_ubyte * self.nPayloadSize_left)())
        self.data_buf_right = byref((c_ubyte * self.nPayloadSize_right)())
        self.frame_id = 0
        self.running = True
        threading.Thread(target=self.capture_frames, daemon=True).start()

    def capture_frames(self):
        stFrameInfo_left = MV_FRAME_OUT_INFO_EX()
        stFrameInfo_right = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo_left), 0, sizeof(stFrameInfo_left))
        memset(byref(stFrameInfo_right), 0, sizeof(stFrameInfo_right))

        while self.running:
            try:
                batch = []

                for _ in range(self.QUEUE_SIZE):
                    if not self.running:
                        return

                    ret1 = self.Camera_left.MV_CC_GetOneFrameTimeout(
                        self.data_buf_left, self.nPayloadSize_left, stFrameInfo_left, 500
                    )
                    ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(
                        self.data_buf_right, self.nPayloadSize_right, stFrameInfo_right, 500
                    )

                    if ret1 == 0 and ret2 == 0:
                        img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                            (stFrameInfo_left.nHeight, stFrameInfo_left.nWidth, 1)
                        )
                        img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                            (stFrameInfo_right.nHeight, stFrameInfo_right.nWidth, 1)
                        )
                        batch.append((img_left, img_right))

                    else:
                        print(f"[WARN] Frame grab failed: ret1={ret1}, ret2={ret2}")

                # Submit each frame pair for processing
                for img_left, img_right in batch:
                    self.executor.submit(self.process_frame, img_left, img_right)
                time.sleep(0.05)  # wait 10ms between batches
            except Exception as e:
                print(f"[ERROR] Frame capture failed: {e}")
                time.sleep(0.01)
                break

    def process_frame(self, img_left, img_right):
        try:
            # thread_id = threading.get_ident()
            # start = time.time()
            rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
            # print(f"[TIMER] Rectify took {time.time() - start:.3f}s")
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            # print(f"[TIMER] Contour + KMeans took {time.time() - start:.3f}s")
            # print(f'thread_id {thread_id} processed_result {len(detections_left)} {len(detections_right)}')
            if len(detections_right) == len(detections_left) and len(detections_left) >= 3:        #################################################################################
                processed_result = {
                    "left": detections_left,
                    "right": detections_right,
                    "timestamp": time.time()
                }
                # print(f'process thread {len(detections_left)} {len(detections_right)} {detections_left}')
                with self.lock:
                    if self.frame_queue.full():
                        self.frame_queue.get_nowait()  # Remove the oldest frame
                    self.frame_queue.put((rec_img_left, rec_img_right))
                    self.result_queue.append(processed_result)  # circular buffer handles overflow
            else:
                time.sleep(0.1)

        except Exception as e:
            print(f"[ERROR] Processing failed: {e}")
            self.stop()


    def get_last_n_results(self, n=10, max_age_ms=10):
        now = time.time()
        threshold = now - (max_age_ms / 1000.0)
        self.recent_results = []

        with self.lock:
            for item in list(self.result_queue):
                if item["timestamp"] >= threshold:
                    self.recent_results.append(item)

        return self.recent_results[-n:]

    async def get_latest_frame(self):
        """Fetch the latest frame if available, otherwise wait briefly."""
        while self.running:
            try:
                with self.lock:
                    if not self.frame_queue.empty():
                        return self.frame_queue.get_nowait()
            except queue.Empty:
                pass
            await asyncio.sleep(0.02)  # Retry after a short delay
        return None

    def average_pairs(self, detection_list):
        """
        Averages corresponding (x, y) pairs across frames for left and right.
        Assumes each detection list has variable length ≤ 12.
        """
        max_pairs = 12
        avg_left = []
        avg_right = []

        for side in ["left", "right"]:
            pair_matrix = [[] for _ in range(max_pairs)]

            for d in detection_list:
                points = d.get(side, [])
                for i, pt in enumerate(points[:max_pairs]):
                    pair_matrix[i].append(pt)

            avg_side = []
            for pts in pair_matrix:
                if pts:
                    arr = np.array(pts)
                    mean_pt = np.mean(arr, axis=0)
                    avg_side.append(tuple(mean_pt))
            if side == "left":
                avg_left = avg_side
            else:
                avg_right = avg_side

        return avg_left, avg_right

    def write_mid_axial(self, value: bool):
        with self.lock:  # Ensure only one thread writes at a time
            with open(MixAxialPlane, "wb") as file:
                pickle.dump(
                    {"mid_Axial": value}, file, protocol=pickle.HIGHEST_PROTOCOL
                )

    # Thread-Safe Read
    def read_mid_axial(self):
        with self.lock:  # Ensure thread-safe reading
            try:
                with open(MixAxialPlane, "rb") as file:
                    data = pickle.load(file)
                    return data.get(
                        "mid_Axial", None
                    )  # Return True/False or None if missing
            except (FileNotFoundError, EOFError):
                return None  # Handle missing/empty file safely

    async def handle_irq_average(self, n=10):
        last_n = self.get_last_n_results(n)
        # for i in last_n:
        # print(i)
        return self.average_pairs(last_n)

    def stop(self):
        """Stop grabbing and clean up."""
        self.running = False
        self.executor.shutdown(wait=True)  # Wait for all tasks to finish
        try:
            self.Camera_left.MV_CC_StopGrabbing()
            self.Camera_right.MV_CC_StopGrabbing()
            self.Camera_left.MV_CC_CloseDevice()
            self.Camera_right.MV_CC_CloseDevice()
            print("[INFO] Cameras released.")
        except Exception as e:
            print(f"[ERROR] Error during camera shutdown: {e}")
        os._exit(0)  # force exit after cleanup

    def handle_signal(self, signum, frame):
        self.stop()

    def setup_signal_handlers(self):
        signal.signal(signal.SIGINT, self.handle_signal)
        signal.signal(signal.SIGTERM, self.handle_signal)


# if __name__ == "__main__":
# Global instance of `HipMarking`
marking = HipMarking()
marking.setup_signal_handlers()
marking.write_mid_axial(False)


def calculate_transformation_matrix_with_scaling(source, target):
    """Calculate transformation matrix with scaling from source points to target points."""
    # Calculate centroids
    centroid_source = np.mean(source, axis=0)
    centroid_target = np.mean(target, axis=0)

    # Center the points
    centered_source = source - centroid_source
    centered_target = target - centroid_target

    # Estimate scaling factor
    scale = np.sum(np.linalg.norm(centered_target, axis=1)) / np.sum(
        np.linalg.norm(centered_source, axis=1)
    )

    # Apply scaling to source points
    scaled_source = centered_source * scale

    # Compute H matrix for SVD
    H = np.dot(scaled_source.T, centered_target)

    # Compute rotation matrix using SVD
    U, S, Vt = np.linalg.svd(H)
    R_matrix = np.dot(Vt.T, U.T)

    # Correct for improper rotation (reflection) if determinant is negative
    if np.linalg.det(R_matrix) < 0:
        Vt[-1, :] *= -1
        R_matrix = np.dot(Vt.T, U.T)

    # Compute translation vector
    t = centroid_target - np.dot(R_matrix, centroid_source * scale)

    # Construct the final transformation matrix
    transformation_matrix = np.eye(4)
    transformation_matrix[:3, :3] = R_matrix * scale
    transformation_matrix[:3, 3] = t

    return transformation_matrix


async def send_tuple(websocket: WebSocket, data: tuple):
    serialized_data = ",".join(map(str, data))
    await websocket.send_text(serialized_data)


async def send_json(websocket: WebSocket, data: dict):
    serialized_data = json.dumps(data)
    await websocket.send_text(serialized_data)


@app.get("/")
async def read_index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/values")
async def get_values():
    try:
        # Open the JSON file and read it
        with open(r"D:\SpineSurgery\pythonProject\values.json", "r") as file:
            data = json.load(file)
        return JSONResponse(content=data)
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)


@app.post("/update_checkbox")
async def update_checkbox_state(data: CheckboxState):
    # Process the received data
    # You can store the state in a database or perform other actions
    print(f"Checkbox ID: {data.id}, State: {data.state}")
    SetplanningInputs(filename=str(data.id), data=str(data.state))
    return {"message": "Checkbox state updated successfully"}


class SkipClickRequest(BaseModel):
    clicked: bool


@app.post("/log_skip_click")
async def log_skip_click(request: SkipClickRequest):
    try:
        # with open(MixAxialPlane, "wb") as file:
        #     variable_memory = {'mid_Axial': request.clicked}
        #     pickle.dump(variable_memory, file, protocol=pickle.HIGHEST_PROTOCOL)
        marking.write_mid_axial(request.clicked)
        return {"success": True}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.get("/form.html", response_class=HTMLResponse)
async def form(request: Request):
    return templates.TemplateResponse(
        "form.html", {"request": request}
    )  # Load form.html


@app.get("/landing-page.html")
async def read_landing_page(request: Request):
    return templates.TemplateResponse("landing-page.html", {"request": request})


@app.get("/hip/register-acetabulum.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/hip/register-acetabulum.html", {"request": request}
    )


@app.get("/knee/pointer.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/knee/pointer.html", {"request": request}
    )


@app.get("/revision-thr/handle-position.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/handle-position.html", {"request": request}
    )


@app.get("/revision-thr/TP2_marking.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/TP2_marking.html", {"request": request}
    )


@app.get("/revision-thr/final-cup-position.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/final-cup-position.html", {"request": request}
    )


@app.get("/revision-thr/revhipFreePointCollection.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/revhipFreePointCollection.html", {"request": request}
    )


@app.get("/hip/hipRingPointCollection.html")
async def hipRingPointCollection(request: Request):
    return templates.TemplateResponse(
        "/hip/hipRingPointCollection.html", {"request": request}
    )


@app.get("/hip/hipFreePointCollection.html")
async def hipFreePointCollection1(request: Request):
    return templates.TemplateResponse(
        "/hip/hipFreePointCollection.html", {"request": request}
    )


@app.get("/revision-thr/result.html")
async def revhipFreePointCollection1(request: Request):
    return templates.TemplateResponse("/revision-thr/result.html", {"request": request})


@app.get("/hip/handle-position.html")
async def read_handle_position(request: Request):
    return templates.TemplateResponse("/hip/handle-position.html", {"request": request})


@app.get("/hip/anterior-pelvic.html")
async def anterior_pelvic(request: Request):
    return templates.TemplateResponse("/hip/anterior-pelvic.html", {"request": request})


@app.get("/hip/final-cup-position.html")
async def read_final_cup_position(request: Request):
    return templates.TemplateResponse(
        "/hip/final-cup-position.html", {"request": request}
    )


@app.get("/revision-thr/mid-axial-body-plane.html")
async def revisionHipmidAxisPlane(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/mid-axial-body-plane.html", {"request": request}
    )


@app.get("/revision-thr/register-acetabulum.html")
async def revisionHipmidAxisPlane(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/register-acetabulum.html", {"request": request}
    )


async def rev_register_acetabulum(marking, websocket, callingStage=None):
    global framecount, variable_dict

    try:
        # Load Transformation Plane (TP) variables
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
        print("✅ TP variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["ASIS", "TF", "PSIS", "IT", "TAL", "PS"]
    framecount = 0
    while framecount < len(point_reg):
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])

                # Extract tracked points
                first_three_right, next_three_right = (
                    detections_right[:3],
                    detections_right[-3:],
                )
                first_three_left, next_three_left = detections_left[:3], detections_left[-3:]

                # Track femur (static reference points)
                femur = TriangleTracker(
                    tuple(first_three_right),
                    tuple(first_three_left),
                )

                F_points = femur.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    Tracker_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                Pointer_tracker = TriangleTracker(
                    tuple(next_three_right), tuple(next_three_left))

                pointer_tip_gcs = Pointer_tracker.getStylusPoint()

                pointRegistered = utils.find_new_point_location(
                    old_plane_points=Tracker_plane,
                    new_plane_points=TP["TP"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )

                variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
                variable_dict[point_reg[framecount]] = variable_memory
                print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")
                # Attempt WebSocket communication
                try:
                    await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
                except Exception as e:
                    print(f"⚠️ WebSocket send failed: {e}")
                # Play notification sound
                utils.play_notification_sound()
                framecount += 1  # Move to the next point
                await asyncio.sleep(0.1)  # 100ms delay
            else:
                await asyncio.sleep(0.1)
        except Exception as e:
            print(f' Error {e}')
            return
    print("🎯 All 6 points registered. Saving data...")
    if not callingStage:
        Hip_threePoint_path = os.path.join(
            current_dir,
            "..",
            "..",
            "registration_data",
            "revision_hip_variables.pickle",
        )
    else:
        Hip_threePoint_path = os.path.join(
            current_dir,
            "..",
            "..",
            "registration_data",
            f"revision_hip_variables{callingStage}.pickle",
        )
    os.makedirs(os.path.dirname(Hip_threePoint_path), exist_ok=True)

    try:
        with open(Hip_threePoint_path, "wb") as handle:
            pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
        print("✅ Pickle dump successful.")

    except (OSError, pickle.PickleError) as e:
        print(f"❌ Error saving hip variables: {e}")
    await send_tuple(websocket, ("exit", 1, 3, 0))


async def register_acetabulum(marking, websocket, callingStage=None):
    try:
        # Load Transformation Plane (TP) variables
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
        print("✅ TP variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["FO", "SU", "CA", "AT", "PT"]
    framecount = 0
    while framecount < len(point_reg):
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if page not in ['acetabulum-component-placement.html', 'register-acetabulum.html']:
                return
            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])

                # Extract tracked points
                first_three_right, next_three_right = (
                    detections_right[:3],
                    detections_right[-3:],
                )
                first_three_left, next_three_left = detections_left[:3], detections_left[-3:]

                # Track femur (static reference points)
                femur = TriangleTracker(
                    tuple(first_three_right),
                    tuple(first_three_left),
                )

                F_points = femur.getLEDcordinates()
                Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                Pointer_tracker = TriangleTracker(
                    tuple(next_three_right), tuple(next_three_left))

                pointer_tip_gcs = Pointer_tracker.getStylusPoint()

                pointRegistered = utils.find_new_point_location(
                    old_plane_points=Tracker_plane,
                    new_plane_points=TP["TP"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
                variable_dict[point_reg[framecount]] = variable_memory
                print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")

                # Attempt WebSocket communication
                try:
                    await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
                except Exception as e:
                    print(f"⚠️ WebSocket send failed: {e}")
                # Play notification sound
                utils.play_notification_sound()

                framecount += 1  # Move to the next point
            else:
                await asyncio.sleep(0.1)  # 100ms delay
        except Exception as e:
            print(f"❌ Unexpected error in register_acetabulum: {e}")
            await asyncio.sleep(0.1)  # 100ms delay
            return
    print("🎯 All 5 points registered. Saving data...")
    if not callingStage:
        Hip_threePoint_path = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_variables.pickle"
        )
    else:
        Hip_threePoint_path = os.path.join(
            current_dir,
            "..",
            "..",
            "registration_data",
            f"hip_variables.pickle{callingStage}",
        )
    os.makedirs(os.path.dirname(Hip_threePoint_path), exist_ok=True)

    try:
        with open(Hip_threePoint_path, "wb") as handle:
            pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
        print("✅ Pickle dump successful.")

    except (OSError, pickle.PickleError) as e:
        print(f"❌ Error saving hip variables: {e}")


async def hipmidAxialBodyPlane(marking, websocket):
    global framecount, variable_dict

    try:
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
            print("TP variables loaded successfully")
    except Exception as e:
        print(f"Error loading TP variables {e}")

    point_reg = ["NOTE1", "NOTE2"]
    framecount = 0
    while framecount < len(point_reg):
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            early_stop = marking.read_mid_axial()
            print(f"early_stop {early_stop}")
            if early_stop in (None, True):
                framecount = 0
                return
            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])

                # Extract tracked points
                first_three_right, next_three_right = (
                    detections_right[:3],
                    detections_right[-3:],
                )
                first_three_left, next_three_left = detections_left[:3], detections_left[-3:]

                # Track femur (static reference points)
                femur = TriangleTracker(
                    tuple(first_three_right),
                    tuple(first_three_left),
                )

                F_points = femur.getLEDcordinates()
                Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                Pointer_tracker = TriangleTracker(
                    tuple(next_three_right), tuple(next_three_left))

                pointer_tip_gcs = Pointer_tracker.getStylusPoint()

                pointRegistered = utils.find_new_point_location(
                    old_plane_points=Tracker_plane,
                    new_plane_points=TP["TP"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )

                variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
                variable_dict[point_reg[framecount]] = variable_memory
                print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")
                try:
                    await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
                except Exception as e:
                    print(f"⚠️ WebSocket send failed: {e}")
                # Play notification sound
                utils.play_notification_sound()

                # Attempt WebSocket communication

                framecount += 1  # Move to the next point
                await asyncio.sleep(0.1)  # 100ms delay
            else:
                await asyncio.sleep(0.5)  # 100ms delay
        except Exception as e:
            print(f"❌ Unexpected error in register_acetabulum: {e}")
            await asyncio.sleep(0.1)  # 100ms delay
            return


@app.get("/hip/leg-length-assessment.html")
async def read_leg_length_assessment(request: Request):
    return templates.TemplateResponse(
        "/hip/leg-length-assessment.html", {"request": request}
    )


@app.get("/hip/mid-axial-body-plane.html")
async def mid_axial_body_plane(request: Request):
    return templates.TemplateResponse(
        "/hip/mid-axial-body-plane.html", {"request": request}
    )


@app.get("/revision-thr/leg-length-assessment.html")
async def read_leg_length_assessment(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/leg-length-assessment.html", {"request": request}
    )


@app.get("/hip/acetabulum-component-placement.html")
async def read_acetabulum_component_placement(request: Request):
    return templates.TemplateResponse(
        "/hip/acetabulum-component-placement.html", {"request": request}
    )


@app.get("/hip/TP2_marking.html")
async def revtp2_marking(request: Request):
    return templates.TemplateResponse("/hip/TP2_marking.html", {"request": request})


@app.get("/revision-thr/TP2_marking.html")
async def revtp2_marking(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/TP2_marking.html", {"request": request}
    )


@app.get("/hip/operating-positio-screen.html")
async def read_operating_position_screen(request: Request):
    return templates.TemplateResponse(
        "/hip/operating-positio-screen.html", {"request": request}
    )


@app.get("/revision-thr/operating-positio-screen.html")
async def read_operating_position_screen(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/operating-positio-screen.html", {"request": request}
    )


@app.get("/hip/system-setup.html")
async def system_setup():
    return templates.TemplateResponse("/hip/system-setup.html", {"request": {}})


@app.get("/hip/system-setup-2.html")
async def system_setup():
    return templates.TemplateResponse("/hip/system-setup-2.html", {"request": {}})


@app.get("/revision-thr/system-setup.html")
async def system_setup():
    return templates.TemplateResponse(
        "/revision-thr/system-setup.html", {"request": {}}
    )


@app.get("/revision-thr/system-setup-2.html")
async def system_setup():
    return templates.TemplateResponse(
        "/revision-thr/system-setup-2.html", {"request": {}}
    )


@app.get("/hip/femur-registration.html")
async def read_femur_registration():
    return templates.TemplateResponse("/hip/femur-registration.html", {"request": {}})


@app.get("/revision-thr/femur-registration.html")
async def read_femur_registration():
    return templates.TemplateResponse(
        "/revision-thr/femur-registration.html", {"request": {}}
    )


# @app.get("/hip/pelvis-registration.html")
# async def read_pelvis_registration():
#     return templates.TemplateResponse("/hip/pelvis-registration.html", {"request": {}})


# @app.get("/revision-thr/pelvis-registration.html")
# async def read_pelvis_registration():
#     return templates.TemplateResponse("/revision-thr/pelvis-registration.html", {"request": {}})


@app.get("/hip/pelvis-registration-2.html")
async def read_pelvis_registration_2(request: Request):
    return templates.TemplateResponse(
        "/hip/pelvis-registration-2.html", {"request": request}
    )


@app.get("/revision-thr/pelvis-registration-2.html")
async def read_pelvis_registration_2(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/pelvis-registration-2.html", {"request": request}
    )


async def hip_pelvis_registration_2(marking, websocket):
    try:
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
            print("✅ TP variables loaded successfully")
    except Exception as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["ASL", "ASR", "PSC"]  # Points to register
    framecount = 0
    while framecount < len(point_reg):
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 6 and len(detections_left) == 6:

                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])

                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                next_three_right = detections_right[-3:]
                next_three_left = detections_left[-3:]

                femur = TriangleTracker(
                    tuple(first_three_right),
                    tuple(first_three_left),
                )

                F_points = femur.getLEDcordinates()
                Tracker_plane = [F_points[2], F_points[1], F_points[0]]
                Pointer_tracker = TriangleTracker(
                    tuple(next_three_right),
                    tuple(next_three_left),
                )

                pointer_tip_gcs = Pointer_tracker.getStylusPoint()

                pointRegistered = utils.find_new_point_location(
                    old_plane_points=Tracker_plane,
                    new_plane_points=TP["TP"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )

                variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
                variable_dict[point_reg[framecount]] = variable_memory
                print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")
                await send_tuple(websocket, (point_reg[framecount], 1, 3, 0))
                utils.play_notification_sound()
                framecount += 1
                await asyncio.sleep(0.1)
            else:
                await asyncio.sleep(0.5)
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return
    print("🎯 All 3 points registered. Saving data...")
    Hip_threePoint_path = os.path.join(
        current_dir,
        "..",
        "..",
        "registration_data",
        "hip_three_variables.pickle",
    )
    os.makedirs(os.path.dirname(Hip_threePoint_path), exist_ok=True)

    with open(Hip_threePoint_path, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print("✅ Pickle dump successful.")


def set_robotic_arm_manual_mode():
    try:
        # Connect to xArm6
        arm = XArmAPI(arm_ip)

        # Check if the connection was successful
        if not arm.connected:
            print("Failed to connect to xArm6.")
            return

        # Retrieve current mode and state
        mode = arm.mode
        state = arm.state

        print(f"Current Mode: {mode}, State: {state}")

        # If the arm is paused, set it to IDLE
        if state == 2:  # Paused state
            print("Arm is in Paused state. Switching to IDLE...")
            arm.set_state(0)  # Set state to IDLE
            time.sleep(0.5)

        # Enable motion & servo power
        arm.motion_enable(True)
        arm.set_servo_attach()  # Ensure servos are powered on
        time.sleep(0.5)

        # Verify motion enable
        if arm.state in [4, 5]:  # Error or protective stop state
            print("Motion enable failed. Trying to clear errors...")
            arm.clean_error()
            arm.clean_warn()
            time.sleep(0.5)
            arm.motion_enable(True)
            arm.set_servo_attach()

        # Ensure it's in Manual Mode
        if mode == 2:
            print("Already in Manual Mode.")
        else:
            print("Switching to Manual Mode...")
            arm.set_mode(2)
            time.sleep(0.5)
            arm.set_state(0)  # Set state to IDLE
            time.sleep(0.5)

        print("Manual Mode is now active. You can move the robotic arm manually.")

    except Exception as e:
        print(f"An error occurred: {e}")

    finally:
        # Ensure disconnection only if connected
        if arm.connected:
            try:
                arm.disconnect()
            except Exception as e:
                print(f"Error while disconnecting: {e}")


def set_robotic_arm_ready_to_move(axis_angle_pose):
    try:
        # Connect to xArm6
        arm = XArmAPI(arm_ip)  # Replace with your xArm6 IP

        if arm is None:
            print("Failed to connect to xArm6.")
            return

        # Get the current mode and state
        try:
            mode = arm.mode  # Read mode
            state = arm.state  # Read state
            print(f"Current Mode: {mode}, State: {state}")
        except Exception as e:
            print(f"Error retrieving mode/state: {e}")
            arm.disconnect()
            return

        # Switch to Manual Mode if not already
        if mode != 0:
            try:
                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to manual mode
                arm.set_state(0)  # Set to ready state
                print("Switched to Manual Mode.")
            except Exception as e:
                print(f"Error switching to Manual Mode: {e}")
                arm.disconnect()
                return
        else:
            print("Already in Manual Mode.")

        # Get current position
        try:
            code, position = arm.get_position_aa()
            if code == 0:
                x, y, z, rx, ry, rz = position  # RX, RY, RZ are in radians
                axis_angle_pose[:3] = [x, y, z]  # Update X, Y, Z
            else:
                print(f"Failed to retrieve position, error code: {code}")
                arm.disconnect()
                return
        except Exception as e:
            print(f"Error retrieving position: {e}")
            arm.disconnect()
            return

        # Move the robotic arm
        try:
            code = arm.set_position_aa(axis_angle_pose, speed=45, mvacc=500, wait=True)
            if code != 0:
                print(f"Failed to move arm, error code: {code}")
        except Exception as e:
            print(f"Error setting position: {e}")

    except Exception as e:
        print(f"Unexpected error: {e}")

    finally:
        # Ensure the arm disconnects even in case of an error
        arm.disconnect()
        print("Disconnected from xArm6.")


async def hip_final_cup_position(marking, websocket):
    try:
        Hip_variable_path = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_variables.pickle"
        )
        with open(Hip_variable_path, "rb") as handle:
            hip_dict = pickle.load(handle)
            print("Hip variables loaded successfully")

        Hip_point_cloud_file = os.path.join(
            current_dir, "..", "..", "registration_data", "RingPointCloud.pickle"
        )
        with open(Hip_point_cloud_file, "rb") as handle:
            pointCloud_dict = pickle.load(handle)
            print("Hip PointCloud loaded successfully")

    except Exception as e:
        print(f"Error loading TP variables: {e}")
        return

    global USE_ROBOT
    cup_seating = None
    set_robotic_arm_manual_mode()
    initial_data = GetRoboticArmInitialData()
    FILE_PATH = os.path.join(
        current_dir, "..", "..", "registration_data", "robotic_positions.json"
    )
    if os.path.exists(FILE_PATH):
        with open(FILE_PATH, "r") as file:
            loaded_data = json.load(file)
    data_dict = {
        "antiversion": random.randint(20, 70),  # Random between 0 and 50
        "inclination": random.randint(5, 50),  # Random between 20 and 70
        "status_colour": "red",  # Keeping within expected range
        "status_text": "Auto Align",  # Keeping within expected range
    }
    await send_json(websocket, data_dict)
    await asyncio.sleep(0.5)  # ✅ Prevents CPU Overload

    while True:  # ✅ Continuous Loop Instead of Recursion
        if page != 'final-cup-position.html':
            return
        detections_left, detections_right = await marking.handle_irq_average(n=10)
        if len(detections_right) == 6 and len(detections_left) == 6:
            detections_right = sorted(
                detections_right, key=lambda x: x[1], reverse=True
            )
            detections_left = sorted(
                detections_left, key=lambda x: x[1], reverse=True
            )
            first_three_right = sorted(detections_right[:3], key=lambda x: x[0])
            first_three_left = sorted(detections_left[:3], key=lambda x: x[0])

            next_three_right = sorted(detections_right[3:], key=lambda x: x[0])
            next_three_left = sorted(detections_left[3:], key=lambda x: x[0])

            femure = TriangleTracker(
                next_three_right,
                next_three_left,
            )

            F_points = femure.getLEDcordinates()

            robot_tracker = TriangleTracker(
                first_three_right,
                first_three_left,
            )
            R_points = robot_tracker.getLEDcordinates()

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(R_points),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)

            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
            )
            robot_calib_data = os.path.join(
                current_dir,
                "..",
                "..",
                "robotic_calib_data",
                "robotic_init_data.txt",
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            # led_points[:, [1, 2]] = led_points[:, [2, 1]]
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            # hip_dict['FO']['C']
            point1 = utils.find_new_point_location(
                old_plane_points=hip_dict["FO"]["Plane"],
                new_plane_points=tuple(Newtranslated_plane),
                old_marked_point=hip_dict["FO"]["C"],
            )
            pointF0 = np.array(point1).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(pointF0)

            ANTERVERSION_angle, INCLINATION_angle = (
                utils.calculate_ANTERVERSION_INCLINATION_angles(
                    R_points[1] - R_points[0],
                    F_points[1] - F_points[0],
                    np.cross(F_points[1] - F_points[0], F_points[2] - F_points[1]),
                )
            )
            try:
                message = load_data(await websocket.receive_text())
                if message.get("action") == "auto_align":
                    print(f"*" * 100)
                    print(f'message.get("action")')
                    print(f"*" * 100)
                    await asyncio.sleep(0.2)  # ✅ Prevents CPU Overload
                    AutoAlign = True

            except Exception as e:
                continue
            if AutoAlign:
                axis_angle_pose = [
                    loaded_data["X"],
                    loaded_data["Y"],
                    loaded_data["Z"],
                    loaded_data["RX"],
                    loaded_data["RY"],
                    loaded_data["RZ"],
                ]
                set_robotic_arm_ready_to_move(axis_angle_pose)
                AutoAlign = False
                data_dict = {
                    "antiversion": random.randint(
                        20, 70
                    ),  # Random between 0 and 50
                    "inclination": random.randint(
                        5, 50
                    ),  # Random between 20 and 70
                    "status_colour": "green",  # Keeping within expected range
                    "status_text": "Auto Align",  # Keeping within expected range
                }
                await send_json(websocket, data_dict)
                cup_seating = True
                await asyncio.sleep(0.2)  # ✅ Prevents CPU Overload
                continue
            if cup_seating:
                data_dict = {
                    "antiversion": random.randint(
                        20, 70
                    ),  # Random between 0 and 50
                    "inclination": random.randint(
                        5, 50
                    ),  # Random between 20 and 70
                    "status_colour": "red",  # Keeping within expected range
                    "status_text": "Cup Seating",  # Keeping within expected range
                }
                await send_json(websocket, data_dict)
                await asyncio.sleep(0.5)  # ✅ Prevents CPU Overload
                cup_seating = False
                continue

            arm = XArmAPI(arm_ip)  # Replace with your xArm6 IP
            if arm.connected:
                code, position = arm.get_position_aa()
                if code == 0:  # Successful retrieval
                    x, y, z, rx, ry, rz = position  # RX, RY, RZ are in radians
                    data1 = abs(ANTERVERSION_angle) - rx
                    data2 = abs(INCLINATION_angle) - (180 - ry)
                    distance = utils.distance_3d(
                        np.array(
                            [
                                predicted_dest[0][0],
                                predicted_dest[0][1],
                                predicted_dest[0][2],
                            ]
                        ),
                        np.array([x, y, z]),
                    )
                    if distance < 0.3:
                        data_dict = {
                            "antiversion": random.randint(
                                20, 70
                            ),  # Random between 0 and 50
                            "inclination": random.randint(
                                5, 50
                            ),  # Random between 20 and 70
                            "status_colour": "green",  # Keeping within expected range
                            "status_text": "Cup Seating",  # Keeping within expected range
                        }
            await send_json(websocket, data_dict)
            await asyncio.sleep(0.1)  # ✅ Prevents CPU Overload


def fit_plane(point_cloud):
    """
    input
        point_cloud : list of xyz values　numpy.array
    output
        plane_v : (normal vector of the best fit plane)
        com : center of mass
    """

    com = np.sum(point_cloud, axis=0) / len(point_cloud)
    # calculate the center of mass
    q = point_cloud - com
    # move the com to the origin and translate all the points (use numpy broadcasting)
    Q = np.dot(q.T, q)
    # calculate 3x3 matrix. The inner product returns total sum of 3x3 matrix
    la, vectors = np.linalg.eig(Q)
    # Calculate eigenvalues and eigenvectors
    plane_v = vectors.T[np.argmin(la)]
    # Extract the eigenvector of the minimum eigenvalue

    return plane_v, com


async def hip_handle_position(marking, websocket):
    global USE_ROBOT

    try:
        Hip_variable_path = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_variables.pickle"
        )
        with open(Hip_variable_path, "rb") as handle:
            hip_dict = pickle.load(handle)
            print("Hip variables loaded successfully")

        Hip_point_cloud_file = os.path.join(
            current_dir, "..", "..", "registration_data", "RingPointCloud.pickle"
        )
        with open(Hip_point_cloud_file, "rb") as handle:
            pointCloud_dict = pickle.load(handle)
            print("Hip PointCloud loaded successfully")

    except Exception as e:
        print(f"Error loading TP variables: {e}")
        return

    for key, value in pointCloud_dict.items():
        locals()[key] = value["C"]
    point_reg = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"]
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        try:
            set_robotic_arm_manual_mode()
        except Exception as e:
            print("error")

    SAFE_CENTER = {"inclination": 15, "antiversion": 40}
    SAFE_RANGE = 2  # ±2 degrees

    in_safe_zone = (
            SAFE_CENTER["inclination"] - SAFE_RANGE
            <= SAFE_CENTER["inclination"]
            <= SAFE_CENTER["inclination"] + SAFE_RANGE
            and SAFE_CENTER["antiversion"] - SAFE_RANGE
            <= SAFE_CENTER["antiversion"]
            <= SAFE_CENTER["antiversion"] + SAFE_RANGE
    )

    FILE_PATH = os.path.join(
        current_dir, "..", "..", "registration_data", "robotic_positions.json"
    )
    if os.path.exists(FILE_PATH):
        os.remove(FILE_PATH)
        print(f"🗑️ Existing file '{FILE_PATH}' removed before starting.")
    try:
        while True:  # ✅ Continuous Loop Instead of Recursion
            if page != 'handle-position.html':
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)

            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = sorted(detections_right[:3], key=lambda x: x[0])
                first_three_left = sorted(detections_left[:3], key=lambda x: x[0])

                next_three_right = sorted(detections_right[3:], key=lambda x: x[0])
                next_three_left = sorted(detections_left[3:], key=lambda x: x[0])

                femure = TriangleTracker(
                    next_three_right,
                    next_three_left,
                )

                F_points = femure.getLEDcordinates()

                robot_tracker = TriangleTracker(
                    first_three_right,
                    first_three_left,
                )
                R_points = robot_tracker.getLEDcordinates()
                # R  (array([501.44909144, 557.82327568, 366.49651939]), array([509.29146161, 557.83370479, 367.43625405]), array([505.66557745, 547.25822684, 373.17744552]))
                new_origin = utils.find_new_point_location(
                    old_plane_points=initial_data["robot_tracker_plane"],
                    new_plane_points=tuple(R_points),
                    old_marked_point=initial_data["old_mark_point"],
                )

                # Step 1: Compute centroid
                centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)

                # Step 2: Compute translation vector
                translation_vector = new_origin - centroid

                # Step 3: Translate the plane
                Newtranslated_plane = (
                        initial_data["robot_tracker_plane"] + translation_vector
                )
                robot_calib_data = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "robotic_calib_data",
                    "robotic_init_data.txt",
                )
                led_points, robot_points = load_data(file_path=robot_calib_data)
                # led_points[:, [1, 2]] = led_points[:, [2, 1]]
                transformed_led_points = np.array(
                    [
                        utils.find_new_point_location(
                            old_plane_points=initial_data["translated_plane"],
                            new_plane_points=tuple(Newtranslated_plane),
                            old_marked_point=led,
                        )
                        for led in led_points
                    ]
                )

                # Swap Y and Z back to match coordinate system
                transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

                updated_model = LinearRegression()
                updated_model.fit(transformed_led_points, robot_points)
                # hip_dict['FO']['C']
                point1 = utils.find_new_point_location(
                    old_plane_points=hip_dict["FO"]["Plane"],
                    new_plane_points=tuple(Newtranslated_plane),
                    old_marked_point=hip_dict["FO"]["C"],
                )
                pointF0 = np.array(point1).reshape(1, -1)
                # point1 = point1 - old_origin_optical + new_origin
                predicted_dest = updated_model.predict(pointF0)

                Mid_point_RoboTracker = np.abs((R_points[1] + R_points[0]) / 2)
                RoboTracker_y = Mid_point_RoboTracker - R_points[2]
                RoboTracker_x = R_points[1] - R_points[0]

                RoboTrackervector_z = np.cross(RoboTracker_x, RoboTracker_y)

                # Normalize the Z-axis vector (to make it a unit vector)
                RoboTrackernorm_z = np.linalg.norm(RoboTrackervector_z)
                RoboTrackernormalized_z = RoboTrackervector_z / RoboTrackernorm_z
                RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)
                RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)
                RoboTrackernormalized_x = (
                    RoboTracker_x / RoboTrackernorm_x
                    if RoboTrackernorm_x != 0
                    else RoboTracker_x
                )
                RoboTrackernormalized_y = (
                    RoboTracker_y / RoboTrackernorm_y
                    if RoboTrackernorm_y != 0
                    else RoboTracker_y
                )
                if F_points[1][1] >= F_points[0][1]:
                    hip_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    hip_plane = [F_points[2], F_points[1], F_points[0]]

                mid_femure = np.abs((F_points[1] + F_points[0]) / 2)

                scale = 107 / utils.distance_3d(mid_femure, F_points[2])

                SU = utils.find_new_point_location(
                    hip_dict["SU"]["Plane"], hip_plane, hip_dict["SU"]["C"]
                )
                CA = utils.find_new_point_location(
                    hip_dict["CA"]["Plane"], hip_plane, hip_dict["CA"]["C"]
                )
                AT = utils.find_new_point_location(
                    hip_dict["AT"]["Plane"], hip_plane, hip_dict["AT"]["C"]
                )
                PT = utils.find_new_point_location(
                    hip_dict["PT"]["Plane"], hip_plane, hip_dict["PT"]["C"]
                )
                pointCloud = []
                for char in point_reg:
                    result = utils.find_new_point_location(
                        pointCloud_dict[char]["Plane"],
                        hip_plane,
                        pointCloud_dict[char]["C"],
                    )
                    pointCloud.append(result)

                pointCloud_array = np.array(pointCloud)
                accetabulumnAxis_x, com = fit_plane(pointCloud_array)
                accetabulumnAxisXnorm = accetabulumnAxis_x / np.linalg.norm(
                    accetabulumnAxis_x
                )
                accetabulumnAxis_y = np.array(SU) - np.array(
                    CA
                )  # Directly subtract the two arrays
                accetabulumnAxis_ynorm = accetabulumnAxis_y / np.linalg.norm(
                    accetabulumnAxis_y
                )
                accetabulumnAxis_z = np.cross(accetabulumnAxis_x, accetabulumnAxis_y)
                accetabulumnAxis_z_norm = accetabulumnAxis_z / np.linalg.norm(
                    accetabulumnAxis_z
                )

                disance_SU_CA = utils.distance_3d(SU, CA)
                accetabulumn_diameterinMM = disance_SU_CA * scale

                ANTERVERSION_angle, INCLINATION_angle = (
                    utils.calculate_ANTERVERSION_INCLINATION_angles(
                        RoboTrackernormalized_y,
                        accetabulumnAxisXnorm,
                        accetabulumnAxis_z_norm,
                    )
                )
                print(f'ANTERVERSION_angle {ANTERVERSION_angle}   INCLINATION_angle {INCLINATION_angle}')
                if USE_ROBOT:
                    from xarm.wrapper import XArmAPI

                    arm = XArmAPI(arm_ip)
                    arm.connect()

                    if arm.connected:
                        arm.motion_enable(enable=True)
                        code, position = arm.get_position_aa()

                        if code == 0:
                            x, y, z, rx, ry, rz = position
                            print(f"Current Position:\nX: {x} mm\nY: {y} mm\nZ: {z} mm   rx {rx}  ry {ry}")
                            data1 = 15 + abs(ANTERVERSION_angle) - (ry - 90)

                            data2 = 40 + abs(INCLINATION_angle) - rz
                            distance = utils.distance_3d(
                                np.array(
                                    [
                                        predicted_dest[0][0],
                                        predicted_dest[0][1],
                                        predicted_dest[0][2],
                                    ]
                                ),
                                np.array([x, y, z]),
                            )
                            data_dict = {
                                "antiversion": data2,  # Random between 0 and 50
                                "inclination": data1,  # Random between 20 and 70
                                "diameter": accetabulumn_diameterinMM,  # Keep diameter fixed or modify as needed
                                "status_value": distance,  # Keeping within expected range
                            }
                            await send_json(websocket, data_dict)
                            if distance <= 30:
                                # ✅ Check for a perfect match
                                if (
                                        data_dict["inclination"],
                                        data_dict["antiversion"],
                                ) == (40, 15):
                                    best_match = {
                                        "x": x,
                                        "y": y,
                                        "z": z,
                                        "rx": rx,
                                        "ry": ry,
                                        "rz": rz,
                                    }
                                    print("🎯 Perfect match found:", best_match)

                                # ✅ If no perfect match, check if within the safe zone
                                elif (
                                        SAFE_CENTER["inclination"] - SAFE_RANGE
                                        <= data_dict["inclination"]
                                        <= SAFE_CENTER["inclination"] + SAFE_RANGE
                                ) and (
                                        SAFE_CENTER["antiversion"] - SAFE_RANGE
                                        <= data_dict["antiversion"]
                                        <= SAFE_CENTER["antiversion"] + SAFE_RANGE
                                ):

                                    best_match = {
                                        "x": x,
                                        "y": y,
                                        "z": z,
                                        "rx": rx,
                                        "ry": ry,
                                        "rz": rz,
                                    }
                                    print("✅ Close to center (Safe Zone):", best_match)

                                else:
                                    best_match = None
                                    print("❌ Out of safe zone, not recording.")

                                # ✅ Store only valid positions
                                if best_match:
                                    existing_data = []

                                    if os.path.exists(FILE_PATH):
                                        with open(FILE_PATH, "r") as file:
                                            try:
                                                existing_data = json.load(file)
                                                if not isinstance(existing_data, list):
                                                    existing_data = []
                                            except json.JSONDecodeError:
                                                existing_data = []

                                    # Append the valid position without overwriting
                                    existing_data.append(best_match)
                                    with open(FILE_PATH, "w") as file:
                                        json.dump(existing_data, file, indent=4)

                                    print(
                                        f"📌 Updated file with new valid position: {best_match}"
                                    )
                    arm.disconnect()
                await asyncio.sleep(0.5)  # ✅ Prevents CPU Overload

    except WebSocketDisconnect:
        print("WebSocket disconnected, stopping hip position tracking.")
    except Exception as e:
        print(f"Unexpected error: {e}")
        return


async def hipCollectRingPointCloudMarkingPipeline(marking, websocket):
    point_reg = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"]
    try:
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
            print("✅ TP variables loaded successfully")
    except Exception as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    try:
        Hip_variable_path = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_variables.pickle"
        )
        with open(Hip_variable_path, "rb") as handle:
            hip_dict = pickle.load(handle)
            print("✅ Hip variables loaded successfully")
    except Exception as e:
        print(f"❌ Error loading Hip variables: {e}")
        return {}

    camera_reference = np.array(
        [
            hip_dict["FO"]["C"],
            hip_dict["SU"]["C"],
            hip_dict["CA"]["C"],
            hip_dict["AT"]["C"],
            hip_dict["PT"]["C"],
        ]
    )
    # for i in camera_reference:
    #     print(i)
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])

    print(f' camera_reference {camera_reference}')
    hip_STL_obj_points = np.array(
        [
            [-45.5239716, -2.5871658, -154.1749573],
            [-63.6134491, 29.2462463, -157.989151],
            [-22.7026062, 2.6151695, -149.2989197],
            [-30.7326965, 8.4291382, -133.5983887],
            [-15.77771, 17.0255318, -159.7479401],
        ]
    )

    # for org in hip_STL_obj_points:
    #     await send_tuple(websocket, org)
    #     await asyncio.sleep(0.1)

    model = LinearRegression()
    model.fit(camera_reference, hip_STL_obj_points)

    # Extract rotation (scaling included) and translation
    R_estimated = model.coef_  # Rotation and scaling
    t_estimated = model.intercept_  # Translation

    # Apply transformation to each point
    for i in camera_reference:
        transformed_point = np.dot(R_estimated, i) + t_estimated
        print(f"point {i}    transformed_point {transformed_point}")
    framecount = 0
    while framecount < len(point_reg):
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])

                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                next_three_right = detections_right[-3:]
                next_three_left = detections_left[-3:]

                femur = TriangleTracker(
                    tuple(first_three_right),
                    tuple(first_three_left),
                )

                F_points = femur.getLEDcordinates()
                Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                if framecount < 10:
                    Pointer_tracker = TriangleTracker(
                        tuple(next_three_right),
                        tuple(next_three_left),
                    )

                    pointer_tip_gcs = Pointer_tracker.getStylusPoint()

                    pointRegistered = utils.find_new_point_location(
                        old_plane_points=Tracker_plane,
                        new_plane_points=TP["TP"]["Plane"],
                        old_marked_point=pointer_tip_gcs,
                    )

                    variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
                    variable_dict[point_reg[framecount]] = variable_memory
                    # print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")

                    point = np.array(pointRegistered, dtype=np.float32)
                    point[1] = -point[1]
                    # transformed_point = np.dot(transformation_matrix[:3, :3], point) + transformation_matrix[:3, 3]

                    transformed_point = np.dot(R_estimated, point) + t_estimated

                    print(
                        f"🔵 Registered {point_reg[framecount]}: {pointRegistered}   transformed_point {transformed_point}"
                    )
                    await send_tuple(websocket, transformed_point)
                    utils.play_notification_sound()

                    framecount += 1
                    await asyncio.sleep(0.1)
            else:
                await asyncio.sleep(0.1)
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return

    print("🎯 All points registered. Saving data...")
    Hip_point_cloud_file = os.path.join(
        current_dir,
        "..",
        "..",
        "registration_data",
        "RingPointCloud.pickle",
    )
    os.makedirs(os.path.dirname(Hip_point_cloud_file), exist_ok=True)

    with open(Hip_point_cloud_file, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
    print("✅ Pickle dump successful.")
    await websocket.send_text("exit")


def save_progress(variable_dict, framecount, filepath):
    """Save progress to a pickle file."""
    with open(filepath, "wb") as handle:
        pickle.dump(
            {"variable_dict": variable_dict, "framecount": framecount},
            handle,
            protocol=pickle.HIGHEST_PROTOCOL,
        )


def load_progress(filepath):
    """Load progress from a pickle file."""
    if os.path.exists(filepath):
        with open(filepath, "rb") as handle:
            return pickle.load(handle)
    return {"variable_dict": {}, "framecount": 0}


async def hipFreePointCollection(marking, websocket: WebSocket, rev=None):
    # global variable_dict, framecount
    # progress_file = os.path.join(
    #     current_dir, "..", "..", "registration_data", "hip_progress.pickle"
    # )
    # progress_data = load_progress(progress_file)
    # variable_dict = progress_data["variable_dict"]
    # framecount = progress_data["framecount"]
    framecount = 0
    try:
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
            print("✅ TP variables loaded successfully")
    except Exception as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}
    if rev is None:
        try:
            Hip_variable_path = os.path.join(
                current_dir, "..", "..", "registration_data", "hip_variables.pickle"
            )
            with open(Hip_variable_path, "rb") as handle:
                hip_dict = pickle.load(handle)
                print("✅ Hip variables loaded successfully")
        except Exception as e:
            print(f"❌ Error loading Hip variables: {e}")
            return {}

        camera_reference = np.array(
            [
                hip_dict["FO"]["C"],
                hip_dict["SU"]["C"],
                hip_dict["CA"]["C"],
                hip_dict["AT"]["C"],
                hip_dict["PT"]["C"],
            ]
        )
        hip_STL_obj_points = np.array(
            [
                [-45.5239716, -2.5871658, -154.1749573],
                [-63.6134491, 29.2462463, -157.989151],
                [-22.7026062, 2.6151695, -149.2989197],
                [-15.77771, 17.0255318, -159.7479401],
                [-30.7326965, 8.4291382, -133.5983887],
            ]
        )
    else:
        try:
            Hip_variable_path = os.path.join(
                current_dir,
                "..",
                "..",
                "registration_data",
                "revision_hip_variables.pickle",
            )
            with open(Hip_variable_path, "rb") as handle:
                hip_dict = pickle.load(handle)
                print("✅Revision Hip variables loaded successfully")
        except Exception as e:
            print(f"❌ Error loading Hip variables: {e}")
            return {}

        camera_reference = np.array(
            [
                hip_dict["ASIS"]["C"],
                hip_dict["TF"]["C"],
                hip_dict["PSIS"]["C"],
                hip_dict["IT"]["C"],
                hip_dict["TAL"]["C"],
            ]
        )
        hip_STL_obj_points = np.array(
            [
                [-41.2922211, 433.4049377, 194.4227905],  # ASIS
                [-108.0319901, 358.8612061, 117.9057465],  # TF
                [-47.8687057, 331.510376, 82.7817383],  # PSIS
                [54.1078644, 397.3883972, 98.4199829],  # IT
                [43.462677, 386.5711975, 151.892395],  # TAL
            ]
        )

    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    # transformation_matrix = calculate_transformation_matrix_with_scaling(camera_reference, hip_STL_obj_points)
    for org in hip_STL_obj_points:
        await send_tuple(websocket, org)
        await asyncio.sleep(0.1)

    model = LinearRegression(fit_intercept=True)
    model.fit(camera_reference, hip_STL_obj_points)

    # Extract rotation (scaling included) and translation
    R_estimated = model.coef_  # Rotation and scaling
    t_estimated = model.intercept_  # Translation

    # Apply transformation to each point
    for i in camera_reference:
        transformed_point = np.dot(R_estimated, i) + t_estimated
        print(f"point {i}    transformed_point {transformed_point}")
        await asyncio.sleep(0.1)

    frame_limit = 80 if rev is None else 150  # Set limit based on rev

    while framecount < frame_limit:
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])

                first_three_right, next_three_right = (
                    detections_right[:3],
                    detections_right[-3:],
                )
                first_three_left, next_three_left = (
                    detections_left[:3],
                    detections_left[-3:],
                )

                femur = TriangleTracker(
                    tuple(first_three_right),
                    tuple(first_three_left),
                )

                F_points = femur.getLEDcordinates()
                Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                Pointer_tracker = TriangleTracker(
                    tuple(next_three_right),
                    tuple(next_three_left)
                )

                pointer_tip_gcs = Pointer_tracker.getStylusPoint()

                pointRegistered = utils.find_new_point_location(
                    old_plane_points=Tracker_plane,
                    new_plane_points=TP["TP"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                variable_dict[str(framecount)] = {
                    "C": pointRegistered,
                    "Plane": TP["TP"]["Plane"],
                }

                point = np.array(pointRegistered, dtype=np.float32)
                point[1] = -point[1]
                transformed_point = np.dot(R_estimated, point) + t_estimated
                print(f'count {framecount + 1} / {frame_limit} point {point}  transformed_point {transformed_point}')
                # transformed_point = np.dot(transformation_matrix[:3, :3], point) + transformation_matrix[:3, 3]
                await send_tuple(websocket, transformed_point)

                framecount += 1
                # save_progress(variable_dict, framecount, progress_file)
                await asyncio.sleep(0.1)
        except Exception as e:
            print(f'Error {e}')
            # save_progress(variable_dict, framecount, progress_file)
            await asyncio.sleep(0.1)
            return
    print("🎯 All points registered. Saving data...")
    if rev is None:
        final_file = os.path.join(
            current_dir, "..", "..", "registration_data", "PointCloud.pickle"
        )
    else:
        final_file = os.path.join(
            current_dir, "..", "..", "registration_data", "revPointCloud.pickle"
        )
    await send_tuple(websocket, ("exit", 1, 3, 0))
    os.makedirs(os.path.dirname(final_file), exist_ok=True)
    with open(final_file, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
    print("✅ Pickle dump successful.")


async def pointer_femur_pipeline(marking, websocket):
    global framecount, variable_dict
    point_reg = ["APT"]
    framecount = 0
    while framecount < len(point_reg):
        if page not in ['system-setup.html', 'system-setup-2.html']:
            return
        detections_left, detections_right = await marking.handle_irq_average(n=10)

        if len(detections_right) == 6 and len(detections_left) == 6:
            pointer_tracker = TriangleTracker(
                tuple(detections_right[-3:]),
                tuple(detections_left[-3:])
            )
            pointer_tip_gcs = pointer_tracker.getStylusPoint()

            femur_tracker = TriangleTracker(
                tuple(detections_right[:3]),
                tuple(detections_left[:3]),
            )
            F_points = femur_tracker.getLEDcordinates()

            variable_memory = {
                "C": pointer_tip_gcs,
                "Plane": [F_points[2], F_points[1], F_points[0]],
            }
            variable_dict[point_reg[framecount]] = variable_memory

            pickle_path_TP = os.path.join(
                current_dir, "..", "..", "registration_data", "act_variable.pickle"
            )
            os.makedirs(os.path.dirname(pickle_path_TP), exist_ok=True)

            with open(pickle_path_TP, "wb") as handle:
                pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

            print("Pickle dump successful")
            await send_tuple(websocket, (f"{point_reg[framecount]}", 1, 3, 0))
            utils.play_notification_sound()
            framecount += 1
            await asyncio.sleep(0.1)
        else:
            await asyncio.sleep(0.1)
    # else:
    #     await pointer_femur_pipeline(marking, websocket)
    #     print(
    #         f" recursive call for system-setup.html  {len(detections_right)}  {len(detections_left)}"
    #     )


async def stream_video(marking, websocket: WebSocket):
    try:
        while True:
            if page != "video":
                return
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return

            # Get frames from queue

            if rec_img_left.shape[0] != rec_img_right.shape[0]:
                print("⚠️ Image heights do not match. Resizing right image.")
                rec_img_right = cv2.resize(
                    rec_img_right, (rec_img_left.shape[1], rec_img_left.shape[0])
                )
            _, thresh = cv2.threshold(rec_img_left, 200, 255, cv2.THRESH_BINARY)

            # Find contours
            contours, _ = cv2.findContours(
                thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )

            # Extract centroid points
            centroids = []
            for cnt in contours:
                M = cv2.moments(cnt)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    centroids.append([cx, cy])

            centroids = np.array(centroids)
            eps = 92  # Default value if only one object

            # Apply DBSCAN clustering
            if len(centroids) > 0:
                clustering = DBSCAN(eps=eps, min_samples=1).fit(centroids)

                # Get unique cluster labels
                unique_labels = set(clustering.labels_)

                for label in unique_labels:
                    if label == -1:
                        continue  # Ignore noise points

                    # Collect contours belonging to the same group
                    grouped_contours = [
                        contours[i]
                        for i in range(len(clustering.labels_))
                        if clustering.labels_[i] == label
                    ]
                    if len(grouped_contours) == 0:
                        continue  # Avoid empty contour groups

                    # Create bounding box around the group
                    x, y, w, h = cv2.boundingRect(np.vstack(grouped_contours))
                    cv2.rectangle(rec_img_left, (x, y), (x + w, y + h), (0, 255, 0), 2)

                    # Alternative: Draw convex hull
                    hull = cv2.convexHull(np.vstack(grouped_contours))
                    cv2.polylines(
                        rec_img_left,
                        [hull],
                        isClosed=True,
                        color=(255, 0, 0),
                        thickness=2,
                    )

                    # -------- Step to Add Centroid Calculation and Draw a Circle --------
                    # Compute group centroid
                    all_points = np.vstack(grouped_contours)
                    centroid_x = int(np.mean(all_points[:, 0, 0]))  # X-coordinate
                    centroid_y = int(np.mean(all_points[:, 0, 1]))  # Y-coordinate

                    # Draw a filled circle at the centroid
                    # cv2.circle(rec_img_left, (centroid_x, centroid_y), 100, (255, 0, 0), -1)  # Red filled circle
                    triangle_pts = np.array(
                        [
                            [
                                centroid_x,
                                centroid_y + 50,
                            ],  # Bottom vertex (was top before)
                            [centroid_x - 50, centroid_y - 50],  # Top left
                            [centroid_x + 50, centroid_y - 50],  # Top right
                        ],
                        np.int32,
                    )

                    triangle_pts = triangle_pts.reshape(
                        (-1, 1, 2)
                    )  # Reshape for OpenCV

                    # Draw the solid triangle
                    cv2.fillPoly(
                        rec_img_left, [triangle_pts], (255, 255, 0)
                    )  # Red triangle

            # Encode the combined frame
            _, buffer = cv2.imencode(
                ".jpg", rec_img_left, [cv2.IMWRITE_JPEG_QUALITY, 70]
            )
            jpg_as_text = base64.b64encode(buffer).decode()

            # Send frame over WebSocket
            await websocket.send_text(jpg_as_text)

            # Control frame rate
            await asyncio.sleep(0.03)

    except WebSocketDisconnect:
        print("🔌 WebSocket disconnected, stopping video stream.")

    except Exception as e:
        print(f"⚠️ Error in stream_video: {e}")


async def hip_tp_variable_Marking(marking, websocket, point=None):
    global framecount, variable_dict

    point_reg = ["TP2"] if point else ["TP"]
    framecount = 0
    while framecount < len(point_reg):
        detections_left, detections_right = await marking.handle_irq_average(n=10)
        if len(detections_right) == 6 and len(detections_left) == 6:
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            # Extract first 3 and last 3 coordinates
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            next_three_right = detections_right[-3:]
            next_three_left = detections_left[-3:]

            Pointer_traker = TriangleTracker(
                tuple(next_three_right), tuple(next_three_left))

            pointer_tip_gcs = Pointer_traker.getStylusPoint()

            femure = TriangleTracker(
                first_three_right, first_three_left)

            F_points = femure.getLEDcordinates()

            Tracker_plane = [F_points[2], F_points[1], F_points[0]]

            pickle_path_TP = os.path.join(
                current_dir,
                "..",
                "..",
                "registration_data",
                "tp2_variable.pickle" if point else "tp_variable.pickle",
            )
            os.makedirs(os.path.dirname(pickle_path_TP), exist_ok=True)

            variable_memory = {"C": pointer_tip_gcs, "Plane": Tracker_plane}
            variable_dict[point_reg[framecount]] = variable_memory

            with open(pickle_path_TP, "wb") as handle:
                pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

            print(f"✅ Pickle dump successful {point_reg[framecount]}")

            await send_tuple(websocket, (f"{point_reg[framecount]}", 1, 3, 0))
            framecount += 1
            utils.play_notification_sound()
            await asyncio.sleep(0.1)
        else:
            await asyncio.sleep(0.2)


@app.get("/knee/multiple-page2.html")
async def multiple_page2(request: Request):
    return templates.TemplateResponse("/knee/multiple-page2.html", {"request": request})


@app.get("/knee/multiple-page.html")
async def multiple_page(request: Request):
    return templates.TemplateResponse("/knee/multiple-page.html", {"request": request})


@app.get("/knee/tkr-screen-2.html")
async def tkr_screen_2(request: Request):
    return templates.TemplateResponse("/knee/tkr-screen-2.html", {"request": request})


@app.get("/knee/inner-page6.html")
async def inner_page6(request: Request):
    return templates.TemplateResponse("/knee/inner-page6.html", {"request": request})


@app.get("/knee/tkr-screen-3.html")
async def tkr_screen_3(request: Request):
    return templates.TemplateResponse("/knee/tkr-screen-3.html", {"request": request})

@app.get("/revision-knee/tkr-screen-3.html")
async def tkr_screen_3(request: Request):
    return templates.TemplateResponse("/revision-knee/tkr-screen-3.html", {"request": request})


@app.get("/revision-knee/Robot_intoduction.html")
async def tkr_screen_3(request: Request):
    return templates.TemplateResponse("/revision-knee/Robot_intoduction.html", {"request": request})


@app.get("/revision-knee/robot_position.html")
async def tkr_screen_3(request: Request):
    return templates.TemplateResponse("/revision-knee/robot_position.html", {"request": request})


@app.get("/revision-knee/robot_workspace.html")
async def tkr_screen_3(request: Request):
    return templates.TemplateResponse("/revision-knee/robot_workspace.html", {"request": request})


@app.get("/knee/robot-introduction.html")
async def robot_introduction(request: Request):
    return templates.TemplateResponse(
        "/knee/robot-introduction.html", {"request": request}
    )


@app.get("/knee/robot_position.html")
async def robot_position(request: Request):
    return templates.TemplateResponse("/knee/robot_position.html", {"request": request})


@app.get("/knee/robot_workspace.html")
async def robot_workspace(request: Request):
    return templates.TemplateResponse(
        "/knee/robot_workspace.html", {"request": request}
    )


@app.get("/knee/inner-page.html")
async def inner_page(request: Request):
    return templates.TemplateResponse("/knee/inner-page.html", {"request": request})


@app.get("/knee/femur-distal-femur-cut.html")
async def knee_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-distal-femur-cut.html", {"request": request}
    )


@app.get("/knee/femur-inner-page5.html")
async def femur_inner_page5(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-inner-page5.html", {"request": request}
    )

@app.get("/revision-knee/femur-inner-page5.html")
async def femur_inner_page5(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/femur-inner-page5.html", {"request": request}
    )
@app.get("/revision-knee/femur-anterior-resection.html")
async def femur_inner_page5(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/femur-anterior-resection.html", {"request": request}
    )


@app.get("/revision-knee/femur-posterior-resection.html")
async def femur_inner_page5(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/femur-posterior-resection.html", {"request": request}
    )

@app.get("/revision-knee/femur-posterior-chamfer-resection.html")
async def femur_inner_page5(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/femur-posterior-chamfer-resection.html", {"request": request}
    )
@app.get("/revision-knee/femur-anterior-chamfer-resection.html")
async def femur_inner_page5(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/femur-anterior-chamfer-resection.html", {"request": request}
    )


@app.get("/knee/femur-anterior-resection.html")
async def femur_anterior_resection(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-anterior-resection.html", {"request": request}
    )


@app.get("/knee/femur-anterior-chamfer-resection.html")
async def femur_anterior_chamfer_resection(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-anterior-chamfer-resection.html", {"request": request}
    )


@app.get("/knee/femur-posterior-resection.html")
async def femur_posterior_resection(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-posterior-resection.html", {"request": request}
    )


@app.get("/knee/femur-posterior-chamfer-resection.html")
async def femur_posterior_chamfer_resection(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-posterior-chamfer-resection.html", {"request": request}
    )


@app.get("/knee/femur-inner-page2.html")
async def femur_inner_page2(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-inner-page2.html", {"request": request}
    )

@app.get("/revision-knee/femur-inner-page2.html")
async def femur_inner_page2(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/femur-inner-page2.html", {"request": request}
    )


@app.get("/knee/tibia-inner-page3.html")
async def tibia_inner_page3(request: Request):
    return templates.TemplateResponse(
        "/knee/tibia-inner-page3.html", {"request": request}
    )

@app.get("/revision-knee/tibia-inner-page3.html")
async def tibia_inner_page3(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/tibia-inner-page3.html", {"request": request}
    )
@app.get("/revision-knee/femur-distal-femur-cut.html")
async def tibia_inner_page3(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/femur-distal-femur-cut.html", {"request": request}
    )


@app.get("/knee/femur-graph-screen.html")
async def femur_graph_screen(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-graph-screen.html", {"request": request}
    )


@app.get("/knee/Robot_intoduction.html")
async def femur_graph_screen(request: Request):
    return templates.TemplateResponse(
        "/knee/Robot_intoduction.html", {"request": request}
    )


#############################################################################
#######                             Uni Knee                             ####
#############################################################################


@app.get("/uni-knee/multiple-page2.html")
async def uni_multiple_page2(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/multiple-page2.html", {"request": request}
    )


@app.get("/uni-knee/multiple-page.html")
async def uni_multiple_page(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/multiple-page.html", {"request": request}
    )


@app.get("/uni-knee/tkr-screen-2.html")
async def uni_tkr_screen_2(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/tkr-screen-2.html", {"request": request}
    )


@app.get("/uni-knee/inner-page6.html")
async def uni_inner_page6(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/inner-page6.html", {"request": request}
    )


@app.get("/uni-knee/Free_point_collectionTibia.html")
async def point_collectionTibia(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/Free_point_collectionTibia.html", {"request": request}
    )


@app.get("/uni-knee/Free_point_collection_femur.html")
async def point_collection_femur(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/Free_point_collection_femur.html", {"request": request}
    )


@app.get("/uni-knee/accp_femure_cut.html")
async def revisionHipmidAxisPlane(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/accp_femure_cut.html", {"request": request}
    )


@app.get("/uni-knee/femur-distal-femur-cut.html")
async def uni_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/femur-distal-femur-cut.html", {"request": request}
    )


@app.get("/uni-knee/tibia-cut.html")
async def uni_tibia_cut(request: Request):
    return templates.TemplateResponse("/uni-knee/tibia-cut.html", {"request": request})


@app.get("/uni-knee/Final_leg_Alignment.html")
async def unifinal_leg_alignment(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/Final_leg_Alignment.html", {"request": request}
    )


#############################################################################
#######                            Revision Knee                         ####
#############################################################################
@app.get("/revision-knee/multiple-page2.html")
async def rev_multiple_page2(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/multiple-page2.html", {"request": request}
    )


@app.get("/revision-knee/multiple-page.html")
async def rev_multiple_page(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/multiple-page.html", {"request": request}
    )


@app.get("/revision-knee/tkr-screen-2.html")
async def rev_tkr_screen_2(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/tkr-screen-2.html", {"request": request}
    )


@app.get("/revision-knee/inner-page6.html")
async def rev_inner_page6(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/inner-page6.html", {"request": request}
    )


@app.get("/revision-knee/remove-primary-implants.html")
async def rev_remove_primary_implants(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/remove-primary-implants.html", {"request": request}
    )


@app.get("/revision-knee/ml-size-acquisition.html")
async def rev_ml_size_acquisition(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/ml-size-acquisition.html", {"request": request}
    )


@app.get("/revision-knee/Free_point_collectionTibia.html")
async def rev_free_point_collection_tibia(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/Free_point_collectionTibia.html", {"request": request}
    )


@app.get("/revision-knee/Free_point_collection_femur.html")
async def rev_free_point_collection_tibia(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/Free_point_collection_femur.html", {"request": request}
    )


@app.get("/revision-knee/Aori_type.html")
async def rev_aori_type(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/Aori_type.html", {"request": request}
    )


@app.get("/revision-knee/tibia-im-canal-ream.html")
async def rev_tibia_im_canal_ream(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/tibia-im-canal-ream.html", {"request": request}
    )


@app.get("/revision-knee/conflict-message.html")
async def rev_conflict_message(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/conflict-message.html", {"request": request}
    )


@app.get("/revision-knee/conflict-messageFemure.html")
async def rev_conflict_messageF(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/conflict-messageFemure.html", {"request": request}
    )


@app.get("/revision-knee/planning-proximal-tibia-cut.html")
async def rev_planning_proximal_tibia_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/planning-proximal-tibia-cut.html", {"request": request}
    )


@app.get("/revision-knee/guidance-proximal-tibia-cut.html")
async def rev_guidance_proximal_tibia_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/guidance-proximal-tibia-cut.html", {"request": request}
    )


@app.get("/revision-knee/guidance-distal-femur-cutBackup.html")
async def rev_guidance_proximal_tibia_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/guidance-distal-femur-cutBackup.html", {"request": request}
    )


@app.get("/revision-knee/revision-knee-burr.html")
async def rev_revision_knee_burr(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/revision-knee-burr.html", {"request": request}
    )


@app.get("/revision-knee/verification-proximal-tibia-cut.html")
async def rev_verification_proximal_tibia_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/verification-proximal-tibia-cut.html", {"request": request}
    )


@app.get("/revision-knee/femur-im-canal-ream.html")
async def rev_femur_im_canal_ream(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/femur-im-canal-ream.html", {"request": request}
    )


@app.get("/revision-knee/planning-distal-femur-cut.html")
async def rev_planning_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/planning-distal-femur-cut.html", {"request": request}
    )


@app.get("/revision-knee/guidance-distal-femur-cut.html")
async def rev_guidance_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/guidance-distal-femur-cut.html", {"request": request}
    )


@app.get("/revision-knee/revision-knee-burrFemur.html")
async def rev_revision_knee_burr_femur(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/revision-knee-burrFemur.html", {"request": request}
    )


@app.get("/revision-knee/verification-distal-femur-cut.html")
async def rev_verification_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/verification-distal-femur-cut.html", {"request": request}
    )


@app.get("/revision-knee/balance-in-extension.html")
async def rev_balance_in_extension(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/balance-in-extension.html", {"request": request}
    )


@app.get("/revision-knee/balance-in-flexion.html")
async def rev_balance_in_flexion(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/balance-in-flexion.html", {"request": request}
    )


@app.get("/revision-knee/graph-screen.html")
async def rev_graph_screen(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/graph-screen.html", {"request": request}
    )


class Timer:
    """Timer class to track elapsed time."""

    def __init__(self, duration):
        self.start_time = None
        self.duration = duration  # Total duration in seconds

    def start(self):
        """Start the timer."""
        self.start_time = time.time()

    def elapsed(self):
        """Check elapsed time."""
        return time.time() - self.start_time if self.start_time else 0

    def is_time_up(self):
        """Check if time has exceeded the set duration."""
        return self.elapsed() >= self.duration


async def pivoting_procedure(marking, websocket):
    global framecount
    femur_kneeMarkinglist = []
    point_reg = (
            ["HIF"] + [f"HC{i}" for i in range(1, 21)] + ["HC"]
    )  # 21 registration points (excluding HIF)
    first = False
    timer = Timer(30)  # 30-second timer

    # **Independent Timers**
    collection_interval = 30 / 100  # ≈ 0.3 sec per collected point (100 points)
    registration_interval = 30 / len(
        point_reg
    )  # ≈ 1.43 sec per registration point (21 points)

    next_collection_time = None
    next_registration_time = None

    framecount = 0  # Tracks collected points
    reg_count = 0  # Tracks sent registration points

    while framecount < 50 and not timer.is_time_up():
        while True:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 6 and len(detections_left) == 6:
                await asyncio.sleep(0.001)  # Prevent rapid resending
                break
            else:
                await asyncio.sleep(0.002)  # Prevent rapid resending
                print(f"detections {len(detections_right)}  {len(detections_left)}  {page}")
            if page not in [
                "unimultiple-page2.html",
                "revmultiple-page2.html",
                "multiple-page2.html",
            ]:
                print(f'Exiting.. pivoting procedure')
                return
        detections_right = sorted(detections_right, key=lambda detection: detection[0])
        detections_left = sorted(detections_left, key=lambda detection: detection[0])
        femure = TriangleTracker(
            detections_right[:3], detections_left[:3], mode='F'
        )
        F_points = femure.getLEDcordinates()

        centroid = utils.calculate_centroid(F_points)
        point_A = (F_points[1] + F_points[0]) / 2.0
        vector = F_points[2] - point_A
        angle = math.degrees(math.atan2(vector[1], vector[0]))

        if framecount == 0 and not first:
            if angle <= 88:
                print(
                    f"Hold the LEG in a single position at most 90° | Current angle: {angle}"
                )

                centroid = utils.calculate_centroid(F_points)
                femur_kneeMarkinglist.append(centroid)

                await asyncio.sleep(0.1)  # Prevent rapid resending
                continue
            try:
                await send_tuple(websocket, (f"{point_reg[reg_count]}", 0.2, 0.2, 0.2))
                utils.play_notification_sound()
            except Exception as e:
                print(f"⚠️ WebSocket send error: {e}")
            first = True
            timer.start()  # Start the timer
            next_collection_time = timer.elapsed() + collection_interval
            next_registration_time = timer.elapsed() + registration_interval
            print("✅ Timer started. Collecting points...")

        if first:
            elapsed_time = timer.elapsed()

            # **Collect 100 points (every ~0.3 sec)**
            if elapsed_time >= next_collection_time and framecount < 50:
                femur_kneeMarkinglist.append(centroid)
                framecount += 1
                print(f"🟢 Collected point {framecount} at {elapsed_time:.2f}s")
                next_collection_time += collection_interval

            # **Send 21 registration points (every ~1.43 sec)**
            if elapsed_time >= next_registration_time and reg_count < len(point_reg):
                try:
                    await send_tuple(
                        websocket, (f"{point_reg[reg_count]}", 0.2, 0.2, 0.2)
                    )
                    print(
                        f"📌 Sent registration point {point_reg[reg_count]} at {elapsed_time:.2f}s"
                    )
                    reg_count += 1
                    next_registration_time += registration_interval
                except Exception as e:
                    print(f"⚠️ WebSocket send error: {e}")
                await asyncio.sleep(0.1)  # Prevent rapid resending

        if framecount >= 50 or timer.is_time_up():
            marking.recent_results.clear()

            print(
                f"⏹️ Collection completed: {framecount} points in {timer.elapsed():.2f} seconds"
            )
            break

    try:
        await send_tuple(websocket, ("HC", 0.2, 0.2, 0.2))
    except Exception as e:
        print(f"⚠️ Final WebSocket send error: {e}")

    points_all2 = np.array(femur_kneeMarkinglist, dtype=float)
    try:

        def sphere_residuals(p, x, y, z):
            x_c, y_c, z_c, r = p
            return np.sqrt((x - x_c) ** 2 + (y - y_c) ** 2 + (z - z_c) ** 2) - r

        x, y, z = points_all2[:, 0], points_all2[:, 1], points_all2[:, 2]
        p0 = [0, 0, 0, 1]
        result = least_squares(sphere_residuals, p0, args=(x, y, z))
        sphere_center = result.x[:3]
    except Exception as e:
        print(f"ERROR: {e}")
        return

    # Save sphere center
    pickle_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "registration_data", "hip_center.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path), exist_ok=True)
    with open(pickle_path, "wb") as handle:
        pickle.dump(sphere_center, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print(f"🎯 Sphere center: {sphere_center}")
    utils.play_notification_sound()
    return 0


async def Tebia_marking(marking, websocket):
    framecount = 1
    variable_dict = {}
    point_reg = ["TC", "TAP", "MC", "LC", "MM", "LM"]

    try:
        # Common base directory for both files
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")

        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)

        # Tibia TC variables
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_handle:
            tibia_tc = pickle.load(tibia_handle)
        print("✅ Femur variables loaded successfully")
    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading femur variables: {e}")
        return {}

    while framecount < len(point_reg):
        try:

            while True:

                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    break  # Exit loop when both lists have exactly 9 detections
                print(f"{len(detections_right)}   {len(detections_left)}")
                await asyncio.sleep(0.1)  # Wait and retry if condition is not met
            if page not in ["multiple-page.html", "revmultiple-page.html"]:
                return
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]
            if (
                    framecount < 4
                    and len(detections_right) == 6
                    and len(detections_left) == 6
            ):
                # Step 3: Sort remaining by Y-coordinate (descending)
                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)

                # Step 4: Extract next three elements
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]

                # Remove extracted elements to ensure mutual exclusivity
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                # Step 5: Extract last three elements safely
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                Pointer_traker = TriangleTracker(
                    next_three_right, next_three_left)

                if framecount == 1:
                    p_points = Pointer_traker.getLEDcordinates()
                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                # print(f'last_three_right {last_three_right}')
                # print(f'last_three_left {last_three_left}')
                femure = TriangleTracker(
                    first_three_right, first_three_left, mode="F"
                )

                F_points = femure.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                print(f"femur_plane  {femur_plane}")
                tebia = TriangleTracker(
                    last_three_right, last_three_left, mode="T"
                )
                T_Points = tebia.getLEDcordinates()
                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]
                print(f"tibia_plane  {tibia_plane}")
                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tibia_plane[2] = NewFC
                NewTC = utils.find_new_point_location(
                    tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
                )
                tibia_plane[2] = NewTC

                print(f"tibia_plane2  {tibia_plane}")
                pointRegistered = utils.find_new_point_location(
                    tibia_plane, tibia_tc["TC"]["Plane"], pointer_tip_gcs
                )
                # tibia_plane[2] = TC
                if framecount == 1:
                    variable_memory = {
                        "C": (pointRegistered, p_points[2]),
                        "Plane": tibia_tc["TC"]["Plane"],
                    }
                else:
                    variable_memory = {
                        "C": pointRegistered,
                        "Plane": tibia_tc["TC"]["Plane"],
                    }

                variable_dict[point_reg[framecount]] = variable_memory
                p_points = Pointer_traker.getLEDcordinates()
                try:
                    await send_tuple(
                        websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2)
                    )
                    framecount = framecount + 1
                    utils.play_notification_sound()
                except Exception as e:
                    print(f" Error {e}")
            elif (
                    framecount >= 4
                    and len(detections_right) == 6
                    and len(detections_left) == 6
            ):
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                middle_three_right = detections_right[:3]
                middle_three_left = detections_left[:3]
                # Initialize the tracker with sorted points
                Pointer_traker = TriangleTracker(
                    detections_right=last_three_right,
                    detections_left=last_three_left,
                )

                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                femure = TriangleTracker(
                    detections_right=first_three_right,
                    detections_left=first_three_left,
                    mode="F",
                )

                F_points = femure.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]

                tebia = TriangleTracker(
                    middle_three_right, middle_three_left, mode="T"
                )
                T_Points = tebia.getLEDcordinates()
                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]
                print(
                    f"Point registered {point_reg[framecount]} tibia_plane  {tibia_plane}"
                )
                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tibia_plane[2] = NewFC
                NewTC = utils.find_new_point_location(
                    tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
                )
                tibia_plane[2] = NewTC

                pointRegistered = utils.find_new_point_location(
                    tibia_plane, tibia_tc["TC"]["Plane"], pointer_tip_gcs
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": tibia_tc["TC"]["Plane"],
                }

                variable_dict[point_reg[framecount]] = variable_memory

                try:
                    await send_tuple(
                        websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2)
                    )
                    print(
                        f"Point registered {point_reg[framecount]}  pointRegistered{pointRegistered}  pointer_tip_gcs{pointer_tip_gcs}"
                    )
                    utils.play_notification_sound()
                    framecount = framecount + 1
                except Exception as e:
                    print(f" Error {e}")

            else:
                print("no data")

            if framecount == len(point_reg):
                print("All points registered.")

                pickle_path_tebia = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "tibia_variables.pickle",
                )
                os.makedirs(os.path.dirname(pickle_path_tebia), exist_ok=True)
                print(f" variable_dict {variable_dict}")
                with open(pickle_path_tebia, "wb") as handle:
                    pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

                print("Pickle dump successful")
                ANC = (
                              np.array(variable_dict["MM"]["C"])
                              + np.array(variable_dict["LM"]["C"])
                      ) / 2

                direction_vector = np.array(ANC) - np.array(tibia_tc["TC"]["C"])

                # Define the normal vector for the plane
                normal_vector = direction_vector / np.linalg.norm(direction_vector)

                # Define the plane equation coefficients based on the point NewTC
                A, B, C = normal_vector
                D = -np.dot(normal_vector, np.array(tibia_tc["TC"]["C"]))

                # Calculate the distance from NewMC to the plane
                def distance_from_point_to_plane(point, A, B, C, D):
                    point = np.array(point)
                    return abs(
                        A * point[0] + B * point[1] + C * point[2] + D
                    ) / np.sqrt(A ** 2 + B ** 2 + C ** 2)

                distance_to_NewMC = distance_from_point_to_plane(
                    variable_dict["MC"]["C"], A, B, C, D
                )
                distance_to_NewLC = distance_from_point_to_plane(
                    variable_dict["LC"]["C"], A, B, C, D
                )

                if distance_to_NewMC > distance_to_NewLC:
                    Tebia_BoneCut_Point = "LC"
                else:
                    Tebia_BoneCut_Point = "MC"
                Tebia_BoneCut_Point_path = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "tibia_cuttingPoint.txt",
                )
                with open(Tebia_BoneCut_Point_path, "wb") as handle:
                    pickle.dump(
                        Tebia_BoneCut_Point, handle, protocol=pickle.HIGHEST_PROTOCOL
                    )
                break
        except Exception as e:
            print(f"Error {e}")


async def UniTebia_marking(marking, websocket):
    framecount = 3
    variable_dict = {}
    point_reg = ["TC", "AL1", "AL2", "MC", "MP", "MM", "LM"]

    try:
        # Common base directory for both files
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")

        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)

        # Tibia TC variables
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_handle:
            tibia_tc = pickle.load(tibia_handle)
        print("✅ Femur variables loaded successfully")
    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading femur variables: {e}")
        return {}

    while framecount < len(point_reg):
        if page != "unimultiple-page.html":
            return
        try:
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                # Wait until at least 9 detections are available

                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.1)
                    break
                if page != "unimultiple-page.html":
                    return

            # Step 1: Sort by X-coordinate
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            if point_reg[framecount] == "MC" or point_reg[framecount] == "MP":
                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)

                # Step 4: Extract next three elements
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]

                # Remove extracted elements to ensure mutual exclusivity
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                # Step 5: Extract last three elements safely
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                Pointer_traker = TriangleTracker(
                    next_three_right, next_three_left
                )

                pointer_tip_gcs = Pointer_traker.getStylusPoint()

                femure = TriangleTracker(
                    first_three_right, first_three_left, mode='F'
                )

                F_points = femure.getLEDcordinates()

                tebia = TriangleTracker(
                    last_three_right, last_three_left, mode='T'
                )
                T_Points = tebia.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )

                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], NewFC]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], NewFC]
                NewTC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tibia_plane[2] = NewTC
                pointRegistered = utils.find_new_point_location(
                    tibia_plane, tibia_tc["TC"]["Plane"], pointer_tip_gcs
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": tibia_tc["TC"]["Plane"],
                }

                variable_dict[point_reg[framecount]] = variable_memory
                try:
                    await send_tuple(
                        websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2)
                    )
                    print(f"Point registered {point_reg[framecount]}")
                    utils.play_notification_sound()
                except Exception as e:
                    print(f" Error {e}")
                framecount = framecount + 1
            elif ((point_reg[framecount] == "MM" or point_reg[framecount] == "LM") and len(detections_right) == 6
                  and len(detections_left) == 6):
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                middle_three_right = detections_right[:3]
                middle_three_left = detections_left[:3]
                Pointer_traker = TriangleTracker(
                    last_three_right, last_three_left,
                )

                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                femure = TriangleTracker(
                    first_three_right, first_three_left, mode='F'
                )

                F_points = femure.getLEDcordinates()
                tebia = TriangleTracker(
                    middle_three_right, middle_three_left, mode='T'
                )
                T_Points = tebia.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )

                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], NewFC]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], NewFC]
                NewTC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tibia_plane[2] = NewTC
                pointRegistered = utils.find_new_point_location(
                    tibia_plane, tibia_tc["TC"]["Plane"], pointer_tip_gcs
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": tibia_tc["TC"]["Plane"],
                }

                variable_dict[point_reg[framecount]] = variable_memory
                try:
                    await send_tuple(
                        websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2)
                    )
                    print(
                        f"Point registered {point_reg[framecount]}  pointRegistered{pointRegistered}  pointer_tip_gcs{pointer_tip_gcs} tibia_plane{tibia_plane}"
                    )
                    utils.play_notification_sound()
                except Exception as e:
                    print(f" Error {e}")
                framecount = framecount + 1
                # else:
                #     print('no data')

            if framecount == len(point_reg):
                print("All points registered.")

                pickle_path_tebia = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "tibia_variables.pickle",
                )
                os.makedirs(os.path.dirname(pickle_path_tebia), exist_ok=True)
                print(f" variable_dict {variable_dict}")
                with open(pickle_path_tebia, "wb") as handle:
                    pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

                print("Pickle dump successful")
                break
        except Exception as e:
            print(f"Error {e}")


async def uniFree_point_collectionTibia(marking, websocket):
    framecount = 0
    variable_dict = {}

    # Base directory
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")

    files_to_load = {
        # "hip_center": os.path.join(base_dir, 'hip_center.pickle'),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        "tibia_variables": os.path.join(base_dir, "tibia_variables.pickle"),
        "tibia_variables_TC": os.path.join(base_dir, "tibia_variables_TC.pickle"),
    }

    femur_dict = load_pickle_file(
        files_to_load["femur_variables"], "femur variables variables"
    )
    tibia_dict_tc = load_pickle_file(
        files_to_load["tibia_variables_TC"], "Tibia TC variables"
    )
    tibia_dict = load_pickle_file(files_to_load["tibia_variables"], "Tibia variables")

    stFrameInfo = MV_FRAME_OUT_INFO_EX()
    memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
    # print(f'tibia_dict_tc {tibia_dict_tc}')
    camera_reference = np.array(
        [
            tibia_dict_tc["TC"]["C"],
            tibia_dict_tc["AL1"]["C"],
            tibia_dict_tc["AL2"]["C"],
            tibia_dict["MC"]["C"],
            tibia_dict["MP"]["C"],
            tibia_dict["MM"]["C"],
            tibia_dict["LM"]["C"],
        ]
    )
    print(f'camera_reference {camera_reference}')
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_uniTibia = np.array(
        [
            [
                -64.6756134,
                -2.9603553,
                -17.7387486,
            ],  # TC [-60.84293912 -17.69269143   2.21923788]
            [-64.6940308, 0.605574, -17.0766163],  # AL1
            [-65.52034, -5.8293805, -17.9646893],  # AL2
            [-64.2572708, -4.0632067, -14.2518415],  # MC
            [-63.9681778, -5.3495793, -12.3823586],  # MP
            [-0.7417173, -8.5226688, -13.8344755],  # MM
            [-0.2158346, -9.4181175, -22.6858425],  # LM
        ]
    )
    # obj_points_uniTibia[:, [1, 2]] = obj_points_uniTibia[:, [2, 1]]
    for i in obj_points_uniTibia:
        await send_tuple(websocket, i)
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_uniTibia
    )

    points_cloud = []
    while framecount < 100:
        if page != "uniFree_point_collectionTibia.html":
            return
        try:

            detections_left, detections_right = await marking.handle_irq_average(n=10)
            # Wait until at least 9 detections are available

            if len(detections_right) == 9 and len(detections_left) == 9:

                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])
                # Step 2: Extract first three elements
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]

                # Remove extracted elements
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)

                # Step 4: Extract next three elements
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]

                # Remove extracted elements to ensure mutual exclusivity
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                # Step 5: Extract last three elements safely
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                femure = TriangleTracker(
                    first_three_right, first_three_left
                )

                F_points = femure.getLEDcordinates()
                # print(f'femur_plane  {F_points}')
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                Pointer_traker = TriangleTracker(
                    next_three_right, next_three_left
                )

                pointer_tip_gcs = Pointer_traker.getStylusPoint()

                tibia = TriangleTracker(
                    last_three_right, last_three_left
                )

                T_points = tibia.getLEDcordinates()
                # print(f'T_points  {T_points}')
                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                if T_points[1][1] >= T_points[0][1]:
                    tibia_plane = [T_points[2], T_points[0], NewFC]
                else:
                    tibia_plane = [T_points[2], T_points[1], NewFC]
                TC = utils.find_new_point_location(
                    tibia_dict_tc["TC"]["Plane"], tibia_plane, tibia_dict_tc["TC"]["C"]
                )
                tibia_plane[2] = TC
                pointRegistered = utils.find_new_point_location(
                    old_plane_points=tibia_plane,
                    new_plane_points=tibia_dict_tc["TC"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                points_cloud.append(pointRegistered)

                data = f"{pointRegistered[0]},{-pointRegistered[1]},{pointRegistered[2]}\n"

                transformed_point = (
                        np.dot(transformation_matrix[:3, :3], eval(data))
                        + transformation_matrix[:3, 3]
                )

                try:
                    await send_tuple(websocket, transformed_point)
                except Exception as e:
                    print(f"⚠️ WebSocket send failed: {e}")
                framecount += 1  # Move to the next point
                print(f'pointer_tip_gcs {framecount}  {pointer_tip_gcs}')

            else:
                await asyncio.sleep(0.1)  # Delay


        except Exception as e:
            print(f"Error {e}")
    utils.play_notification_sound()
    print("All points registered.")

    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloudtibia.pickle"
    )
    try:
        with open(freePoint_knee_uni, "wb") as handle:
            pickle.dump(
                np.array(points_cloud), handle, protocol=pickle.HIGHEST_PROTOCOL
            )
        print("Pickle dump successful")
    except:
        print("Error loading HC variables")

    try:
        await send_tuple(websocket, ("exit", 0, 0, 0))
    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")


async def uniKneeDistalFemureCut(marking, websocket):
    global condition

    # if not USE_BURR:
    #     return

    # from scipy.spatial import ConvexHull
    def load_pickle_file(file_path, var_name):
        """Load a pickle file and handle exceptions."""
        try:
            with open(file_path, "rb") as handle:
                data = pickle.load(handle)
            # print(f"{var_name} loaded successfully")
            return data
        except FileNotFoundError:
            print(f"Error: File not found - {file_path}")
        except pickle.UnpicklingError:
            print(f"Error: Unable to unpickle data from - {file_path}")
        except Exception as e:
            print(f"Error loading {var_name}: {e}")
        return None
        # Base directory

    base_dir = os.path.join(current_dir, "..", "..", "registration_data")

    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        # "femur_variables": os.path.join(base_dir, 'femur_variables.pickle'),
        # "tibia_variables_TC": os.path.join(base_dir, 'tibia_variables_TC.pickle'),
    }
    # femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")

    # Construct the correct path to the registration_data folder (up one level)
    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloud.pickle"
    )
    try:
        with open(freePoint_knee_uni, "rb") as handle:
            points_cloud = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")
    # Construct the correct path to the registration_data folder (up one level)
    pickle_path_hipCenter = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    try:
        with open(pickle_path_hipCenter, "rb") as handle:
            femure_dict = pickle.load(handle)
            print(f' femure_dict {femure_dict}')
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    points_cloud = np.array(points_cloud)
    # anatomical_points = np.array(
    #     [
    #         # femure_dict['FC']['C'],
    #         femure_dict["TM"]["C"],
    #         femure_dict["TS1"]["C"],
    #         femure_dict["TS2"]["C"],
    #         femure_dict["MDC"]["C"],
    #         femure_dict["MPC"]["C"],
    #         femure_dict["ME"]["C"],
    #     ]
    # )
    # points_cloud = np.concatenate((points_cloud, new_points), axis=0)
    startmotor = False
    # # //GetRoboticArmInitialData(scalling=True)
    # model_path = os.path.join(current_dir, "..", "..", "robotic_calib_data", "robot_to_led_model.pkl") # LED to Robot
    # modelR_path = os.path.join(current_dir, "..", "..", "robotic_calib_data", "led_to_robot_model.pkl") #robot to LED
    # model = joblib.load(model_path)
    # modelR = joblib.load(modelR_path)
    arm = XArmAPI(arm_ip)  # Replace with your xArm6 IP
    initial_data = GetRoboticArmInitialData()

    while True:
        try:
            if page != "unifemur-distal-femur-cut.html":
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if not (len(detections_right) == 9 and len(detections_left)) == 9:
                await asyncio.sleep(0.5)
                continue
            # Step 1: Sort by X-coordinate
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            robot_detection_right = detections_right[:3]
            robot_detection_left = detections_left[:3]

            robot = TriangleTracker(
                robot_detection_right, robot_detection_left
            )
            R_points = robot.getLEDcordinates()
            print(f'R_points   {R_points}')
            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]
            #
            # detections_right.sort(key=lambda x: x[1], reverse=True)
            # detections_left.sort(key=lambda x: x[1], reverse=True)

            # Step 4: Extract next three elements
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]

            # Remove extracted elements to ensure mutual exclusivity
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            # Step 5: Extract last three elements safely
            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]
            femure = TriangleTracker(
                next_three_right,
                next_three_left,
                mode="F",
            )

            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = np.array(
                    [F_points[2], F_points[0], F_points[1]]
                )  # Proper order of points
            else:
                femur_plane = np.array(
                    [F_points[2], F_points[1], F_points[0]]
                )  # Proper order of points

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
            )

            print(
                f" translation_vector {translation_vector} Newtranslated_plane{Newtranslated_plane}"
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )
            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]
            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)

            cut_offset = 0  # mm
            temp_HC = HC.copy()
            temp_HC[1], temp_HC[2] = temp_HC[2], temp_HC[1]
            temp_HC = np.array(temp_HC).reshape(1, -1)
            HC_robot = updated_model.predict(temp_HC)

            FC = utils.find_new_point_location(
                femure_dict["FC"]["Plane"], femur_plane, femure_dict["FC"]["C"]
            )
            MDC = utils.find_new_point_location(
                femure_dict["MDC"]["Plane"], femur_plane, femure_dict["MDC"]["C"]
            )
            temp_FC = FC.copy()
            temp_FC[1], temp_FC[2] = temp_FC[2], temp_FC[1]
            temp_FC = np.array(temp_FC).reshape(1, -1)
            FC_robot = updated_model.predict(temp_FC)

            temp_MDC = MDC
            temp_MDC[1], temp_MDC[2] = temp_MDC[2], temp_MDC[1]
            temp_MDC = np.array(temp_MDC).reshape(1, -1)
            temp_MDC = updated_model.predict(temp_MDC)
            a, b, c = direction_vector = HC_robot.flatten() - FC_robot.flatten()
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            # 4. Project the cut point from FC_robot along this vector
            projection_point = temp_MDC.flatten() + unit_vector * cut_offset

            x0, y0, z0 = projection_point  # Point on the plane
            # Calculate 'd' using the plane equation
            d = -(a * x0 + b * y0 + c * z0)

            position = arm.get_position()
            if position[0] == 0:  # Success check
                x, y, z, roll, pitch, yaw = position[1]

            moving_point = [x, y, z]

            print(f' HC_robot {HC_robot} FC_robot {FC_robot}   FC {FC}')
            print(f' projection_point {projection_point} temp_MDC {temp_MDC}  ')
            print(f' a, b, c {a} {b} {c}  FC_robot {FC_robot}  moving_point {moving_point}')
            print('*' * 100)
            print(f'{utils.distance_3d(FC_robot.flatten(), moving_point)}')
            print('*' * 100)
            continue
            # x1, y1, z1 = FC_robot.flatten()
            # # 2. Set mode and state
            # arm.set_mode(0)  # Mode 0 = position control
            # arm.set_state(0)  # State 0 = ready

            # # 3. Enable motion
            # arm.motion_enable(True)
            # code = arm.set_position(x=x1, y=y1, z=z1, roll=roll, pitch=pitch, yaw=yaw, speed=15, mvacc=1000)

            # if code == 0:
            #     print("Successfully moved to position.")
            # else:
            #     print(f"Failed to move. Error code: {code}")
            # return
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=femure_dict["FC"]["Plane"],
                        new_plane_points=tuple(femur_plane),
                        old_marked_point=led,
                    )
                    for led in points_cloud
                ]
            )

            # Step 3: Transform anatomical points to new plane
            transformed_anatomical_points = np.array([
                utils.find_new_point_location(
                    old_plane_points=femure_dict["FC"]["Plane"],
                    new_plane_points=tuple(femur_plane),
                    old_marked_point=pt,
                )
                for pt in anatomical_points
            ])

            all_transformed_points = np.vstack([transformed_led_points, transformed_anatomical_points])

            temp_points = np.array([
                [p[0], p[2], p[1]]  # Swap Y and Z
                for p in all_transformed_points
            ])

            # Step 2: Reshape and predict
            predicted_robot_coords = updated_model.predict(temp_points)  # Shape: (N, 3)

            # Concatenate the projection point to the points_cloud
            points_cloud = np.concatenate((predicted_robot_coords, HC[np.newaxis, :]), axis=0)

            hull = ConvexHull(points_cloud)
            moving_point_distance = np.dot(moving_point, np.array([a, b, c])) - d

            def point_in_hull(point, hull):
                new_points = np.vstack([hull.points, point])
                new_hull = ConvexHull(new_points)
                return np.array_equal(new_hull.vertices, hull.vertices)

            if point_in_hull(moving_point, hull):
                startmotor = True
                if moving_point_distance <= 0:
                    condition = 1
                    print(f"moving_point_distance {moving_point_distance} start motor")
                    await send_tuple(websocket, ("GREEN", 11, 5.6, 7.3))
                    # start_motor()

                if moving_point_distance >= 0.1:
                    startmotor = False
                    print(f"moving_point_distance {moving_point_distance} stop motor")
                    await send_tuple(websocket, ("RED", 11, 5.6, 7.3))
                    condition = 0
                    # stop_motor()

            else:
                condition = 2
                startmotor = False
                # if is_outside_convex_hull(moving_point, hull_points):
                #     print("Pointer is outside the convex hull")
                # else:
                # print("Pointer is inside or on the surface of the convex hull")
                await send_tuple(websocket, ("RED", 11, 5.6, 7.3))
                print(f"moving_point_distance {moving_point_distance} stop motor")
                # stop_motor()
        except Exception as e:
            print(f"Error {e}")
            return


async def uniKneeDistalFemureCutBackup(marking, websocket):
    global condition
    if not USE_BURR:
        return

    # from scipy.spatial import ConvexHull
    def load_pickle_file(file_path, var_name):
        """Load a pickle file and handle exceptions."""
        try:
            with open(file_path, "rb") as handle:
                data = pickle.load(handle)
            # print(f"{var_name} loaded successfully")
            return data
        except FileNotFoundError:
            print(f"Error: File not found - {file_path}")
        except pickle.UnpicklingError:
            print(f"Error: Unable to unpickle data from - {file_path}")
        except Exception as e:
            print(f"Error loading {var_name}: {e}")
        return None
        # Base directory

    base_dir = os.path.join(current_dir, "..", "..", "registration_data")

    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        # "femur_variables": os.path.join(base_dir, 'femur_variables.pickle'),
        # "tibia_variables_TC": os.path.join(base_dir, 'tibia_variables_TC.pickle'),
    }
    # femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")

    # Construct the correct path to the registration_data folder (up one level)
    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloud.pickle"
    )
    try:
        with open(freePoint_knee_uni, "rb") as handle:
            points_cloud = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")
    # Construct the correct path to the registration_data folder (up one level)
    pickle_path_hipCenter = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    try:
        with open(pickle_path_hipCenter, "rb") as handle:
            femure_dict = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    points_cloud = np.array(points_cloud)
    new_points = np.array(
        [
            # femure_dict['FC']['C'],
            femure_dict["TM"]["C"],
            femure_dict["TS1"]["C"],
            femure_dict["TS2"]["C"],
            femure_dict["MDC"]["C"],
            femure_dict["MPC"]["C"],
            femure_dict["ME"]["C"],
        ]
    )
    points_cloud = np.concatenate((points_cloud, new_points), axis=0)
    startmotor = False
    while True:
        try:
            if page != "unifemur-distal-femur-cut.html":
                stop_motor()
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if not (len(detections_right) == 9 and len(detections_left)) == 9:
                await asyncio.sleep(0.5)
                # stop_motor()
                continue
            # Step 1: Sort by X-coordinate
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)

            # Step 4: Extract next three elements
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]

            # Remove extracted elements to ensure mutual exclusivity
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            # Step 5: Extract last three elements safely
            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]
            femure = TriangleTracker(
                first_three_right,
                first_three_left,
                mode="F",
            )

            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = np.array(
                    [F_points[2], F_points[0], F_points[1]]
                )  # Proper order of points
            else:
                femur_plane = np.array(
                    [F_points[2], F_points[1], F_points[0]]
                )  # Proper order of points
            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left
            )

            P_point = Pointer_traker.getLEDcordinates()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()
            # pointer_tip = Pointer_traker.getStylusPointBurr()
            direction_vector = HC - femure_dict["FC"]["C"]
            print(f'P_point {P_point}')
            tip_point = calculate_contact_point(
                led1=P_point[0],
                led2=P_point[1],
                led3=P_point[2],
                surface_normal=direction_vector,
                point=pointer_tip_gcs,
                radius=3.14,
            )

            pointRegistered = utils.find_new_point_location(
                old_plane_points=femur_plane,
                new_plane_points=femure_dict["FC"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )

            tip_pointRegistered = utils.find_new_point_location(
                old_plane_points=femur_plane,
                new_plane_points=femure_dict["FC"]["Plane"],
                old_marked_point=tip_point,
            )

            a = distance(F_points[0], F_points[1])
            b = distance(F_points[1], F_points[2])
            c = distance(F_points[2], F_points[0])
            # Identify the unique (base) and repeated (equal legs) lengths
            sides = [a, b, c]
            sides_rounded = np.round(sides, decimals=5)  # for floating point stability
            counted = Counter(sides_rounded)
            leg_length, base_length = 0, 0
            for length, count in counted.items():
                if count == 2:
                    leg_length = length
                elif count == 1:
                    base_length = length
            real_base_length = 64  # for example, 50 mm in real world
            # Calculate scale factor (real-world units per unit in camera space)
            scale = real_base_length / base_length

            a, b, c = direction_vector  # Normal vector

            B = np.array(femur_plane[2])
            C = np.array(femur_plane[1])
            distance1 = utils.distance_3d(B, C)
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = 15 / scale1
            print(
                f'pointRegistered {pointRegistered} tip_pointRegistered {tip_pointRegistered} scale {scale} pointer_tip_gcs {np.linalg.norm(tip_pointRegistered - pointRegistered)}')

            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            projection_point = femure_dict["MDC"]["C"] + scaled_unit_vector

            x0, y0, z0 = projection_point  # Point on the plane
            # Calculate 'd' using the plane equation
            d = -(a * x0 + b * y0 + c * z0)
            moving_point = np.array(
                pointRegistered
            )  # Replace with your actual moving point
            # Concatenate the projection point to the points_cloud
            points_cloud = np.concatenate((points_cloud, HC[np.newaxis, :]), axis=0)
            hull = ConvexHull(points_cloud)
            moving_point_distance = np.dot(moving_point, np.array([a, b, c])) + d

            def point_in_hull(point, hull):
                new_points = np.vstack([hull.points, point])
                new_hull = ConvexHull(new_points)
                return np.array_equal(new_hull.vertices, hull.vertices)

            if point_in_hull(moving_point, hull):
                startmotor = True
                if moving_point_distance <= 0:
                    condition = 1
                    print(f"moving_point_distance {moving_point_distance} start motor")
                    await send_tuple(websocket, ("GREEN", 11, 5.6, 7.3))
                    start_motor()

                if moving_point_distance >= 0.1:
                    startmotor = False
                    print(f"moving_point_distance {moving_point_distance} stop motor")
                    await send_tuple(websocket, ("RED", 11, 5.6, 7.3))
                    condition = 0
                    stop_motor()

            else:
                condition = 2
                startmotor = False
                # if is_outside_convex_hull(moving_point, hull_points):
                #     print("Pointer is outside the convex hull")
                # else:
                # print("Pointer is inside or on the surface of the convex hull")
                await send_tuple(websocket, ("RED", 11, 5.6, 7.3))
                print(f"moving_point_distance {moving_point_distance} stop motor")
                stop_motor()
        except Exception as e:
            print(f"Error {e}")
            return


def angle_between_vectors(v1, v2):
    if len(v1) != len(v2):
        raise ValueError("Vectors must be of the same dimension")

    # Compute dot product and magnitudes
    dot = sum(a * b for a, b in zip(v1, v2))
    mag1 = math.sqrt(sum(a ** 2 for a in v1))
    mag2 = math.sqrt(sum(b ** 2 for b in v2))

    if mag1 == 0 or mag2 == 0:
        raise ValueError("Cannot calculate angle with zero-length vector")

    # Clamp dot / (mag1 * mag2) to [-1, 1] to avoid domain errors in acos
    cos_theta = max(-1.0, min(1.0, dot / (mag1 * mag2)))
    angle_rad = math.acos(cos_theta)
    angle_deg = math.degrees(angle_rad)

    return angle_deg


async def accp_femure_cut(marking, websocket):
    # data=['RED', 'GREEN', 'INIT']
    # while True:
    #     for i in range(0,3):
    #         await asyncio.sleep(2)
    #         var = {
    #             "abbreviation": data[i],
    #             "anteversion": 20,
    #             "inclination": 40,
    #         }
    #         await send_json(websocket, var)
    #
    pickle_path_hipCenter = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    try:
        with open(pickle_path_hipCenter, "rb") as handle:
            femure_dict = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    ValidMDCpoint = False
    while True:
        try:
            if page != "uniaccp_femure_cut.html":
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            print(f'{len(detections_left)}  {len(detections_right)} ')
            if len(detections_right) == 9 and len(detections_left) == 9:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])
                # Step 2: Extract first three elements
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]

                # Remove extracted elements
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)

                # Step 4: Extract next three elements
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]

                # Remove extracted elements to ensure mutual exclusivity
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                # Step 5: Extract last three elements safely
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                femure = TriangleTracker(
                    first_three_right, first_three_left
                )

                F_points = femure.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                Pointer_traker = TriangleTracker(
                    next_three_right, next_three_left
                )

                P_point = Pointer_traker.getLEDcordinates()
                pointer_tip_gcs = Pointer_traker.getStylusPoint()

                # tip_point = calculate_contact_point(led1=P_point[0], led2=P_point[1], led3=P_point[3], center_point=, point=pointer_tip_gcs, radius=6.28)
                # pointRegistered = utils.find_new_point_location(
                #     old_plane_points=femur_plane,
                #     new_plane_points=femure_dict["FC"]["Plane"],
                #     old_marked_point=tip_point,
                # )

                # points_cloud.append(pointRegistered)

                def load_pickle_file(file_path, var_name):
                    """Load a pickle file and handle exceptions."""
                    try:
                        with open(file_path, "rb") as handle:
                            data = pickle.load(handle)
                        # print(f"{var_name} loaded successfully")
                        return data
                    except FileNotFoundError:
                        print(f"Error: File not found - {file_path}")
                    except pickle.UnpicklingError:
                        print(f"Error: Unable to unpickle data from - {file_path}")
                    except Exception as e:
                        print(f"Error loading {var_name}: {e}")
                    return None

                # Base directory
                base_dir = os.path.join(current_dir, "..", "..", "registration_data")

                # File paths and variable names
                files_to_load = {
                    "hip_center": os.path.join(base_dir, "hip_center.pickle"),
                    "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
                    "tibia_variables_TC": os.path.join(
                        base_dir, "tibia_variables_TC.pickle"
                    ),
                }
                femur_dict = load_pickle_file(
                    files_to_load["femur_variables"], "Femur variables"
                )
                HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")

                FC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                MDC = utils.find_new_point_location(
                    femur_dict["MDC"]["Plane"], femur_plane, femur_dict["MDC"]["C"]
                )
                TS1 = utils.find_new_point_location(
                    femur_dict["TS1"]["Plane"], femur_plane, femur_dict["TS1"]["C"]
                )
                TS2 = utils.find_new_point_location(
                    femur_dict["TS2"]["Plane"], femur_plane, femur_dict["TS2"]["C"]
                )

                # print(f'Fc{FC} MDC {MDC} {pointer_tip_gcs}')
                # Step 1: Calculate the direction vectors (X-axis and Y-axis)
                X_vector = FC - HC  # Vector from HC to FC (X-axis)
                Y_vector = TS2 - TS1  # Vector from TS1 to TS2 (Y-axis)

                # Step 2: Normalize the vectors
                bon_X_unit = X_vector / np.linalg.norm(
                    X_vector
                )  # Normalized X-axis vector
                bon_Y_unit = Y_vector / np.linalg.norm(
                    Y_vector
                )  # Normalized Y-axis vector

                # Step 3: Calculate the Z-axis using the cross product (perpendicular to X and Y)
                Z_unit = np.cross(
                    bon_X_unit, bon_Y_unit
                )  # Z-axis vector (perpendicular to both X and Y)
                bon_Z_unit = Z_unit / np.linalg.norm(Z_unit)  # Normalize Z-axis vector

                # Step 1: Calculate the midpoint between P_point[0] and P_point[1]
                mid = (P_point[0] + P_point[1]) / 2

                # Step 2: Define the X-axis (from mid to P_point[2])
                X_vector_tracker = P_point[2] - mid

                # Step 3: Define the Y-axis (from P_point[0] to P_point[1])
                Y_vector = P_point[1] - P_point[0]

                trackerY_unit = Y_vector / np.linalg.norm(
                    Y_vector
                )  # Normalized Y-axis vector

                # B = np.array(P_point[0])
                # C = np.array(P_point[1])
                #
                # # Calculate the distance between B and C
                # distance1 = np.linalg.norm(B - C)
                #
                # # Scale factor based on real-life reference distance
                # scale1 = (
                #         107 / distance1
                # )  # 68mm is the real-life reference distance between B and C

                # Calculate the distance between MDC and pointer_tip_gcs
                distance_Mdc = np.linalg.norm(
                    MDC - pointer_tip_gcs
                )  # (MDC, pointer_tip_gcs)

                color = "RED"
                # Check if the distance is within the threshold
                if distance_Mdc <= 0.2:
                    print(
                        f"distance {distance_Mdc} MDC point is valid generating green Flag"
                    )
                    ValidMDCpoint = True
                    color = "GREEN"

                else:
                    ValidMDCpoint = False
                    # stop_motor()
                if ValidMDCpoint:
                    ang1, ang2 = utils.calculate_varus_flexion_angles(
                        trackerY_unit, bon_X_unit, bon_Z_unit
                    )
                    print(f" Ang1{ang1} Ang2 {ang2}\n")
                    data = [int(ang1), int(ang2), 0]
                    # utils.SendDataToFrontEnd(data)

                    angle = 180 - angle_between_vectors(X_vector, X_vector_tracker)
                    print(f'angle  ************** {angle}')
                    if angle <= 10:

                        data = [
                            int(ang1),
                            int(ang2),
                            1,
                        ]  # Marking it as out of safe zone
                        print("Angles are inside  the safe zone:}")

                        var = {
                            "abbreviation": color,
                            "anteversion": int(ang1),
                            "inclination": int(ang2),
                        }
                        await send_json(websocket, var)

                        # start_motor()
                        # data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]},{condition}\n"
                        # self.point_queue.put(data)
                        print(f"data {data}")
                    else:
                        data = [
                            int(ang1),
                            int(ang2),
                            1,
                        ]  # Marking it as out of safe zone
                        print("Angles are outside the safe zone:}")
                        color = "RED"
                    var = {
                        "abbreviation": color,
                        "anteversion": int(ang1),
                        "inclination": int(ang2),
                    }
                    await send_json(websocket, var)
                else:
                    var = {
                        "abbreviation": 'RED',
                        "anteversion": 0,
                        "inclination": 0,
                    }
                    await send_json(websocket, var)
            else:
                await asyncio.sleep(0.05)
        except Exception as e:
            print(f"ERROR {e}")


async def unitibia_cut(marking, websocket):
    # if not USE_BURR:
    #     return;
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")
    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        "tibia_variables_TC": os.path.join(base_dir, "tibia_variables_TC.pickle"),
        "tibia_variables": os.path.join(base_dir, "tibia_variables.pickle"),
    }
    # femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")
    femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    tibia_dict_tc = load_pickle_file(
        files_to_load["tibia_variables_TC"], "Tibia TC variables"
    )
    tibia_dict = load_pickle_file(files_to_load["tibia_variables"], "Tibia variables")

    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloudtibia.pickle"
    )
    try:
        with open(freePoint_knee_uni, "rb") as handle:
            points_cloud = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")
    arm = XArmAPI(arm_ip)  # Replace with your xArm6 IP
    initial_data = GetRoboticArmInitialData()

    while True:
        try:
            if page != "unitibia-cut.html":
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 9 and len(detections_left) == 9:
                # Step 1: Sort by X-coordinate
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])
                robot_detection_right = detections_right[:3]
                robot_detection_left = detections_left[:3]

                robot = TriangleTracker(
                    robot_detection_right, robot_detection_left
                )
                R_points = robot.getLEDcordinates()
                print(f'R_points   {R_points}')
                if R_points[1][1] >= R_points[0][1]:
                    tracker_plane = [R_points[2], R_points[0], R_points[1]]
                else:
                    tracker_plane = [R_points[2], R_points[1], R_points[0]]

                # Remove extracted elements
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                # detections_right.sort(key=lambda x: x[1], reverse=True)
                # detections_left.sort(key=lambda x: x[1], reverse=True)

                # Step 4: Extract next three elements
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]

                # Remove extracted elements to ensure mutual exclusivity
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                # Step 5: Extract last three elements safely
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]
                femure = TriangleTracker(
                    next_three_right, next_three_left, mode='F'
                )

                F_points = femure.getLEDcordinates()
                print(f'femur_plane  {F_points}')
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                print(f'femur_plane {femur_plane}')

                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tibia = TriangleTracker(
                    last_three_right, last_three_left
                )

                T_Points = tibia.getLEDcordinates()
                print(f'T_Points {T_Points}')
                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]

                tibia_plane[2] = NewFC
                NewTC = utils.find_new_point_location(
                    tibia_dict_tc["TC"]["Plane"], tibia_plane, tibia_dict_tc["TC"]["C"]
                )

                AL1 = utils.find_new_point_location(
                    tibia_dict_tc["AL1"]["Plane"], tibia_plane, tibia_dict_tc["AL1"]["C"]
                )

                AL2 = utils.find_new_point_location(
                    tibia_dict_tc["AL2"]["Plane"], tibia_plane, tibia_dict_tc["AL2"]["C"]
                )

                tibia_plane[2] = NewTC

                MM = utils.find_new_point_location(
                    tibia_dict["MM"]["Plane"], tibia_plane, tibia_dict["MM"]["C"]
                )
                print('*' * 50)
                print(f'{MM}')
                print('*' * 50)
                LM = utils.find_new_point_location(
                    tibia_dict["LM"]["Plane"], tibia_plane, tibia_dict["LM"]["C"]
                )
                MC = utils.find_new_point_location(
                    tibia_dict["MC"]["Plane"], tibia_plane, tibia_dict["MC"]["C"]
                )
                MP = utils.find_new_point_location(
                    tibia_dict["MP"]["Plane"], tibia_plane, tibia_dict["MP"]["C"]
                )
                ANC = (MM + LM) / 2

                new_origin = utils.find_new_point_location(
                    old_plane_points=initial_data["robot_tracker_plane"],
                    new_plane_points=tuple(tracker_plane),
                    old_marked_point=initial_data["old_mark_point"],
                )

                # Step 1: Compute centroid
                centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
                # R_points
                # Step 2: Compute translation vector
                translation_vector = new_origin - centroid

                # Step 3: Translate the plane
                Newtranslated_plane = (
                        initial_data["robot_tracker_plane"] + translation_vector
                )

                print(
                    f" translation_vector {translation_vector} Newtranslated_plane{Newtranslated_plane}"
                )

                robot_calib_data = os.path.join(
                    current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
                )
                led_points, robot_points = load_data(file_path=robot_calib_data)
                transformed_led_points = np.array(
                    [
                        utils.find_new_point_location(
                            old_plane_points=initial_data["translated_plane"],
                            new_plane_points=tuple(Newtranslated_plane),
                            old_marked_point=led,
                        )
                        for led in led_points
                    ]
                )
                # Swap Y and Z back to match coordinate system
                transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]
                updated_model = LinearRegression()
                updated_model.fit(transformed_led_points, robot_points)

                points_cloud = np.array(points_cloud)

                new_points = np.array(
                    [
                        NewTC,
                        AL1,
                        AL2,
                        MC,
                        MP,
                        MM,
                        LM,
                    ]
                )

                robot_points = []
                for pt in new_points:
                    temp = pt.copy()
                    temp[1], temp[2] = temp[2], temp[1]  # Swap Y and Z
                    temp = np.array(temp).reshape(1, -1)
                    predicted = updated_model.predict(temp)
                    robot_points.append(predicted)

                robot_points = np.vstack(robot_points)  # Shape: (N, features)

                cut_offset = 0  # mm

                tempTC = NewTC.copy()
                tempTC[1], tempTC[2] = tempTC[2], tempTC[1]
                tempTC = np.array(tempTC).reshape(1, -1)
                TC_robot = updated_model.predict(tempTC)

                tempANC = ANC.copy()
                tempANC[1], tempANC[2] = tempANC[2], tempANC[1]
                tempANC = np.array(tempANC).reshape(1, -1)
                ANC_robot = updated_model.predict(tempANC)

                tempMC = MC.copy()
                tempMC[1], tempMC[2] = tempMC[2], tempMC[1]
                tempMC = np.array(tempMC).reshape(1, -1)
                MC_robot = updated_model.predict(tempMC)

                print(f'TC_robot {TC_robot.flatten()}   ANC_robot {ANC_robot.flatten()}')
                a, b, c = direction_vector = TC_robot.flatten() - ANC_robot.flatten()
                unit_vector = direction_vector / np.linalg.norm(direction_vector)
                projection_point = MC_robot.flatten() + unit_vector * cut_offset

                print(f'direction_vector {direction_vector}')

                x0, y0, z0 = projection_point  # Point on the plane
                # Calculate 'd' using the plane equation
                d = -(a * x0 + b * y0 + c * z0)

                position = arm.get_position()
                if position[0] == 0:  # Success check
                    x, y, z, roll, pitch, yaw = position[1]

                moving_point = [x, y, z]

                print(f' TC_robot {TC_robot} ANC_robot {ANC_robot}   TC {NewTC}  ANC {ANC}')
                print(f' projection_point_robot {projection_point} moving_point {moving_point}  ')
                moving_point_distance = utils.distance_3d(projection_point, np.array(moving_point))
                print('*' * 100)
                print(f'{moving_point_distance}')
                print('*' * 100)
                # continue
                hull = ConvexHull(robot_points)

                def point_in_hull(point, hull):
                    new_points = np.vstack([hull.points, point])
                    new_hull = ConvexHull(new_points)
                    return np.array_equal(new_hull.vertices, hull.vertices)

                if point_in_hull(moving_point, hull):
                    startmotor = True
                    await send_tuple(websocket, ("GREEN", 11, 5.6, 7.3))
                    if moving_point_distance >= 0:
                        print(f"moving_point_distance {moving_point_distance} startmotor")
                        start_motor()
                    if moving_point_distance <= 0.1:
                        await send_tuple(websocket, ("YELLOW", 11, 5.6, 7.3))
                        startmotor = False
                        print(f"moving_point_distance {moving_point_distance} stopmotor")
                        stop_motor()
                else:
                    print("Pointer is outside the convex hull")
                await asyncio.sleep(0.05)
            else:
                await asyncio.sleep(0.05)
                await send_tuple(websocket, ("RED", 11, 5.6, 7.3))
                startmotor = False
                # if is_outside_convex_hull(moving_point, hull_points):
                #     print("Pointer is outside the convex hull")
                # else:
                # print("Pointer is inside or on the surface of the convex hull")
                # print(f"moving_point_distance {moving_point_distance} stopmotor ")
                # stop_motor()

        except Exception as e:
            print(f"ERROR {e}")


async def unitkr_screen_2(marking, websocket):
    try:
        utils.SendDataToFrontEnd(0.0)
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
    except:
        print("Error loading femur variables")
        return -1

    while True:
        try:
            if page != "unitkr-screen-2.html":
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) != 9 and len(detections_left) != 9:
                await asyncio.sleep(0.05)
                continue
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]
            detections_right = sorted(
                detections_right, key=lambda x: x[1], reverse=True
            )
            detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            femure = TriangleTracker(
                next_three_right,
                next_three_left,
                mode="F",
            )

            F_points = femure.getLEDcordinates()
            Pointer_traker = TriangleTracker(
                first_three_right,
                first_three_left,
            )

            tip = Pointer_traker.getStylusPoint()

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            a = distance(F_points[0], F_points[1])
            b = distance(F_points[1], F_points[2])
            c = distance(F_points[2], F_points[0])

            # Identify the unique (base) and repeated (equal legs) lengths
            sides = [a, b, c]
            sides_rounded = np.round(sides, decimals=5)  # for floating point stability

            counted = Counter(sides_rounded)
            leg_length, base_length = 0, 0
            for length, count in counted.items():
                if count == 2:
                    leg_length = length
                elif count == 1:
                    base_length = length

            real_base_length = 64  # for example, 50 mm in real world

            # # Calculate scale factor (real-world units per unit in camera space)
            scale = real_base_length / base_length

            MDC = utils.find_new_point_location(
                femur_dict["MDC"]["Plane"], femur_plane, femur_dict["MDC"]["C"]
            )
            MPC = utils.find_new_point_location(
                femur_dict["MPC"]["Plane"], femur_plane, femur_dict["MPC"]["C"]
            )

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            TS1 = utils.find_new_point_location(
                femur_dict["TS1"]["Plane"], femur_plane, femur_dict["TS1"]["C"]
            )
            TS2 = utils.find_new_point_location(
                femur_dict["TS2"]["Plane"], femur_plane, femur_dict["TS2"]["C"]
            )

            def round_to_two_decimals(value):
                return round(value, 2)

            # Compute all distances and store them with labels
            distances = {
                "MDC": utils.distance_3d(MDC, tip) * scale,
                "MPC": utils.distance_3d(MPC, tip) * scale,
                "TS1": utils.distance_3d(TS1, tip) * scale,
                "TS2": utils.distance_3d(TS2, tip) * scale,
                "FC": utils.distance_3d(FC, tip) * scale
            }
            print(f'distances {distances}')
            # Print MDC details for debugging
            print(f" MDC {MDC} {tip} distace_Mdc {distances['MDC']}  scale1 {scale}")

            # Find the closest point
            closest_point, closest_distance = min(distances.items(), key=lambda x: x[1])

            # Compare with the threshold
            DISTANCE_CONSTANT = 0.5 / scale
            rounded_closest = round_to_two_decimals(closest_distance)
            rounded_threshold = round_to_two_decimals(DISTANCE_CONSTANT)

            if rounded_closest <= rounded_threshold:
                print(f"distance {closest_distance} {closest_point} point is valid generating green Flag")
            else:
                print(f"distance {closest_distance} {closest_point} point is INvalid generating red Flag")

            # Send result
            await send_tuple(websocket, (rounded_closest, 0, 0, 0))

        except Exception as e:
            print(f"Error in {inspect.currentframe().f_code.co_name}: {e}")


async def AlignmentAnglesUni(marking, websocket):
    try:
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        with open(pickle_path_femur_hc, "rb") as handle:
            HC = np.array(pickle.load(handle))
        print("Femur HC variables loaded successfully")

        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_tc_handle:
            tibia_tc = pickle.load(tibia_tc_handle)
        pickle_path_tibia = os.path.join(base_dir, "tibia_variables.pickle")
        with open(pickle_path_tibia, "rb") as tibia_handle:
            tibia_dict = pickle.load(tibia_handle)
    except Exception as e:
        print(f"Error loading femur variables: {e}")
        return -1

    while True:
        try:
            if page not in ["uniinner-page6.html", "uniFinal_leg_Alignment.html"]:
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right = sorted(
                    detections_right, key=lambda detection: detection[0]
                )
                detections_left = sorted(
                    detections_left, key=lambda detection: detection[0]
                )
                femure = TriangleTracker(
                    detections_right[:3],
                    detections_left[:3],
                    mode="F",
                )

                F_points = femure.getLEDcordinates()

                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]

                FC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tebia = TriangleTracker(
                    detections_right[3:],
                    detections_left[3:],
                    mode="T",
                )
                T_Points = tebia.getLEDcordinates()

                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]

                tibia_plane[2] = FC
                TC = utils.find_new_point_location(
                    tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
                )
                tibia_plane[2] = TC
                MM = utils.find_new_point_location(
                    tibia_dict["MM"]["Plane"], tibia_plane, tibia_dict["MM"]["C"]
                )
                LM = utils.find_new_point_location(
                    tibia_dict["LM"]["Plane"], tibia_plane, tibia_dict["LM"]["C"]
                )

                ANC = (np.array(MM) + np.array(LM)) / 2

                def vector2_signed_angle(A, B):
                    AProjXZ = np.array([A[0], A[2]])
                    BProjXZ = np.array([B[0], B[2]])
                    angle = np.degrees(
                        np.arctan2(np.cross(AProjXZ, BProjXZ), np.dot(AProjXZ, BProjXZ))
                    )
                    return np.round(angle * 2) / 2

                def vector2_signed_angle_xy(A, B):
                    AProjXY = np.array([A[0], A[1]])
                    BProjXY = np.array([B[0], B[1]])
                    angle = np.degrees(
                        np.arctan2(
                            np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2],
                            np.dot(AProjXY, BProjXY),
                        )
                    )
                    return np.round(angle * 2) / 2

                vectorF = HC - FC
                vectorT = TC - ANC
                ang = vector2_signed_angle(vectorF, vectorT)
                ROM = vector2_signed_angle_xy(vectorF, vectorT)
                print(f"******* {ang}  {ROM}")
                try:
                    await send_tuple(websocket, (abs(int(ang)), abs(int(ROM)), 0.2, 0.2))
                except Exception as e:
                    print(f"⚠️ Error sending via websocket: {e}")
                    break  # Break on send failure (likely closed connection)
            else:
                await asyncio.sleep(0.05)

        except Exception as e:
            print(f"Error in {inspect.currentframe().f_code.co_name}: {e}")
            break  # Optional: Break on unexpected errors (to avoid infinite loop on error)


async def Tebia_marking_tc(marking, websocket):
    framecount = 0
    variable_dict = {}
    point_reg = ["TC"]

    try:
        pickle_path_femur = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_femur, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur variables loaded successfully")
    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading femur variables: {e}")
        return {}

    while framecount < len(point_reg):
        try:
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    break
                await asyncio.sleep(0.1)
                if page not in ["multiple-page.html", "revmultiple-page.html" ]:
                    return

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            detections_right = sorted(
                detections_right[3:], key=lambda x: x[1], reverse=True
            )
            detections_left = sorted(
                detections_left[3:], key=lambda x: x[1], reverse=True
            )

            next_three_right = sorted(detections_right[:3], key=lambda x: x[0])
            next_three_left = sorted(detections_left[:3], key=lambda x: x[0])
            last_three_right = sorted(detections_right[-3:], key=lambda x: x[0])
            last_three_left = sorted(detections_left[-3:], key=lambda x: x[0])

            Pointer_tracker = TriangleTracker(
                next_three_right, next_three_left
            )

            pointer_tip_gcs = Pointer_tracker.getStylusPoint()
            femur = TriangleTracker(
                first_three_right,
                first_three_left,
                mode="F",
            )

            F_points = femur.getLEDcordinates()
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )

            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tibia = TriangleTracker(
                last_three_right, last_three_left, "T"
            )

            T_Points = tibia.getLEDcordinates()
            tibia_plane = (
                [T_Points[2], T_Points[0], NewFC]
                if T_Points[1][1] >= T_Points[0][1]
                else [T_Points[2], T_Points[1], NewFC]
            )
            variable_memory = {"C": pointer_tip_gcs, "Plane": tibia_plane}
            variable_dict[point_reg[framecount]] = variable_memory
            try:
                await send_tuple(websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2))
                utils.play_notification_sound()
                print(f"✅ Point registered: {point_reg[framecount]}")
            except Exception as e:
                print(f"⚠️ Error: {e}")

            framecount += 1
            await asyncio.sleep(0.1)
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.1)
            return
    print(f"🎯 All points registered. Saving data... ")
    pickle_path_tibia = os.path.join(
        current_dir, "..", "..", "registration_data", "tibia_variables_TC.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_tibia), exist_ok=True)
    with open(pickle_path_tibia, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
    print("✅ Pickle dump successful ")
    print(variable_dict)
    return


async def UniTebia_marking_tc(marking, websocket):
    framecount = 0
    variable_dict = {}
    point_reg = ["TC", "AL1", "AL2"]
    try:
        pickle_path_femur = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_femur, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur variables loaded successfully")
    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading femur variables: {e}")
        return {}

    while framecount < len(point_reg):
        try:
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 or len(detections_left) == 9:
                    break
                await asyncio.sleep(0.2)
                if page != "unimultiple-page.html":
                    return
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            detections_right = sorted(
                detections_right[3:], key=lambda x: x[1], reverse=True
            )
            detections_left = sorted(
                detections_left[3:], key=lambda x: x[1], reverse=True
            )

            next_three_right = sorted(detections_right[:3], key=lambda x: x[0])
            next_three_left = sorted(detections_left[:3], key=lambda x: x[0])
            last_three_right = sorted(detections_right[-3:], key=lambda x: x[0])
            last_three_left = sorted(detections_left[-3:], key=lambda x: x[0])

            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left
            )

            pointer_tip_gcs = Pointer_traker.getStylusPoint()

            femure = TriangleTracker(
                first_three_right,
                first_three_left,
                mode="F",
            )

            F_points = femure.getLEDcordinates()
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )

            try:
                pickle_path_femure = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "femur_variables.pickle",
                )
                with open(pickle_path_femure, "rb") as handle:
                    femur_dict = pickle.load(handle)
            except:
                print("Error loading femur variables")

            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            tebia = TriangleTracker(
                last_three_right, last_three_left, mode="T"
            )
            T_Points = tebia.getLEDcordinates()
            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], NewFC]
            else:
                tibia_plane = [T_Points[2], T_Points[1], NewFC]

            if framecount == 0:
                variable_memory = {"C": pointer_tip_gcs, "Plane": tibia_plane}
            else:
                pointRegistered = utils.find_new_point_location(
                    old_plane_points=tibia_plane,
                    new_plane_points=variable_dict["TC"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": variable_dict["TC"]["Plane"],
                }
            variable_dict[point_reg[framecount]] = variable_memory

            try:
                await send_tuple(websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2))
                utils.play_notification_sound()
                print(f"✅ Point registered: {point_reg[framecount]}")
            except Exception as e:
                print(f"⚠️ Error: {e}")
            framecount += 1
            await asyncio.sleep(0.1)
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.5)
            return
    print("🎯 All points registered. Saving data...")
    pickle_path_tibia = os.path.join(
        current_dir, "..", "..", "registration_data", "tibia_variables_TC.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_tibia), exist_ok=True)
    with open(pickle_path_tibia, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
    print("✅ Pickle dump successful")
    return


async def FemureMarking(marking, websocket):
    global framecount, variable_dict
    framecount = 1
    try:
        # Load Transformation Plane (TP) variables
        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_center.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_HC = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["HC", "FC", "ME", "LE", "FAP", "MDC", "LDC", "MPC", "LPC", "AC"]

    while framecount < len(point_reg):
        try:

            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    break
                else:
                    await asyncio.sleep(0.1)
                    continue
            if page not in ["revmultiple-page2.html", "multiple-page2.html"]:
                print(f'page is not matching***********')
                return
            if point_reg[framecount] in ["MPC", "LDC", "LPC", "MDC"]:
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                #
                # first_three_right = sorted(first_three_right, key=lambda x: x[0])
                # first_three_left = sorted(first_three_left, key=lambda x: x[0])

            else:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])
                # Delete last three elements (in-place)
                del detections_right[-3:]
                del detections_left[-3:]

                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)
                first_three_right, first_three_left = (
                    detections_right[:3],
                    detections_left[:3],
                )
                next_three_right, next_three_left = (
                    detections_right[3:6],
                    detections_left[3:6],
                )

            # Process the detections and compute the 3D points
            Pointer_tracker = TriangleTracker(
                detections_right=first_three_right,
                detections_left=first_three_left,

            )

            p_ = Pointer_tracker.getLEDcordinates()
            pointer_tip_gcs = Pointer_tracker.getStylusPoint()

            Femur = TriangleTracker(
                detections_right=next_three_right,
                detections_left=next_three_left,

                mode="F",
            )

            F_points = Femur.getLEDcordinates()
            print(
                f" point {point_reg[framecount]} first_three_left {first_three_right}   pointer_tip_gcs {pointer_tip_gcs}")
            print(
                f" point {point_reg[framecount]} first_three_right {first_three_left} pointer_tip_gcs {pointer_tip_gcs}")
            # Arrange femur plane points
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )
            if framecount == 1:
                x1, y1, z1 = F_points[2]
                point_A = (F_points[1] + F_points[0]) / 2.0
                x2, y2, z2 = point_A
                m, b = utils.find_line_equation(x1, y1, x2, y2)

                def d_min(x0, y0, m, b):
                    return abs(m * x0 - y0 + b) / math.sqrt(1 + m ** 2)

                x0, y0, z0 = pointer_tip_gcs
                distance = d_min(x0, y0, m, b)

                variable_dict[point_reg[framecount]] = {
                    "C": pointer_tip_gcs,
                    "Plane": femur_plane,
                }

            elif point_reg[framecount] == "FAP":
                pointRegistered = utils.find_new_point_location(
                    femur_plane, variable_dict["FC"]["Plane"], pointer_tip_gcs
                )
                p_points = Pointer_tracker.getLEDcordinates()
                variable_dict[point_reg[framecount]] = {
                    "C": (pointRegistered, p_points[2]),
                    "Plane": variable_dict["FC"]["Plane"],
                }
            else:
                pointRegistered = utils.find_new_point_location(
                    femur_plane, variable_dict["FC"]["Plane"], pointer_tip_gcs
                )
                variable_dict[point_reg[framecount]] = {
                    "C": pointRegistered,
                    "Plane": variable_dict["FC"]["Plane"],
                }
            try:
                await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
            except Exception as e:
                print(f"⚠️ WebSocket send failed: {e}")
            utils.play_notification_sound()

            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay

        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.1)
            return

    print("🎯 All points registered. Saving data...")

    pickle_path_femure = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_femure), exist_ok=True)

    with open(pickle_path_femure, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print("✅ Pickle dump successful")

    direction_vector = np.array(femur_HC) - np.array(variable_dict["FC"]["C"])
    normal_vector = direction_vector / np.linalg.norm(direction_vector)

    A, B, C = normal_vector
    D = -np.dot(normal_vector, np.array(variable_dict["FC"]["C"]))

    def distance_from_point_to_plane(point, A, B, C, D):
        point = np.array(point)
        return abs(A * point[0] + B * point[1] + C * point[2] + D) / np.sqrt(
            A ** 2 + B ** 2 + C ** 2
        )

    distance_to_MDC = distance_from_point_to_plane(
        variable_dict["MDC"]["C"], A, B, C, D
    )
    distance_to_LDC = distance_from_point_to_plane(
        variable_dict["LDC"]["C"], A, B, C, D
    )

    femure_BoneCut_Point = "MDC" if distance_to_MDC > distance_to_LDC else "LDC"

    FemureCuttingPath = os.path.join(
        current_dir, "..", "..", "registration_data", "FemureCuttingPoint.txt"
    )
    with open(FemureCuttingPath, "wb") as handle:
        pickle.dump(femure_BoneCut_Point, handle, protocol=pickle.HIGHEST_PROTOCOL)

    framecount = 0
    variable_dict = {}


async def FemureMarkingUni(marking, websocket):
    global framecount, variable_dict
    framecount = 1
    try:
        # Load Transformation Plane (TP) variables
        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_center.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_HC = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["HC", "FC", "TM", "TS1", "TS2", "MDC", "MPC", "ME"]

    while framecount < len(point_reg):
        if page != "unimultiple-page2.html":
            return
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) < 9 and len(detections_left) < 9:
                await asyncio.sleep(0.1)
                continue
            if point_reg[framecount] in ["TS1", "TS2", "MPC", "MDC"]:
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
            else:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])
                # Delete last three elements (in-place)
                del detections_right[-3:]
                del detections_left[-3:]

                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)
                first_three_right, first_three_left = (
                    detections_right[:3],
                    detections_left[:3],
                )
                next_three_right, next_three_left = (
                    detections_right[3:6],
                    detections_left[3:6],
                )

            Pointer_tracker = TriangleTracker(
                detections_right=first_three_right,
                detections_left=first_three_left,

            )

            pointer_tip_gcs = Pointer_tracker.getStylusPoint()

            Femur = TriangleTracker(
                detections_right=next_three_right,
                detections_left=next_three_left,

                mode="F",
            )

            F_points = Femur.getLEDcordinates()

            print(f' F_points {F_points}')
            # Arrange femur plane points
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )
            if framecount == 1:
                variable_memory = {"C": pointer_tip_gcs, "Plane": femur_plane}
                variable_dict[point_reg[framecount]] = variable_memory
                print(f"pointRegistered {point_reg[framecount]} {pointer_tip_gcs}")
            else:
                pointRegistered = utils.find_new_point_location(
                    old_plane_points=femur_plane,
                    new_plane_points=variable_dict["FC"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": variable_dict["FC"]["Plane"],
                }
                print(f"pointRegistered {point_reg[framecount]} {pointRegistered}")

            variable_dict[point_reg[framecount]] = variable_memory
            try:
                await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
            except Exception as e:
                print(f"⚠️ WebSocket send failed: {e}")
            utils.play_notification_sound()
            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay

        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.1)
            return

    print("🎯 All points registered. Saving data...")

    pickle_path_femure = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_femure), exist_ok=True)

    with open(pickle_path_femure, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print("✅ Pickle dump successful")
    framecount = 0
    variable_dict = {}


def load_pickle_file(file_path, var_name):
    """Load a pickle file and handle exceptions."""
    try:
        with open(file_path, "rb") as handle:
            data = pickle.load(handle)
        # print(f"{var_name} loaded successfully")
        return data
    except FileNotFoundError:
        print(f"Error: File not found - {file_path}")
    except pickle.UnpicklingError:
        print(f"Error: Unable to unpickle data from - {file_path}")
    except Exception as e:
        print(f"Error loading {var_name}: {e}")
    return None


async def uniFree_point_collection_femur(marking, websocket):
    global framecount, variable_dict
    points_cloud = []
    framecount = 0
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")
    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        # "tibia_variables_TC": os.path.join(base_dir, 'tibia_variables_TC.pickle'),
        # "tibia_variables": os.path.join(base_dir, 'tibia_variables.pickle'),
    }
    femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")
    print(f"femur_dict {femur_dict}")
    camera_reference = np.array(
        [
            HC,
            femur_dict["FC"]["C"],
            femur_dict["TM"]["C"],
            femur_dict["TS1"]["C"],
            femur_dict["TS2"]["C"],
            femur_dict["MDC"]["C"],
            femur_dict["MPC"]["C"],
            femur_dict["ME"]["C"],
        ]
    )
    print(f' camera_reference  {camera_reference}')
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_uniFemure = np.array(
        [
            [
                -102.9053543979592,
                -14.204959734693874,
                -8.730872840816328,
            ],  # Hip Center in STL model
            [-52.2903404, -3.131314, -11.5643253],  # FC
            [-52.2250595, -1.853195, -9.8895073],  # TM
            [-53.1750908, -1.504328, -11.4055157],  # TS1
            [-52.1881371, -3.095505, -10.8363781],  # TS2
            [-51.2940788, -3.5388589, -8.9086733],  # MDC
            [-52.9359016, -6.9525371, -8.3687782],  # MPC
            [-54.2269897, -3.982173, -6.5660419],  # ME
        ]
    )
    for i in obj_points_uniFemure:
        await send_tuple(websocket, i)
        await asyncio.sleep(1)

    # Calculate the transformation matrix based on the camera reference
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_uniFemure
    )
    while framecount < 100:
        try:
            if page != "uniFree_point_collection_femur.html":
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            print(f'test  {len(detections_right)}    {len(detections_left)}')
            if len(detections_right) == 9 and len(detections_left) == 9:
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]

                femure = TriangleTracker(
                    next_three_right, next_three_left, mode='F'
                )

                F_points = femure.getLEDcordinates()

                # print(f'Pointer_traker {p_point}')
                Pointer_traker = TriangleTracker(
                    first_three_right,
                    first_three_left,
                    mode="T",
                )

                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                print(f'F_points  {F_points} ')
                # Arrange femur plane points
                femur_plane = (
                    [F_points[2], F_points[0], F_points[1]]
                    if F_points[1][1] >= F_points[0][1]
                    else [F_points[2], F_points[1], F_points[0]]
                )
                pointRegistered = utils.find_new_point_location(
                    old_plane_points=femur_plane,
                    new_plane_points=femur_dict["FC"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                points_cloud.append(pointRegistered)
                data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]}\n"
                # data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]},{condition}\n"
                point = np.array(eval(data), dtype=np.float32)
                point = [
                    point[0],
                    -point[1],
                    point[2],
                ]  # Adjust Z axis orientation if needed
                transformed_point = (
                        np.dot(transformation_matrix[:3, :3], point)
                        + transformation_matrix[:3, 3]
                )
                try:
                    await send_tuple(websocket, transformed_point)
                except Exception as e:
                    print(f"⚠️ WebSocket send failed: {e}")
                # utils.play_notification_sound()
                framecount += 1  # Move to the next point
                await asyncio.sleep(0.1)  # Delay
                print(
                    f"framecount  {framecount} pointRegistered {pointRegistered} transformed_point {transformed_point}")
            else:
                await asyncio.sleep(0.05)
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.1)
            return
    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloud.pickle"
    )
    try:
        with open(freePoint_knee_uni, "wb") as handle:
            pickle.dump(
                np.array(points_cloud), handle, protocol=pickle.HIGHEST_PROTOCOL
            )
        print("Pickle dump successful")
    except:
        print("Error loading HC variables")
    framecount = 0
    variable_dict = {}
    try:
        await websocket.send_text("exit")
    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")
    return


async def PrimaryKneeLandMarkVerification(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
    except Exception as e:
        print(f"Error loading femur or tibia variables: {e}")
        return {}
    while True:
        try:
            if page not in ["tkr-screen-2.html", "revtkr-screen-2.html"]:
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 9 and len(detections_left) == 9:
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                femure = TriangleTracker(
                    next_three_right, next_three_left, "F"
                )

                F_points = femure.getLEDcordinates()
                Pointer_traker = TriangleTracker(
                    first_three_right,
                    first_three_left,
                )

                tip = Pointer_traker.getStylusPoint()

                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                # print(f'F_points {F_points}')
                a = distance(F_points[0], F_points[1])  # 64 mm
                b = distance(F_points[1], F_points[2])  # 113.6 mm
                c = distance(F_points[2], F_points[0])  # 111.26 mm
                # Real-world side lengths in mm
                real_a = 64.0
                real_b = 113.6
                real_c = 111.26

                # Heron's formula - camera space
                s_cam = (a + b + c) / 2
                area_cam = np.sqrt(s_cam * (s_cam - a) * (s_cam - b) * (s_cam - c))

                # Heron's formula - real-world space
                s_real = (real_a + real_b + real_c) / 2
                area_real = np.sqrt(s_real * (s_real - real_a) * (s_real - real_b) * (s_real - real_c))

                # Scale factor (mm per unit)
                scale = np.sqrt(area_real / area_cam)

                MDC = utils.find_new_point_location(
                    femur_dict["MDC"]["Plane"], femur_plane, femur_dict["MDC"]["C"]
                )
                MPC = utils.find_new_point_location(
                    femur_dict["MPC"]["Plane"], femur_plane, femur_dict["MPC"]["C"]
                )
                LDC = utils.find_new_point_location(
                    femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
                )
                LPC = utils.find_new_point_location(
                    femur_dict["LPC"]["Plane"], femur_plane, femur_dict["LPC"]["C"]
                )
                FC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )

                def round_to_two_decimals(value):
                    return round(value, 2)

                # Compute all distances and store them with labels
                distances = {
                    "MDC": utils.distance_3d(MDC, tip) * scale,
                    "MPC": utils.distance_3d(MPC, tip) * scale,
                    "LDC": utils.distance_3d(LDC, tip) * scale,
                    "LPC": utils.distance_3d(LPC, tip) * scale,
                    "FC": utils.distance_3d(FC, tip) * scale
                }
                # Find the closest point
                closest_point, closest_distance_mm = min(distances.items(), key=lambda x: x[1])

                # Compare with the threshold
                REAL_WORLD_THRESHOLD_MM = 0.4
                rounded_closest = round_to_two_decimals(closest_distance_mm)
                rounded_threshold = round_to_two_decimals(REAL_WORLD_THRESHOLD_MM)

                # Decide validity based on actual real-world mm distance
                if closest_distance_mm <= REAL_WORLD_THRESHOLD_MM:
                    print(
                        f"distance {rounded_closest} mm, closest point {closest_point} {MDC} , tip {tip}, scale {scale:.6f} → ✅ Valid (green flag)")
                else:
                    print(
                        f"distance {rounded_closest} mm, closest point {closest_point} {MDC}  , tip {tip}, scale {scale:.6f} → ❌ Not Valid (red flag)")

                # Send result (in mm)
                await send_tuple(websocket, (rounded_closest, 0, 0, 0))
                # Send result
                await send_tuple(websocket, (rounded_closest, 0, 0, 0))
            else:
                # await send_tuple(websocket, ('NA', 0, 0, 0))
                await asyncio.sleep(0.02)
        except Exception as err:
            print(f"{__file__} e {err}")
            return


async def ml_size_acquisition(marking, websocket):
    global framecount, variable_dict
    framecount = 0
    try:
        # Load Transformation Plane (TP) variables
        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["M", "L"]

    while framecount < len(point_reg):
        try:
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    break
                await asyncio.sleep(0.2)

            # Sort detections
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)
            first_three_right, first_three_left = (
                detections_right[:3],
                detections_left[:3],
            )
            next_three_right, next_three_left = (
                detections_right[3:6],
                detections_left[3:6],
            )

            # Process the detections and compute the 3D points
            Pointer_tracker = TriangleTracker(
                tuple(first_three_right),
                tuple(first_three_left)
            )

            pointer_tip_gcs = Pointer_tracker.getStylusPoint()

            Femur = TriangleTracker(
                tuple(next_three_right),
                tuple(next_three_left),
            )

            F_points = Femur.getLEDcordinates()

            # Arrange femur plane points
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )

            pointRegistered = utils.find_new_point_location(
                femur_plane, femur_dict["FC"]["Plane"], pointer_tip_gcs
            )
            variable_dict[point_reg[framecount]] = {
                "C": pointRegistered,
                "Plane": femur_dict["FC"]["Plane"],
            }
            utils.play_notification_sound()

            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay

        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.5)
            return

    print("🎯 All points registered. Saving data...")

    pickle_path_femure = os.path.join(
        current_dir, "..", "..", "registration_data", "femure_ML_Size_variables.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_femure), exist_ok=True)

    with open(pickle_path_femure, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print("✅ Pickle dump successful")
    try:
        await send_tuple(websocket, ("exit", 4.0, 5.6, 7.3))
    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")


async def revtibia_im_canal_ream(marking, websocket):
    global framecount, variable_dict
    framecount = 0

    try:
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Load Transformation Plane (TP) variables
        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

        pickle_path_femure = os.path.join(
            current_dir,
            "..",
            "..",
            "registration_data",
            "femure_ML_Size_variables.pickle",
        )

        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_tc_handle:
            tibia_tc = pickle.load(tibia_tc_handle)
        pickle_path_tibia = os.path.join(base_dir, "tibia_variables.pickle")
        with open(pickle_path_tibia, "rb") as tibia_handle:
            tibia_dict = pickle.load(tibia_handle)

        with open(pickle_path_femure, "rb") as handle:
            femure_size_dict = pickle.load(handle)
        print("✅ ML Sizevariables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    while True:
        try:
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 6 and len(detections_left) == 6:
                    await asyncio.sleep(0.2)
                    break
                await asyncio.sleep(0.1)

            # Sort detections
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right, first_three_left = (
                detections_right[:3],
                detections_left[:3],
            )
            next_three_right, next_three_left = (
                detections_right[:-3],
                detections_left[:-3],
            )

            # Process the detections and compute the 3D points
            Femure = TriangleTracker(
                tuple(first_three_right),
                tuple(first_three_left),
            )
            F_points = Femure.getLEDcordinates()
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )
            Tibia = TriangleTracker(
                tuple(next_three_right),
                tuple(next_three_left),
            )

            T_points = Tibia.getLEDcordinates()
            tibia_plane = (
                [T_points[2], T_points[0], T_points[1]]
                if T_points[1][1] >= T_points[0][1]
                else [T_points[2], T_points[1], T_points[0]]
            )
            # pointA = utils.find_new_point_location(femur_plane, variable_dict['M']['Plane'],
            #                                        variable_dict['C'])
            # pointB = utils.find_new_point_location(femur_plane, variable_dict['M']['Plane'],
            #                                        variable_dict['C'])

            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tibia_plane[2] = NewFC
            NewTC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
            )
            tibia_plane[2] = NewTC
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tibia_plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tibia_plane, tibia_dict["LM"]["C"]
            )

            ANC = (np.array(MM) + np.array(LM)) / 2
            print(f' femur_plane {femur_plane}')
            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            mid = (B + C) / 2
            # distance1 = utils.distance_3d(mid, femur_plane[0])
            # scale1 = 107 / distance1  # 107 mm is the reference distance
            # distance2 = utils.distance_3d(pointA, pointB)
            # femure_size = abs(distance2) * scale1

            vector1 = np.array(ANC) - np.array(NewTC)
            vector2 = np.array(mid) - np.array(femur_plane[0])

            # Compute dot product
            dot_product = np.dot(vector1, vector2)

            # Compute norms (magnitudes)
            norm_v1 = np.linalg.norm(vector1)
            norm_v2 = np.linalg.norm(vector2)

            # Compute angle in radians
            angle_radians = np.arccos(dot_product / (norm_v1 * norm_v2))

            # Convert to degrees
            angle_degrees = np.degrees(angle_radians)
            if angle_degrees <= 3:
                message = "Noconflict_message"
            else:
                message = "conflict_message"
            await send_tuple(websocket, (message, 2, 2, 2))
            await asyncio.sleep(0.05)  # Delay
            print(f" angle_degrees {angle_degrees}  message{message}")
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            return


async def revfemur_im_canal_ream(marking, websocket):
    global framecount, variable_dict
    framecount = 0

    try:

        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

        pickle_path_femur_hc = os.path.join(current_dir, "..", "..", "registration_data", "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    while True:
        try:
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 6 and len(detections_left) == 6:
                    break
                await asyncio.sleep(0.2)

            # Sort detections
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right, first_three_left = (
                detections_right[:3],
                detections_left[:3],
            )
            next_three_right, next_three_left = (
                detections_right[:-3],
                detections_left[:-3],
            )

            # Process the detections and compute the 3D points
            Femure = TriangleTracker(
                first_three_right,
                first_three_left,
                mode='F'
            )
            F_points = Femure.getLEDcordinates()
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )

            # pointA = utils.find_new_point_location(femur_plane, variable_dict['M']['Plane'],
            #                                        variable_dict['C'])
            # pointB = utils.find_new_point_location(femur_plane, variable_dict['M']['Plane'],
            #                                        variable_dict['C'])

            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            mid = (B + C) / 2
            # distance1 = utils.distance_3d(mid, femur_plane[0])
            # scale1 = 107 / distance1  # 107 mm is the reference distance
            # distance2 = utils.distance_3d(pointA, pointB)
            # femure_size = abs(distance2) * scale1

            vector1 = np.array(HC) - np.array(NewFC)
            vector2 = np.array(mid) - np.array(femur_plane[0])

            # Compute dot product
            dot_product = np.dot(vector1, vector2)

            # Compute norms (magnitudes)
            norm_v1 = np.linalg.norm(vector1)
            norm_v2 = np.linalg.norm(vector2)

            # Compute angle in radians
            angle_radians = np.arccos(dot_product / (norm_v1 * norm_v2))

            # Convert to degrees
            angle_degrees = np.degrees(angle_radians)
            if angle_degrees <= 3:
                message = "Noconflict_message"
            else:
                message = "conflict_message"
            await send_tuple(websocket, (message, 2, 2, 2))
            await asyncio.sleep(1)  # Delay
            print(f" angle_degrees {angle_degrees}  message{message}")
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.5)
            return


async def revrevisionKneeTibiaCut(marking, websocket):
    def custom_sort(points):
        # Step 1: Find the point with the highest y-value
        highest_y_point = max(points, key=lambda p: p[1])  # Use max() for highest y

        # Step 2: Remove it from the list and sort the remaining points based on x
        remaining_points = [p for p in points if p != highest_y_point]
        sorted_remaining = sorted(remaining_points, key=lambda p: p[0])

        # Step 3: Return sorted list in order: highest_y_point, then x-sorted points
        return [highest_y_point] + sorted_remaining

    base_dir = os.path.join(current_dir, "..", "..", "registration_data")
    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        "tibia_variables_TC": os.path.join(base_dir, "tibia_variables_TC.pickle"),
        "tibia_variables": os.path.join(base_dir, "tibia_variables.pickle"),
    }
    # femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")
    femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    tibia_dict_tc = load_pickle_file(
        files_to_load["tibia_variables_TC"], "Tibia TC variables"
    )
    tibia_dict = load_pickle_file(files_to_load["tibia_variables"], "Tibia variables")
    # Construct the correct path to the registration_data folder (up one level)
    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloudtibia.pickle"
    )
    try:
        with open(freePoint_knee_uni, "rb") as handle:
            points_cloud = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    camera_reference = np.array(
        [
            tibia_dict_tc["TC"]["C"],
            tibia_dict["MC"]["C"],
            tibia_dict["MM"]["C"],
            tibia_dict["LM"]["C"],
        ]
    )
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_Tibia = np.array(
        [
            [
                -64.6756134,
                -2.9603553,
                -17.7387486,
            ],  # TC [-60.84293912 -17.69269143   2.21923788]
            [-64.2572708, -4.0632067, -14.2518415],  # MC
            [-0.7417173, -8.5226688, -13.8344755],  # MM
            [-0.2158346, -9.4181175, -22.6858425],  # LM
        ]
    )
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_Tibia
    )

    points_cloud = np.array(points_cloud)

    new_points = np.array(
        [
            tibia_dict_tc["TC"]["C"],
            tibia_dict["MC"]["C"],
            tibia_dict["LC"]["C"],
            # tibia_dict['MM']['C'],
            # tibia_dict['LM']['C']
        ]
    )

    while True:
        try:
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.2)
                    break
                await asyncio.sleep(0.1)
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]

            femure = TriangleTracker(
                first_three_right,
                first_three_left,
                mode='F'
            )

            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            next_three_right = custom_sort(detections_right[3:6])
            next_three_left = custom_sort(detections_left[3:6])

            # Initialize the tracker with sorted points
            Pointer_traker = TriangleTracker(
                next_three_right,
                next_three_left,
            )

            pointer_tip_gcs = Pointer_traker.getStylusPoint()
            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tibia = TriangleTracker(
                last_three_right,
                last_three_left,
                mode='T'
            )

            T_Points = tibia.getLEDcordinates()
            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
            else:
                tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]

            tibia_plane[2] = NewFC
            NewTC = utils.find_new_point_location(
                tibia_dict_tc["TC"]["Plane"], tibia_plane, tibia_dict_tc["TC"]["C"]
            )

            tibia_plane[2] = NewTC
            pointRegistered = utils.find_new_point_location(
                tibia_plane, tibia_dict_tc["TC"]["Plane"], pointer_tip_gcs
            )

            ANC = np.array(
                [
                    (tibia_dict["MM"]["C"][i] + tibia_dict["LM"]["C"][i]) / 2
                    for i in range(3)
                ]
            )
            # points_cloud = np.concatenate((points_cloud, new_points), axis=0)
            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            # distance1 = utils.distance_3d(B, C)
            v_am = np.subtract(B, C)
            distance1 = np.linalg.norm(v_am)
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = 10 / scale1

            direction_vector = tibia_dict_tc["TC"]["C"] - ANC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling

            projection_point = tibia_dict["MC"]["C"] - scaled_unit_vector
            # projection_point = tibia_dict['MC']['C']
            a, b, c = direction_vector  # Normal vector
            x0, y0, z0 = projection_point  # Point on the plane
            # Calculate 'd' using the plane equation
            d = -(a * x0 + b * y0 + c * z0)
            # Concatenate the new points to the existing points_cloud
            points_cloud = np.concatenate((points_cloud, new_points), axis=0)
            hull = ConvexHull(points_cloud)
            moving_point = np.array(pointRegistered)  #
            moving_point_distance = np.dot(moving_point, np.array([a, b, c])) + d

            def point_in_hull(point, hull):
                new_points = np.vstack([hull.points, point])
                new_hull = ConvexHull(new_points)
                return np.array_equal(new_hull.vertices, hull.vertices)

            data = f"{moving_point[0]},{-moving_point[1]},{moving_point[2]}\n"

            transformed_point = (
                    np.dot(transformation_matrix[:3, :3], eval(data))
                    + transformation_matrix[:3, 3]
            )
            if point_in_hull(moving_point, hull):
                startmotor = True
                if moving_point_distance >= 0:
                    print(f"moving_point_distance {moving_point_distance} startmotor")

                    ui_point = np.append(transformed_point, 0)
                if moving_point_distance <= 0.1:
                    # await send_tuple(websocket, ('YELLOW', 11, 5.6, 7.3))
                    ui_point = np.append(transformed_point, 1)
                    startmotor = False
                    print(f"moving_point_distance {moving_point_distance} stopmotor")
                await send_tuple(websocket, ui_point)
            else:
                # await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
                startmotor = False
                ui_point = np.append(transformed_point, 3)
                await send_tuple(websocket, ui_point)
                # if is_outside_convex_hull(moving_point, hull_points):
                #     print("Pointer is outside the convex hull")
                # else:
                # print("Pointer is inside or on the surface of the convex hull")
                print(f"moving_point_distance {moving_point_distance} stopmotor")
                time.sleep(0.1)
        except Exception as e:
            await asyncio.sleep(0.2)
            print(f"⚠️ WebSocket send failed: {e}")


async def revverificationTibiaCut(marking, websocket):
    try:
        await send_tuple(websocket, ("tibia-inner-page3", 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")

            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_tc = pickle.load(tibia_handle)
            # Tibia TC variables
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_dict = pickle.load(tibia_handle)
                print(f" tibia_dict {tibia_dict}")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    omega = utils.GetplanningInput(filename="tibia_input1")
    while True:
        try:
            if page != "revverification-proximal-tibia-cut.html":
                return
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            # Extract elements in groups of 3
            detection_groups = {
                "F": (detections_right[:3], detections_left[:3]),  # Robot
                "V": (detections_right[3:6], detections_left[3:6]),  # Femur
                "T": (detections_right[6:9], detections_left[6:9]),  # Verification Tool
                # "T": (detections_right[9:12], detections_left[9:12]),  # Tibia
            }

            # Store the results
            tracked_points = {}

            # Iterate over detection groups and process each one
            for label, (right, left) in detection_groups.items():
                tracker = (
                    TriangleTracker(right, left)
                    if label == "V"  # No label passed for Robot & Verification Tool
                    else TriangleTracker(
                        right, left, label
                    )
                    # Label passed for Femur & Tibia
                )
                tracked_points[label] = tracker.getLEDcordinates()
            # Extract individual results
            # R_points = tracked_points["R"]
            F_points = tracked_points["F"]
            Tool_points = tracked_points["V"]
            T_points = tracked_points["T"]
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            if T_points[1][1] >= T_points[0][1]:
                tebia_Plane = [T_points[2], T_points[0], T_points[1]]
            else:
                tebia_Plane = [T_points[2], T_points[1], T_points[0]]

            B = np.array(tebia_Plane[1])
            C = np.array(tebia_Plane[0])
            distance1 = utils.distance_3d(B, C)
            scale = 107 / distance1  # 107 mm is the reference distance

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tebia_Plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tebia_Plane, tibia_tc["TC"]["C"]
            )

            tebia_Plane[2] = TC
            # print(f'  tebia_Plane  {tebia_Plane}')
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tebia_Plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tebia_Plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tebia_Plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tebia_Plane, tibia_dict["LM"]["C"]
            )
            ANC = (np.array(MM) + np.array(LM)) / 2

            Mid_Tool_points = (np.array(Tool_points[1]) + np.array(Tool_points[2])) / 2

            # Step 2: y vector calculation
            toolTracker_y = Mid_Tool_points - Tool_points[0]

            # Step 3: x vector calculation
            toolracker_x = np.array(Tool_points[1]) - np.array(Tool_points[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            toolrackernorm_x = np.linalg.norm(toolracker_x)

            toolrackernorm_y = np.linalg.norm(toolTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            toolracker_z = np.cross(toolTracker_y, toolracker_x)

            # Step 6: FCS vectors
            x_FCS = ANC - TC
            z_FCS = LC - MC

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, MC, LC):
                # print(f'*' * 100)
                # print(f'tracker {tracker}  ME {MC}  LE {LC}')
                # print(f'*' * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                MC_translated = MC - mid_point
                LC_translated = LC - mid_point

                # Step 5: Apply rotation (World to Local)
                MC_local = R.T @ MC_translated
                LC_local = R.T @ LC_translated

                # Step 6: Compute MC→LC vector in LCS
                V_local = LC_local - MC_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                print(f'toolrackernorm_y  {toolrackernorm_y}')
                print(f'x_FCS  {x_FCS}')
                flexion = vector2_signed_angle_xy(toolTracker_y, x_FCS)
                varus = vector3_signed_angle(tracker=Tool_points, MC=MC, LC=LC)

                print(f"flexion  {flexion} varus {varus}")

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")

            # Given data

            cutting_points_scalling = 50 / scale
            cutting_points = 9 / scale
            # # Compute direction vectors
            direction_vector = ANC - TC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points
            projection_point = TC + scaled_unit_vector
            print(f'projection_point  {projection_point} ')
            direction_vector2 = toolTracker_y.copy()
            unit_vector2 = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector2 = unit_vector2 * cutting_points_scalling
            projection_point2 = TC + scaled_unit_vector2  #
            print(f'projection_point2  {projection_point2} ')
            # Compute plane normal using cross product
            normal_vector = np.cross(direction_vector, scaled_unit_vector)

            # Compute plane equation: Ax + By + Cz + D = 0
            A, B, C = normal_vector
            D = -np.dot(normal_vector, projection_point)

            # Compute distance from projection_point2 to the plane
            x1, y1, z1 = projection_point2
            distance_mm = abs(A * x1 + B * y1 + C * z1 + D) / np.linalg.norm(
                normal_vector
            )

            print(
                f"Distance between projection_point2 and the plane in mm: {distance_mm:.2f} mm"
            )

            await send_tuple(
                websocket, ("rev_tibia_verify", flexion, varus, f'{distance_mm:.2f}')
            )
            await asyncio.sleep(0.1)
        except Exception as e:
            await asyncio.sleep(0.2)
            print(f"Error {e}")
            return


async def revverificationdistalFemure(marking, websocket):
    try:
        await send_tuple(websocket, ("tibia-inner-page3", 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")

            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_tc = pickle.load(tibia_handle)
            # Tibia TC variables
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_dict = pickle.load(tibia_handle)
                print(f" tibia_dict {tibia_dict}")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    omega = utils.GetplanningInput(filename="tibia_input1")
    while True:
        try:
            if page != "revverification-proximal-tibia-cut.html":
                return
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            # Extract elements in groups of 3
            detection_groups = {
                "F": (detections_right[:3], detections_left[:3]),  # Robot
                "V": (detections_right[3:6], detections_left[3:6]),  # Femur
                # "T": (detections_right[6:9], detections_left[6:9]),  # Verification Tool
                # "T": (detections_right[9:12], detections_left[9:12]),  # Tibia
            }

            # Store the results
            tracked_points = {}

            # Iterate over detection groups and process each one
            for label, (right, left) in detection_groups.items():
                tracker = (
                    TriangleTracker(right, left)
                    if label == "V"  # No label passed for Robot & Verification Tool
                    else TriangleTracker(
                        right, left, label
                    )
                    # Label passed for Femur & Tibia
                )
                tracked_points[label] = tracker.getLEDcordinates()
            # Extract individual results
            # R_points = tracked_points["R"]
            F_points = tracked_points["F"]
            Tool_points = tracked_points["V"]
            # T_points = tracked_points["T"]
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[0])
            distance1 = utils.distance_3d(B, C)
            scale = 107 / distance1  # 107 mm is the reference distance

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            Mid_Tool_points = (np.array(Tool_points[1]) + np.array(Tool_points[2])) / 2

            # Step 2: y vector calculation
            toolTracker_y = Mid_Tool_points - Tool_points[0]

            # Step 3: x vector calculation
            toolracker_x = np.array(Tool_points[1]) - np.array(Tool_points[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            toolrackernorm_x = np.linalg.norm(toolracker_x)

            toolrackernorm_y = np.linalg.norm(toolTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            toolracker_z = np.cross(toolTracker_y, toolracker_x)

            x_FCS = FC - HC
            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, ME, LE):
                # print(f'*' * 100)
                # print(f'tracker {tracker}  ME {MC}  LE {LC}')
                # print(f'*' * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                MC_translated = ME - mid_point
                LC_translated = LE - mid_point

                # Step 5: Apply rotation (World to Local)
                ME_local = R.T @ MC_translated
                LE_local = R.T @ LC_translated

                # Step 6: Compute MC→LC vector in LCS
                V_local = LE_local - ME_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                print(f'toolrackernorm_y  {toolrackernorm_y}')
                print(f'x_FCS  {x_FCS}')
                flexion = vector2_signed_angle_xy(toolTracker_y, x_FCS)
                varus = vector3_signed_angle(tracker=Tool_points, ME=ME, LE=LE)

                print(f"flexion  {flexion} varus {varus}")

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")

            # Given data

            cutting_points_scalling = 50 / scale
            cutting_points = 9 / scale
            # # Compute direction vectors
            direction_vector = x_FCS.copy()
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points
            projection_point = FC + scaled_unit_vector
            print(f'projection_point  {projection_point} ')
            direction_vector2 = toolTracker_y.copy()
            unit_vector2 = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector2 = unit_vector2 * cutting_points_scalling
            projection_point2 = FC + scaled_unit_vector2  #
            print(f'projection_point2  {projection_point2} ')
            # Compute plane normal using cross product
            normal_vector = np.cross(direction_vector, scaled_unit_vector)

            # Compute plane equation: Ax + By + Cz + D = 0
            A, B, C = normal_vector
            D = -np.dot(normal_vector, projection_point)

            # Compute distance from projection_point2 to the plane
            x1, y1, z1 = projection_point2
            distance_mm = abs(A * x1 + B * y1 + C * z1 + D) / np.linalg.norm(
                normal_vector
            )

            print(
                f"Distance between projection_point2 and the plane in mm: {distance_mm:.2f} mm"
            )

            await send_tuple(
                websocket, ("rev_femur_verify", flexion, varus, f'{distance_mm:.2f}')
            )
            await asyncio.sleep(0.1)
        except Exception as e:
            await asyncio.sleep(0.2)
            print(f"Error {e}")


async def revDistalFemurCut(marking, websocket):
    global condition
    if not USE_BURR:
        return;

    # from scipy.spatial import ConvexHull
    def load_pickle_file(file_path, var_name):
        """Load a pickle file and handle exceptions."""
        try:
            with open(file_path, "rb") as handle:
                data = pickle.load(handle)
            # print(f"{var_name} loaded successfully")
            return data
        except FileNotFoundError:
            print(f"Error: File not found - {file_path}")
        except pickle.UnpicklingError:
            print(f"Error: Unable to unpickle data from - {file_path}")
        except Exception as e:
            print(f"Error loading {var_name}: {e}")
        return None
        # Base directory

    base_dir = os.path.join(current_dir, "..", "..", "registration_data")

    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        # "femur_variables": os.path.join(base_dir, 'femur_variables.pickle'),
        # "tibia_variables_TC": os.path.join(base_dir, 'tibia_variables_TC.pickle'),
    }
    # femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")

    # Construct the correct path to the registration_data folder (up one level)
    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloud.pickle"
    )
    try:
        with open(freePoint_knee_uni, "rb") as handle:
            points_cloud = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")
    # Construct the correct path to the registration_data folder (up one level)
    pickle_path_hipCenter = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    try:
        with open(pickle_path_hipCenter, "rb") as handle:
            femure_dict = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    points_cloud = np.array(points_cloud)
    new_points = np.array(
        [
            femure_dict['FC']['C'],
            femure_dict['LPC']['C'],
            femure_dict['MPC']['C'],
            femure_dict["MDC"]["C"],
            femure_dict["MPC"]["C"],
            femure_dict["ME"]["C"],
            femure_dict["LE"]["C"],
        ]
    )
    points_cloud = np.concatenate((points_cloud, new_points), axis=0)

    camera_reference = np.array(
        [
            femure_dict["FC"]["C"],
            femure_dict["MDC"]["C"],
            femure_dict["MPC"]["C"],
            femure_dict["ME"]["C"],
        ]
    )
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_Femure = np.array(
        [
            [-52.2903404, -3.131314, -11.5643253],  # FC
            [-51.2940788, -3.5388589, -8.9086733],  # MDC
            [-52.9359016, -6.9525371, -8.3687782],  # MPC
            [-54.2269897, -3.982173, -6.5660419],  # ME
        ]
    )
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_Femure
    )

    startmotor = False
    while True:
        try:
            if page != "revrevision-knee-burrFemur.html":
                return
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.2)
                    break
                elif len(detections_right) == 6 and len(detections_left) == 6:
                    stop_motor()
                    await asyncio.sleep(0.2)
                else:

                    stop_motor()
                    await asyncio.sleep(0.2)
                    await websocket.send_text('no detection')

                if page != "revrevision-knee-burrFemur.html":
                    # stop_motor()
                    return
            # Step 1: Sort by X-coordinate
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)

            # Step 4: Extract next three elements
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]

            # Remove extracted elements to ensure mutual exclusivity
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            # Step 5: Extract last three elements safely
            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]
            femure = TriangleTracker(
                first_three_right,
                first_three_left,
                mode="F",
            )

            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = np.array(
                    [F_points[2], F_points[0], F_points[1]]
                )  # Proper order of points
            else:
                femur_plane = np.array(
                    [F_points[2], F_points[1], F_points[0]]
                )  # Proper order of points
            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left
            )

            P_point = Pointer_traker.getLEDcordinates()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()
            direction_vector = HC - femure_dict["FC"]["C"]
            tip_point = calculate_contact_point(
                led1=P_point[0],
                led2=P_point[1],
                led3=P_point[2],
                surface_normal=direction_vector,
                point=pointer_tip_gcs,
                radius=6.28,
            )
            #
            pointRegistered = utils.find_new_point_location(
                old_plane_points=femur_plane,
                new_plane_points=femure_dict["FC"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )

            a, b, c = direction_vector  # Normal vector

            B = np.array(femur_plane[2])
            C = np.array(femur_plane[1])
            distance1 = utils.distance_3d(B, C)
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = 15 / scale1

            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            projection_point = femure_dict["MDC"]["C"] + scaled_unit_vector

            x0, y0, z0 = projection_point  # Point on the plane
            # Calculate 'd' using the plane equation
            d = -(a * x0 + b * y0 + c * z0)
            moving_point = np.array(
                pointRegistered
            )  # Replace with your actual moving point
            # Concatenate the projection point to the points_cloud
            points_cloud = np.concatenate((points_cloud, HC[np.newaxis, :]), axis=0)
            hull = ConvexHull(points_cloud)
            moving_point_distance = np.dot(moving_point, np.array([a, b, c])) + d

            data = f"{moving_point[0]},{-moving_point[1]},{moving_point[2]}\n"

            transformed_point = (
                    np.dot(transformation_matrix[:3, :3], eval(data))
                    + transformation_matrix[:3, 3]
            )

            def point_in_hull(point, hull):
                new_points = np.vstack([hull.points, point])
                new_hull = ConvexHull(new_points)
                return np.array_equal(new_hull.vertices, hull.vertices)

            if point_in_hull(moving_point, hull):
                startmotor = True
                if moving_point_distance <= 0:
                    condition = 1
                    print(f"moving_point_distance {moving_point_distance} start motor")
                    ui_point = np.append(transformed_point, 0)
                    start_motor()

                if moving_point_distance >= 0.1:
                    startmotor = False
                    print(f"moving_point_distance {moving_point_distance} stop motor")
                    condition = 0
                    stop_motor()
                    ui_point = np.append(transformed_point, 1)
                await send_tuple(websocket, ui_point)
            else:
                condition = 2
                startmotor = False
                # if is_outside_convex_hull(moving_point, hull_points):
                #     print("Pointer is outside the convex hull")
                # else:
                # print("Pointer is inside or on the surface of the convex hull")
                ui_point = np.append(transformed_point, 3)
                await send_tuple(websocket, ui_point)
                print(f"moving_point_distance {moving_point_distance} stop motor")
                stop_motor()
                await asyncio.sleep(0.1)
        except Exception as e:
            await asyncio.sleep(0.2)
            print(f"Error {e}")


async def revkneeFree_point_collection_femur(marking, websocket):
    global framecount, variable_dict
    points_cloud = []
    framecount = 0
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")
    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        # "tibia_variables_TC": os.path.join(base_dir, 'tibia_variables_TC.pickle'),
        # "tibia_variables": os.path.join(base_dir, 'tibia_variables.pickle'),
    }
    femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")
    print(f"femur_dict {femur_dict}")
    camera_reference = np.array(
        [
            HC,
            femur_dict["FC"]["C"],
            femur_dict["MDC"]["C"],
            femur_dict["MPC"]["C"],
            femur_dict["ME"]["C"],
        ]
    )
    print(f' camera_reference  {camera_reference}')
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_uniFemure = np.array(
        [
            [
                -102.9053543979592,
                -14.204959734693874,
                -8.730872840816328,
            ],  # Hip Center in STL model
            [-52.2903404, -3.131314, -11.5643253],  # FC
            [-51.2940788, -3.5388589, -8.9086733],  # MDC
            [-52.9359016, -6.9525371, -8.3687782],  # MPC
            [-54.2269897, -3.982173, -6.5660419],  # ME
        ]
    )
    # for i in obj_points_uniFemure:
    #     await send_tuple(websocket, i)
    #     await asyncio.sleep(1)

    # Calculate the transformation matrix based on the camera reference
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_uniFemure
    )
    while framecount < 150:
        try:
            if page != "revFree_point_collection_femur.html":
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 9 and len(detections_left) == 9:
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]

                femure = TriangleTracker(
                    next_three_right, next_three_left
                )

                F_points = femure.getLEDcordinates()

                # print(f'Pointer_traker {p_point}')
                Pointer_traker = TriangleTracker(
                    first_three_right,
                    first_three_left,
                    mode="F",
                )

                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                print(f'F_points  {F_points} ')
                # Arrange femur plane points
                femur_plane = (
                    [F_points[2], F_points[0], F_points[1]]
                    if F_points[1][1] >= F_points[0][1]
                    else [F_points[2], F_points[1], F_points[0]]
                )
                pointRegistered = utils.find_new_point_location(
                    old_plane_points=femur_plane,
                    new_plane_points=femur_dict["FC"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                points_cloud.append(pointRegistered)
                data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]}\n"
                # data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]},{condition}\n"
                point = np.array(eval(data), dtype=np.float32)
                point = [
                    point[0],
                    -point[1],
                    point[2],
                ]  # Adjust Z axis orientation if needed
                transformed_point = (
                        np.dot(transformation_matrix[:3, :3], point)
                        + transformation_matrix[:3, 3]
                )
                try:
                    await send_tuple(websocket, transformed_point)
                except Exception as e:
                    print(f"⚠️ WebSocket send failed: {e}")
                print(
                    f"framecount  {framecount} pointRegistered {pointRegistered} transformed_point {transformed_point}")
                # utils.play_notification_sound()
                framecount += 1  # Move to the next point

            else:
                await asyncio.sleep(0.5)

        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            return

    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloud.pickle"
    )
    try:
        with open(freePoint_knee_uni, "wb") as handle:
            pickle.dump(
                np.array(points_cloud), handle, protocol=pickle.HIGHEST_PROTOCOL
            )
        print("Pickle dump successful")
    except:
        print("Error loading HC variables")
    try:
        # await websocket.send("exit")
        await websocket.send_text('exit')
    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")
    utils.play_notification_sound()
    return


async def revFree_point_collectionTibia(marking, websocket):
    framecount = 0
    variable_dict = {}

    # Base directory
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")

    files_to_load = {
        # "hip_center": os.path.join(base_dir, 'hip_center.pickle'),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        "tibia_variables": os.path.join(base_dir, "tibia_variables.pickle"),
        "tibia_variables_TC": os.path.join(base_dir, "tibia_variables_TC.pickle"),
    }

    femur_dict = load_pickle_file(
        files_to_load["femur_variables"], "femur variables variables"
    )
    tibia_dict_tc = load_pickle_file(
        files_to_load["tibia_variables_TC"], "Tibia TC variables"
    )
    tibia_dict = load_pickle_file(files_to_load["tibia_variables"], "Tibia variables")

    stFrameInfo = MV_FRAME_OUT_INFO_EX()
    memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
    # print(f'tibia_dict_tc {tibia_dict_tc}')
    camera_reference = np.array(
        [
            tibia_dict_tc["TC"]["C"],
            tibia_dict["MC"]["C"],
            tibia_dict["MM"]["C"],
            tibia_dict["LM"]["C"],
        ]
    )
    print(f'camera_reference {camera_reference}')
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_uniTibia = np.array(
        [
            [
                -64.6756134,
                -2.9603553,
                -17.7387486,
            ],  # TC [-60.84293912 -17.69269143   2.21923788]
            [-64.2572708, -4.0632067, -14.2518415],  # MC
            [-0.7417173, -8.5226688, -13.8344755],  # MM
            [-0.2158346, -9.4181175, -22.6858425],  # LM
        ]
    )
    # # obj_points_uniTibia[:, [1, 2]] = obj_points_uniTibia[:, [2, 1]]
    # for i in obj_points_uniTibia:
    #     await send_tuple(websocket, i)
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_uniTibia
    )

    points_cloud = []
    while framecount < 100:
        if page != "revFree_point_collectionTibia.html":
            return
        try:
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.1)
                    break
                else:
                    try:
                        await asyncio.sleep(0.1)
                        await websocket.send_text("null")
                    except Exception as e:
                        print(f"⚠️ WebSocket send failed: {e}")
                    # if page != "uniFree_point_collectionTibia.html":
                    #     return
            # print(f' {len(detections_right)}   {len(detections_left)}')
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)

            # Step 4: Extract next three elements
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]

            # Remove extracted elements to ensure mutual exclusivity
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            # Step 5: Extract last three elements safely
            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]

            femure = TriangleTracker(
                first_three_right, first_three_left, mode='F'
            )

            F_points = femure.getLEDcordinates()
            print(f'femur_plane  {F_points}')
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left
            )

            pointer_tip_gcs = Pointer_traker.getStylusPoint()

            tibia = TriangleTracker(
                last_three_right, last_three_left
            )

            T_points = tibia.getLEDcordinates()
            print(f'T_points  {T_points}')
            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            if T_points[1][1] >= T_points[0][1]:
                tibia_plane = [T_points[2], T_points[0], NewFC]
            else:
                tibia_plane = [T_points[2], T_points[1], NewFC]
            TC = utils.find_new_point_location(
                tibia_dict_tc["TC"]["Plane"], tibia_plane, tibia_dict_tc["TC"]["C"]
            )
            tibia_plane[2] = TC
            pointRegistered = utils.find_new_point_location(
                old_plane_points=tibia_plane,
                new_plane_points=tibia_dict_tc["TC"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )
            points_cloud.append(pointRegistered)

            data = f"{pointRegistered[0]},{-pointRegistered[1]},{pointRegistered[2]}\n"

            transformed_point = (
                    np.dot(transformation_matrix[:3, :3], eval(data))
                    + transformation_matrix[:3, 3]
            )

            try:
                await send_tuple(websocket, transformed_point)
            except Exception as e:
                print(f"⚠️ WebSocket send failed: {e}")

            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay
        except Exception as e:
            print(f"Error {e}")
    utils.play_notification_sound()
    print("All points registered.")

    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloudtibia.pickle"
    )
    try:
        with open(freePoint_knee_uni, "wb") as handle:
            pickle.dump(
                np.array(points_cloud), handle, protocol=pickle.HIGHEST_PROTOCOL
            )
        print("Pickle dump successful")
    except:
        print("Error loading HC variables")

    try:
        await websocket.send_text("exit")

    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")


async def revfinal_cup_position(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        Hip_threePoint_path = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_three_variables.pickle"
        )
        with open(Hip_threePoint_path, "rb") as handle:
            hip_dict = pickle.load(handle)
    except Exception as e:
        print(f"Error loading femur or tibia variables: {e}")
        return -1
    while True:
        try:
            if page != 'revfinal-cup-position.html':
                return
            # Ensure frames are available in the queue
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if not (len(detections_right) == 6 and len(detections_left) == 6):
                await asyncio.sleep(0.1)
                continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            next_three_right = detections_right[3:]
            next_three_left = detections_left[3:]
            femure = TriangleTracker(
                tuple(next_three_right),
                tuple(next_three_left),
            )

            F_points = femure.getLEDcordinates()
            Pointer_traker = TriangleTracker(
                tuple(first_three_right),
                tuple(first_three_left),
            )

            Remure_tracker = Pointer_traker.getLEDcordinates()

            if F_points[1][1] >= F_points[0][1]:
                Tracker_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                Tracker_plane = [F_points[2], F_points[1], F_points[0]]

            ASL = utils.find_new_point_location(
                old_plane_points=hip_dict["ASL"]["Plane"],
                new_plane_points=tuple(Tracker_plane),
                old_marked_point=hip_dict["ASL"]["C"],
            )

            ASR = utils.find_new_point_location(
                old_plane_points=hip_dict["ASR"]["Plane"],
                new_plane_points=tuple(Tracker_plane),
                old_marked_point=hip_dict["ASR"]["C"],
            )

            PSC = utils.find_new_point_location(
                old_plane_points=hip_dict["PSC"]["Plane"],
                new_plane_points=tuple(Tracker_plane),
                old_marked_point=hip_dict["PSC"]["C"],
            )
            X_axis, Y_axis, Z_axis = utils.compute_local_coordinate_system(
                ASL, ASR, PSC
            )
            X_axisRh, Y_axisRh, Z_axisRh = utils.compute_local_coordinate_system(
                Remure_tracker[0], Remure_tracker[1], Remure_tracker[2]
            )
            ANTERVERSION_angle, INCLINATION_angle = (
                utils.calculate_ANTERVERSION_INCLINATION_angles(
                    Y_axisRh, X_axis, Z_axis
                )
            )

            await send_tuple(
                websocket, ("final-cup-position-1", int(ANTERVERSION_angle), 5.0, 7.3)
            )
            await send_tuple(
                websocket, ("final-cup-position-2", int(INCLINATION_angle), 0.2, 7.3)
            )
            await asyncio.sleep(0.02)
        except Exception as err:
            print(f"{__file__} e {err}")
            return


def signed_angle(u, v, axis):
    """Calculate signed angle between vectors u and v about given axis"""
    cross = np.cross(u, v)
    angle = np.arccos(np.clip(np.dot(u / np.linalg.norm(u), v / np.linalg.norm(v)), -1.0, 1.0))
    return np.sign(np.dot(axis, cross)) * angle


def interpolate_condyle_arc(MDC, MPC, LDC, LPC, Z_axis, t):
    """Returns interpolated points Q_med (medial) and Q_lat (lateral) at parameter t"""
    Z_axis = Z_axis / np.linalg.norm(Z_axis)

    # Medial condyle calculations
    h_MDC = np.dot(MDC, Z_axis)
    C_M = h_MDC * Z_axis
    vec_MDC = MDC - C_M
    vec_MPC = MPC - C_M
    r_M = (np.linalg.norm(vec_MDC) + np.linalg.norm(vec_MPC)) / 2
    u_M = vec_MDC / np.linalg.norm(vec_MDC)
    w_M = np.cross(Z_axis, u_M)
    theta_M = signed_angle(vec_MDC, vec_MPC, Z_axis)  # Signed angle

    # Lateral condyle calculations
    h_LDC = np.dot(LDC, Z_axis)
    C_L = h_LDC * Z_axis
    vec_LDC = LDC - C_L
    vec_LPC = LPC - C_L
    r_L = (np.linalg.norm(vec_LDC) + np.linalg.norm(vec_LPC)) / 2
    u_L = vec_LDC / np.linalg.norm(vec_LDC)
    w_L = np.cross(Z_axis, u_L)
    theta_L = signed_angle(vec_LDC, vec_LPC, Z_axis)  # Signed angle

    # Calculate points
    Q_med = C_M + r_M * (np.cos(t * theta_M) * u_M + np.sin(t * theta_M) * w_M)
    Q_lat = C_L + r_L * (np.cos(t * theta_L) * u_L + np.sin(t * theta_L) * w_L)

    return Q_med, Q_lat


def rotation_matrix_from_vectors(vec1, vec2):
    a, b = (vec1 / np.linalg.norm(vec1)).reshape(3), (vec2 / np.linalg.norm(vec2)).reshape(3)
    v = np.cross(a, b)
    c = np.dot(a, b)
    s = np.linalg.norm(v)
    if s == 0:
        return np.eye(3)  # No rotation needed if vectors are parallel
    kmat = np.array([[0, -v[2], v[1]],
                     [v[2], 0, -v[0]],
                     [-v[1], v[0], 0]])
    rotation_matrix = np.eye(3) + kmat + kmat.dot(kmat) * ((1 - c) / (s ** 2))
    return rotation_matrix


def cylinder_radius(point, axis_point, axis_dir):
    axis_dir = axis_dir / np.linalg.norm(axis_dir)
    v = point - axis_point
    proj_length = np.dot(v, axis_dir)
    proj_point = axis_point + proj_length * axis_dir
    radius = np.linalg.norm(point - proj_point)
    return radius


def shortest_cylinder_arc(mdc, mpc, tea, intervals=5):
    axis = tea / np.linalg.norm(tea)
    # Assume axis passes through origin; change axis_point if needed
    axis_point = np.array([0, 0, 0])
    R1 = cylinder_radius(mdc, axis_point, axis)
    R2 = cylinder_radius(mpc, axis_point, axis)
    R = (R1 + R2) / 2
    R_align = rotation_matrix_from_vectors(axis, np.array([0, 0, 1]))
    mdc_rot = R_align.dot(mdc)
    mpc_rot = R_align.dot(mpc)

    def cart_to_cyl(xyz):
        x, y, z = xyz
        theta = np.arctan2(y, x)
        return theta, z

    theta1, z1 = cart_to_cyl(mdc_rot)
    theta2, z2 = cart_to_cyl(mpc_rot)

    t_values = np.linspace(0, 1, intervals + 1)
    points = []
    for t in t_values:
        theta_t = theta1 + t * (theta2 - theta1)
        z_t = z1 + t * (z2 - z1)
        x_t = R * np.cos(theta_t)
        y_t = R * np.sin(theta_t)
        points.append(np.array([x_t, y_t, z_t]))

    R_inv = R_align.T
    points_original = [R_inv.dot(p) for p in points]
    return R, points_original


def compute_distance_to_plane(point, plane_point, normal_vector):
    # Ensure inputs are numpy arrays
    point = np.array(point)
    plane_point = np.array(plane_point)
    normal_vector = np.array(normal_vector)

    # Vector from plane point to target point
    vec = point - plane_point

    # Project vec onto the normal vector and take magnitude
    distance = np.abs(np.dot(vec, normal_vector)) / np.linalg.norm(normal_vector)
    return distance


def signed_angle(u, v, axis):
    """Calculate signed angle between vectors u and v about given axis"""
    cross = np.cross(u, v)
    angle = np.arccos(np.clip(np.dot(u / np.linalg.norm(u), v / np.linalg.norm(v)), -1.0, 1.0))
    return np.sign(np.dot(axis, cross)) * angle


def interpolate_condyle_arc(MDC, MPC, LDC, LPC, Z_axis, t):
    """Returns interpolated points Q_med (medial) and Q_lat (lateral) at parameter t"""
    Z_axis = Z_axis / np.linalg.norm(Z_axis)

    # Medial condyle calculations
    h_MDC = np.dot(MDC, Z_axis)
    C_M = h_MDC * Z_axis
    vec_MDC = MDC - C_M
    vec_MPC = MPC - C_M
    r_M = (np.linalg.norm(vec_MDC) + np.linalg.norm(vec_MPC)) / 2
    u_M = vec_MDC / np.linalg.norm(vec_MDC)
    w_M = np.cross(Z_axis, u_M)
    theta_M = signed_angle(vec_MDC, vec_MPC, Z_axis)  # Signed angle

    # Lateral condyle calculations
    h_LDC = np.dot(LDC, Z_axis)
    C_L = h_LDC * Z_axis
    vec_LDC = LDC - C_L
    vec_LPC = LPC - C_L
    r_L = (np.linalg.norm(vec_LDC) + np.linalg.norm(vec_LPC)) / 2
    u_L = vec_LDC / np.linalg.norm(vec_LDC)
    w_L = np.cross(Z_axis, u_L)
    theta_L = signed_angle(vec_LDC, vec_LPC, Z_axis)  # Signed angle

    # Calculate points
    Q_med = C_M + r_M * (np.cos(t * theta_M) * u_M + np.sin(t * theta_M) * w_M)
    Q_lat = C_L + r_L * (np.cos(t * theta_L) * u_L + np.sin(t * theta_L) * w_L)

    return Q_med, Q_lat


async def AlignmentAngles_Knee(marking, websocket):
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D
    from scipy.spatial.transform import Rotation as R
    # Count occurrences
    from collections import Counter

    try:
        from datetime import datetime
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")

        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f'femur_dict  {femur_dict}')
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        with open(pickle_path_femur_hc, "rb") as handle:
            HC = np.array(pickle.load(handle))
        print("Femur HC variables loaded successfully")

        # Tibia variables
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_tc_handle:
            tibia_tc = pickle.load(tibia_tc_handle)

        pickle_path_tibia = os.path.join(base_dir, "tibia_variables.pickle")
        with open(pickle_path_tibia, "rb") as tibia_handle:
            tibia_dict = pickle.load(tibia_handle)
        print(f'tibia_dict  {tibia_dict}')
    except Exception as e:
        print(f"Error loading femur or tibia variables: {e}")
        return -1

    result_dict = {angle: {"left": 0, "right": 0} for angle in range(-10, 150, 10)}
    GetRoboticArmInitialData(scalling=True)
    model_path = os.path.join(current_dir, "..", "..", "robotic_calib_data", "robot_to_led_model.pkl")
    model = joblib.load(model_path)

    # predicted = model.predict(point)
    while True:
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if not (len(detections_right) == 6 and len(detections_left) == 6):
                await asyncio.sleep(0.1)
                continue
            if page not in ['inner-page6.html','revinner-page6.html', "revgraph-screen.html", "femur-graph-screen.html"]:
                return

            detections_right = sorted(
                detections_right, key=lambda detection: detection[0]
            )
            detections_left = sorted(
                detections_left, key=lambda detection: detection[0]
            )
            femure = TriangleTracker(
                detections_right[:3],
                detections_left[:3],
                mode="F",
            )

            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            MDC = utils.find_new_point_location(
                femur_dict["MDC"]["Plane"], femur_plane, femur_dict["MDC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            MPC = utils.find_new_point_location(
                femur_dict["MPC"]["Plane"], femur_plane, femur_dict["MPC"]["C"]
            )
            LPC = utils.find_new_point_location(
                femur_dict["LPC"]["Plane"], femur_plane, femur_dict["LPC"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            tebia = TriangleTracker(
                detections_right[3:],
                detections_left[3:],
                mode="T",
            )
            T_Points = tebia.getLEDcordinates()
            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
            else:
                tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]
            tibia_plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
            )
            tibia_plane[2] = TC
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tibia_plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tibia_plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tibia_plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tibia_plane, tibia_dict["LM"]["C"]
            )
            ANC = (np.array(MM) + np.array(LM)) / 2

            femurbone_x_axis = FC - HC
            norm_femurbone_x_axis = np.linalg.norm(femurbone_x_axis)
            if norm_femurbone_x_axis < 1e-6:
                raise ValueError("Degenerate X axis: points too close or colinear in X")
            femurbone_x_axis /= norm_femurbone_x_axis

            femurbone_z_axis = ME - LE
            norm_femurbone_z_axis = np.linalg.norm(femurbone_z_axis)
            if norm_femurbone_z_axis < 1e-6:
                raise ValueError("Degenerate Z axis: points too close or colinear in Z")
            femurbone_z_axis /= norm_femurbone_z_axis

            # Recalculate Y to ensure orthogonality
            femurbone_y_axis = np.cross(femurbone_z_axis, femurbone_x_axis)
            norm_femurbone_y_axis = np.linalg.norm(femurbone_y_axis)
            if norm_femurbone_y_axis < 1e-6:
                raise ValueError("Degenerate Y axis: points are colinear or coplanar")
            femurbone_y_axis /= norm_femurbone_y_axis
            # Rotation matrix: columns are axes

            femurbone_z_axis = np.cross(femurbone_y_axis, femurbone_x_axis)
            femurbone_z_axis /= np.linalg.norm(femurbone_z_axis)
            R_F = np.column_stack((femurbone_x_axis, femurbone_y_axis, femurbone_z_axis))
            ###########################################################################
            tibiabone_x_axis = ANC - TC
            norm_tibiabone_x_axis = np.linalg.norm(tibiabone_x_axis)
            if norm_tibiabone_x_axis < 1e-6:
                raise ValueError("Degenerate X axis: points too close or colinear in X")
            tibiabone_x_axis /= norm_tibiabone_x_axis

            tibiabone_z_axis = MC - LC
            norm_tibiabone_z_axis = np.linalg.norm(tibiabone_z_axis)
            if norm_tibiabone_z_axis < 1e-6:
                raise ValueError("Degenerate Z axis: points too close or colinear in Z")
            tibiabone_z_axis /= norm_tibiabone_z_axis

            # Recalculate Y to ensure orthogonality
            tibiabone_y_axis = np.cross(tibiabone_z_axis, tibiabone_x_axis)
            norm_tibiabone_y_axis = np.linalg.norm(tibiabone_y_axis)
            if norm_tibiabone_y_axis < 1e-6:
                raise ValueError("Degenerate Y axis: points are colinear or coplanar")
            tibiabone_y_axis /= norm_tibiabone_y_axis
            # Rotation matrix: columns are axes

            tibiabone_z_axis = np.cross(tibiabone_y_axis, tibiabone_x_axis)
            tibiabone_z_axis /= np.linalg.norm(tibiabone_z_axis)
            R_T = np.column_stack((tibiabone_x_axis, tibiabone_y_axis, tibiabone_z_axis))

            R_rel = R_T @ R_F.T

            # Check if R_rel is a proper rotation matrix (orthogonal & det=1)
            if not np.allclose(np.linalg.det(R_rel), 1.0, atol=1e-3):
                raise ValueError("Rotation matrix determinant not close to 1")

            # Try converting to Euler angles
            try:
                euler_angles = R.from_matrix(R_rel).as_euler('xyz', degrees=True)
            except Exception as e:
                raise RuntimeError(f"Euler angle extraction failed: {str(e)}")

            # print(f'F_points {F_points}')
            a = distance(F_points[0], F_points[1])  # 64 mm
            b = distance(F_points[1], F_points[2])  # 113.6 mm
            c = distance(F_points[2], F_points[0])  # 111.26 mm
            # Real-world side lengths in mm
            real_a = 64.0
            real_b = 113.6
            real_c = 111.26

            # Heron's formula - camera space
            s_cam = (a + b + c) / 2
            area_cam = np.sqrt(s_cam * (s_cam - a) * (s_cam - b) * (s_cam - c))

            # Heron's formula - real-world space
            s_real = (real_a + real_b + real_c) / 2
            area_real = np.sqrt(s_real * (s_real - real_a) * (s_real - real_b) * (s_real - real_c))

            # Scale factor (mm per unit)
            scale = np.sqrt(area_real / area_cam)

            # # Sample coordinates (in mm)
            # MDC = np.array([-25.3, 12.7, 45.0])  # Medial distal
            # MPC = np.array([-26.1, -8.4, 45.2])  # Medial posterior
            # LDC = np.array([24.8, 13.2, 45.1])  # Lateral distal
            # LPC = np.array([25.7, -7.9, 45.3])  # Lateral posterior
            TEA = ME - LE
            print(f'MDC {MDC} MPC {MPC} LDC {LDC} LPC {LPC}')
            # print("MEDIAL PATH POINTS (Y should decrease):")
            # for i, t in enumerate(np.linspace(0, 1, 9), 1):
            #     Q_med, _ = interpolate_condyle_arc(MDC, MPC, LDC, LPC, TEA, t)
            #     print(f"Point {i}: {np.round(Q_med, 2)}")
            #
            # print("\nLATERAL PATH POINTS (Y should decrease):")
            # for i, t in enumerate(np.linspace(0, 1, 9), 1):
            #     _, Q_lat = interpolate_condyle_arc(MDC, MPC, LDC, LPC, TEA, t)
            #     print(f"Point {i}: {np.round(Q_lat, 2)}")
            # Prepare lists
            Q_med_list, Q_lat_list = [], []

            # Interpolate and store points
            for t in np.linspace(0, 1, 9):
                Q_med, Q_lat = interpolate_condyle_arc(MDC, MPC, LDC, LPC, TEA, t)
                Q_med_list.append(Q_med)
                Q_lat_list.append(Q_lat)

            # Reverse X values only
            x_med_reversed = [q[0] for q in Q_med_list][::-1]
            x_lat_reversed = [q[0] for q in Q_lat_list][::-1]

            # Create dictionaries
            medial_points = {
                i + 1: np.array([x_med_reversed[i], Q_med_list[i][1], Q_med_list[i][2]])
                for i in range(9)
            }

            lateral_points = {
                i + 1: np.array([x_lat_reversed[i], Q_lat_list[i][1], Q_lat_list[i][2]])
                for i in range(9)
            }

            if -120 <= euler_angles[2] <= -10:  # Corrected condition
                rom_key = int(round(-euler_angles[2] / 10.0) * 10)

                if rom_key % 10 == 0:
                    if rom_key == 0:
                        d2 = utils.distance_3d(MDC, MC) * scale
                        d1 = utils.distance_3d(LDC, LC) * scale
                    else:
                        selected_point = rom_key // 10
                        if 1 <= selected_point <= 9:
                            d2 = utils.distance_3d(medial_points[selected_point], MC) * scale - 15
                            d1 = utils.distance_3d(lateral_points[selected_point], LC) * scale - 15
                        else:
                            print("Invalid key:", selected_point)
                            continue

                    # direction_vector = ANC - TC
                    #
                    # distance_ME_to_MC_plane = compute_distance_to_plane(ME, MC, direction_vector)
                    #
                    # # Step 3: Distance from point B to plane through LC
                    # distance_LE_to_LC_plane = compute_distance_to_plane(LE, LC, direction_vector)
                    #
                    # # d1 = distance(medial_points[rom_key], MC) * scale
                    # # d2 = distance(lateral_points[rom_key], LC) * scale
                    #
                    # d1 = distance_ME_to_MC_plane * scale
                    # d2 = distance_LE_to_LC_plane * scale

                    result_dict[rom_key] = {"left": -d2, "right": d1}

                    # Send updated data to the frontend
                result_dict = {
                    key: value
                    for key, value in result_dict.items()
                    if value["left"] != 0 and value["right"] != 0
                }
            ###################################################################################################
            # data = {0: {'left': -10, 'right': 10}, 10: {'left': -10, 'right': 10}, 20: {'left': -9, 'right': 9},
            #         30: {'left': -10, 'right': 10},
            #         40: {'left': -10, 'right': 10}, 50: {'left': -10, 'right': 10}, 60: {'left': -9, 'right': 9},
            #         70: {'left': -10, 'right': 10}, 80: {'left': -11, 'right': 11}, 90: {'left': -10, 'right': 10},
            #         100: {'left': -10, 'right': 10}, 110: {'left': 0, 'right': 0}, 120: {'left': 0, 'right': 0}}
            # result_dict = {key: value for key, value in data.items() if
            #                value['left'] != 0 and value['right'] != 0}
            ###################################################################################################

            parent_dict = {
                "Graph_data": {
                    "filtered_results": {
                        key: value
                        for key, value in result_dict.items()
                        if value["left"] != 0 and value["right"] != 0
                    }
                },
                "angles": {
                    "Alignement": abs(int(euler_angles[1])),
                    "Anteversion": int(-euler_angles[2])
                },
            }
            print(f"parent_dict {parent_dict}")

            parent_dict = {
                'Graph_data': {
                    'filtered_results': {
                        90: {'left': -10.12736377895172, 'right': 19.000474002792558},
                        80: {'left': -11.213145160881265, 'right': 14.827290162794288},
                        70: {'left': -11.029552451545987, 'right': 14.910727256012457},
                        60: {'left': -11.09909770361232, 'right': 12.187526557167953},
                        50: {'left': -12.17456652332109, 'right': 12.075808046447683},
                        40: {'left': -13.185671368987487, 'right': 13.362051331235316},
                        30: {'left': -5.86687527405641, 'right': 16.49890774031986},
                        20: {'left': -15.989358356887578, 'right': 15.170693394128843},
                        10: {'left': -16.403372357124017, 'right': 15.576301011306255},
                    }
                },
                'angles': {
                    'Alignement': 23,
                    'Anteversion': 85
                }
            }

            await websocket.send_json(parent_dict)
            # await asyncio.sleep(0.1)
            # break
        except Exception as err:
            print(f"{__file__} e {err}")
            return


async def AlignmentAngles_KneeBackup(marking, websocket):
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D
    from scipy.spatial.transform import Rotation as R
    # Count occurrences
    from collections import Counter

    try:
        from datetime import datetime
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")

        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f'femur_dict  {femur_dict}')
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        with open(pickle_path_femur_hc, "rb") as handle:
            HC = np.array(pickle.load(handle))
        print("Femur HC variables loaded successfully")

        # Tibia variables
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_tc_handle:
            tibia_tc = pickle.load(tibia_tc_handle)

        pickle_path_tibia = os.path.join(base_dir, "tibia_variables.pickle")
        with open(pickle_path_tibia, "rb") as tibia_handle:
            tibia_dict = pickle.load(tibia_handle)
        print(f'tibia_dict  {tibia_dict}')
    except Exception as e:
        print(f"Error loading femur or tibia variables: {e}")
        return -1

    result_dict = {angle: {"left": 0, "right": 0} for angle in range(-10, 150, 10)}
    GetRoboticArmInitialData(scalling=True)
    model_path = os.path.join(current_dir, "..", "..", "robotic_calib_data", "robot_to_led_model.pkl")
    model = joblib.load(model_path)

    # predicted = model.predict(point)
    while True:
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if not (len(detections_right) == 6 and len(detections_left) == 6):
                await asyncio.sleep(0.1)
                continue
            if page not in ['inner-page6.html', "revgraph-screen.html", "femur-graph-screen.html"]:
                return

            detections_right = sorted(
                detections_right, key=lambda detection: detection[0]
            )
            detections_left = sorted(
                detections_left, key=lambda detection: detection[0]
            )
            femure = TriangleTracker(
                detections_right[:3],
                detections_left[:3],
                mode="F",
            )

            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            MDC = utils.find_new_point_location(
                femur_dict["MDC"]["Plane"], femur_plane, femur_dict["MDC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            MPC = utils.find_new_point_location(
                femur_dict["MPC"]["Plane"], femur_plane, femur_dict["MPC"]["C"]
            )
            LPC = utils.find_new_point_location(
                femur_dict["LPC"]["Plane"], femur_plane, femur_dict["LPC"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            tebia = TriangleTracker(
                detections_right[3:],
                detections_left[3:],
                mode="T",
            )
            T_Points = tebia.getLEDcordinates()
            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
            else:
                tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]
            tibia_plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
            )
            tibia_plane[2] = TC
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tibia_plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tibia_plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tibia_plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tibia_plane, tibia_dict["LM"]["C"]
            )
            ANC = (np.array(MM) + np.array(LM)) / 2

            femurbone_x_axis = FC - HC
            norm_femurbone_x_axis = np.linalg.norm(femurbone_x_axis)
            if norm_femurbone_x_axis < 1e-6:
                raise ValueError("Degenerate X axis: points too close or colinear in X")
            femurbone_x_axis /= norm_femurbone_x_axis

            femurbone_z_axis = ME - LE
            norm_femurbone_z_axis = np.linalg.norm(femurbone_z_axis)
            if norm_femurbone_z_axis < 1e-6:
                raise ValueError("Degenerate Z axis: points too close or colinear in Z")
            femurbone_z_axis /= norm_femurbone_z_axis

            # Recalculate Y to ensure orthogonality
            femurbone_y_axis = np.cross(femurbone_z_axis, femurbone_x_axis)
            norm_femurbone_y_axis = np.linalg.norm(femurbone_y_axis)
            if norm_femurbone_y_axis < 1e-6:
                raise ValueError("Degenerate Y axis: points are colinear or coplanar")
            femurbone_y_axis /= norm_femurbone_y_axis
            # Rotation matrix: columns are axes

            femurbone_z_axis = np.cross(femurbone_y_axis, femurbone_x_axis)
            femurbone_z_axis /= np.linalg.norm(femurbone_z_axis)
            R_F = np.column_stack((femurbone_x_axis, femurbone_y_axis, femurbone_z_axis))
            ###########################################################################
            tibiabone_x_axis = ANC - TC
            norm_tibiabone_x_axis = np.linalg.norm(tibiabone_x_axis)
            if norm_tibiabone_x_axis < 1e-6:
                raise ValueError("Degenerate X axis: points too close or colinear in X")
            tibiabone_x_axis /= norm_tibiabone_x_axis

            tibiabone_z_axis = MC - LC
            norm_tibiabone_z_axis = np.linalg.norm(tibiabone_z_axis)
            if norm_tibiabone_z_axis < 1e-6:
                raise ValueError("Degenerate Z axis: points too close or colinear in Z")
            tibiabone_z_axis /= norm_tibiabone_z_axis

            # Recalculate Y to ensure orthogonality
            tibiabone_y_axis = np.cross(tibiabone_z_axis, tibiabone_x_axis)
            norm_tibiabone_y_axis = np.linalg.norm(tibiabone_y_axis)
            if norm_tibiabone_y_axis < 1e-6:
                raise ValueError("Degenerate Y axis: points are colinear or coplanar")
            tibiabone_y_axis /= norm_tibiabone_y_axis
            # Rotation matrix: columns are axes

            tibiabone_z_axis = np.cross(tibiabone_y_axis, tibiabone_x_axis)
            tibiabone_z_axis /= np.linalg.norm(tibiabone_z_axis)
            R_T = np.column_stack((tibiabone_x_axis, tibiabone_y_axis, tibiabone_z_axis))

            R_rel = R_T @ R_F.T

            # Check if R_rel is a proper rotation matrix (orthogonal & det=1)
            if not np.allclose(np.linalg.det(R_rel), 1.0, atol=1e-3):
                raise ValueError("Rotation matrix determinant not close to 1")

            # Try converting to Euler angles
            try:
                euler_angles = R.from_matrix(R_rel).as_euler('xyz', degrees=True)
            except Exception as e:
                raise RuntimeError(f"Euler angle extraction failed: {str(e)}")

            a = distance(F_points[0], F_points[1])
            b = distance(F_points[1], F_points[2])
            c = distance(F_points[2], F_points[0])

            # Identify the unique (base) and repeated (equal legs) lengths
            sides = [a, b, c]
            sides_rounded = np.round(sides, decimals=5)  # for floating point stability

            counted = Counter(sides_rounded)
            leg_length, base_length = 0, 0
            for length, count in counted.items():
                if count == 2:
                    leg_length = length
                elif count == 1:
                    base_length = length

            real_base_length = 64  # for example, 50 mm in real world

            # # Calculate scale factor (real-world units per unit in camera space)
            scale = real_base_length / base_length

            if -120 <= euler_angles[2] <= -10:  # Corrected condition
                rom_key = int(-euler_angles[2])
                if rom_key % 10 == 0:
                    direction_vector = ANC - TC

                    distance_ME_to_MC_plane = compute_distance_to_plane(ME, MC, direction_vector)

                    # Step 3: Distance from point B to plane through LC
                    distance_LE_to_LC_plane = compute_distance_to_plane(LE, LC, direction_vector)

                    # d1 = distance(medial_points[rom_key], MC) * scale
                    # d2 = distance(lateral_points[rom_key], LC) * scale

                    d1 = distance_ME_to_MC_plane * scale
                    d2 = distance_LE_to_LC_plane * scale
                    result_dict[rom_key] = {"left": -d2, "right": d1}

                    # Send updated data to the frontend
                result_dict = {
                    key: value
                    for key, value in result_dict.items()
                    if value["left"] != 0 and value["right"] != 0
                }

            ###################################################################################################
            # data = {0: {'left': -10, 'right': 10}, 10: {'left': -10, 'right': 10}, 20: {'left': -9, 'right': 9},
            #         30: {'left': -10, 'right': 10},
            #         40: {'left': -10, 'right': 10}, 50: {'left': -10, 'right': 10}, 60: {'left': -9, 'right': 9},
            #         70: {'left': -10, 'right': 10}, 80: {'left': -11, 'right': 11}, 90: {'left': -10, 'right': 10},
            #         100: {'left': -10, 'right': 10}, 110: {'left': 0, 'right': 0}, 120: {'left': 0, 'right': 0}}
            # result_dict = {key: value for key, value in data.items() if
            #                value['left'] != 0 and value['right'] != 0}
            ###################################################################################################
            print(f"*" * 100)
            print(f"{scale}")
            print(f"*" * 100)
            parent_dict = {
                "Graph_data": {
                    "filtered_results": {
                        key: value
                        for key, value in result_dict.items()
                        if value["left"] != 0 and value["right"] != 0
                    }
                },
                "angles": {
                    "Alignement": abs(int(euler_angles[1])),
                    "Anteversion": int(-euler_angles[2])
                },
            }
            print(f"parent_dict {parent_dict}")
            await websocket.send_json(parent_dict)
            # await asyncio.sleep(0.1)
            # break
        except Exception as err:
            print(f"{__file__} e {err}")
            return


async def EulerAngles(marking, websocket):
    from scipy.spatial.transform import Rotation as R

    def construct_local_axes(pts):
        pts = np.array(pts)
        if pts.shape != (3, 3):
            raise ValueError("Input must be 3 points with 3D coordinates each")

        mid_idx = np.argmin(pts[:, 1])
        mid = pts[mid_idx]
        remaining = [pts[i] for i in range(3) if i != mid_idx]

        # Step 2: Sort remaining two by X to get low and high
        low, high = sorted(remaining, key=lambda p: p[0])
        print(f'mid {mid}   low {low} high {high}')
        # X-axis from low to high
        x_axis = high - low
        norm_x = np.linalg.norm(x_axis)
        if norm_x < 1e-6:
            raise ValueError("Degenerate X axis: points too close or colinear in X")
        x_axis /= norm_x

        # Midpoint of x-axis
        x_mid = (low + high) / 2.0
        y_axis = mid - x_mid
        norm_y = np.linalg.norm(y_axis)
        if norm_y < 1e-6:
            raise ValueError("Degenerate Y axis: middle point is aligned with X axis")
        y_axis /= norm_y

        # Z-axis = X × Y
        z_axis = np.cross(x_axis, y_axis)
        norm_z = np.linalg.norm(z_axis)
        if norm_z < 1e-6:
            raise ValueError("Degenerate Z axis: points are colinear or coplanar")
        z_axis /= norm_z

        # Recalculate Y to ensure orthogonality
        y_axis = np.cross(z_axis, x_axis)
        y_axis /= np.linalg.norm(y_axis)

        # Rotation matrix: columns are axes
        R_mat = np.column_stack((x_axis, y_axis, z_axis))
        return R_mat

    def compute_transform(F_points, T_points):
        try:
            R_F = construct_local_axes(F_points)
            R_T = construct_local_axes(T_points)

            # Relative rotation from F → T
            R_rel = R_T @ R_F.T

            # Check if R_rel is a proper rotation matrix (orthogonal & det=1)
            if not np.allclose(np.linalg.det(R_rel), 1.0, atol=1e-3):
                raise ValueError("Rotation matrix determinant not close to 1")

            # Try converting to Euler angles
            try:
                euler_angles = R.from_matrix(R_rel).as_euler('xyz', degrees=True)
            except Exception as e:
                raise RuntimeError(f"Euler angle extraction failed: {str(e)}")

            # Compute translation between origins (centroids)
            F_center = np.mean(F_points, axis=0)
            T_center = np.mean(T_points, axis=0)
            translation = T_center - F_center

            return {
                'rotation_matrix': R_rel,
                'euler_angles_xyz_deg': euler_angles,
                'translation_vector': translation
            }

        except Exception as err:
            return {'error': str(err)}

    while True:
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)

            if not (len(detections_right) == 6 and len(detections_left) == 6):
                await asyncio.sleep(0.1)
                print(f' {__file__} {len(detections_right)} {len(detections_left)}')
                continue

            detections_right = sorted(
                detections_right, key=lambda detection: detection[0]
            )
            detections_left = sorted(
                detections_left, key=lambda detection: detection[0]
            )
            femure = TriangleTracker(
                detections_right[:3],
                detections_left[:3],
            )

            F_points = femure.getLEDcordinates()
            print(f'F_points {F_points}')
            # await asyncio.sleep(1)
            # continue
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            tebia = TriangleTracker(
                detections_right[3:],
                detections_left[3:],
            )
            T_Points = tebia.getLEDcordinates()
            # print(f' T_Points {T_Points}')

            print(f'F_points {F_points}')
            print(f'T_Points {T_Points}')

            result = compute_transform(F_points, T_Points)

            if 'error' in result:
                print("❌ Error:", result['error'])
            else:
                print("✅ Rotation Matrix:\n", result['rotation_matrix'])
                print("✅ Euler Angles (XYZ, degrees):", result['euler_angles_xyz_deg'])
                print("✅ Translation Vector:", result['translation_vector'])

            await asyncio.sleep(1)
            # break
        except Exception as err:
            print(f"{__file__} e {err}")
            return


async def Knee_tooltipcalibration(marking, websocket, tool=None):
    led_frames = []

    while True:
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if page != 'Calibration':
                return
            if not (len(detections_right) == 3 and len(detections_left) == 3):
                await asyncio.sleep(0.1)
                # print(f'{len(detections_right)} {len(detections_left)}')
                continue

            detections_right = sorted(
                detections_right, key=lambda detection: detection[0]
            )
            detections_left = sorted(
                detections_left, key=lambda detection: detection[0]
            )
            # print(f'before sorting detections_right {detections_right} detections_left {detections_left}')

            tracker = TriangleTracker(
                detections_right,
                detections_left,
            )

            # asyncio.sleep(0.25)
            # continue
            led_positions = tracker.getLEDcordinates()
            # print(f"frames led_positions {led_positions}")
            # await asyncio.sleep(0.25)
            # continue
            led_frames.append(led_positions)
            print(f"Captured {len(led_frames)} frames led_positions {led_positions}")

            if len(led_frames) >= 50:  # Calibrate after collecting 30 frames
                tip_local, model_leds = pivot_calibration(led_frames)
                save_calibration(tip_local, model_leds, filename=tool)
                print("Calibration completed and saved.")
                # await websocket.send("tooltip_calibrated")
                break  # stop calibration loop
            await asyncio.sleep(0.2)
        except Exception as err:
            print(f"{__file__} error: {err}")
            return


async def robot_offset_calibration(marking, websocket, tool='robot_offset'):
    led_frames = []

    while True:
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if page != 'robot_tooltipCalibration':
                return
            # print(f'{len(detections_right)} {len(detections_left)}')
            if not (len(detections_right) == 3 and len(detections_left) == 3):
                await asyncio.sleep(0.1)
                continue

            detections_right = sorted(
                detections_right, key=lambda detection: detection[0]
            )
            detections_left = sorted(
                detections_left, key=lambda detection: detection[0]
            )
            # print(f'before sorting detections_right {detections_right} detections_left {detections_left}')

            tracker = TriangleTracker(
                detections_right,
                detections_left,
            )
            model_path = os.path.join(current_dir, "..", "..", "robotic_calib_data", "robot_to_led_model.pkl")
            model = joblib.load(model_path)

            # === Get current LED point from tracker
            pointer_tip_gcs = tracker.getStylusPoint()  # 3D point in optical space
            print(f' pointer_tip_gcs  {pointer_tip_gcs}')
            await asyncio.sleep(0.2)
            continue
            # === Apply same axis swap as in training
            pointer_tip_swapped = pointer_tip_gcs.copy()
            pointer_tip_swapped[1], pointer_tip_swapped[2] = pointer_tip_swapped[2], pointer_tip_swapped[1]

            # === Predict robot coordinates
            projection_point = np.array(pointer_tip_swapped).reshape(1, -1)
            robotic_position_led_coord = model.predict(projection_point)[0]

            # === Print LED position in robot's coordinate system
            print(f"LED optical point (original): {pointer_tip_gcs}")
            print(f"Mapped to robot coordinate system: {robotic_position_led_coord}")

            arm = XArmAPI(arm_ip)  # Replace with your xArm6 IP

            if arm.connected:
                position = arm.get_position()
                if position[0] == 0:  # Success check
                    x, y, z, roll, pitch, yaw = position[1]

                    # Compute translated vector (LED position in robot frame - actual robot position)
                    robot_position = np.array([x, y, z])
                    translated_vector = robotic_position_led_coord - robot_position

                    print(f"LED (in robot coords): {robotic_position_led_coord}")
                    print(f"Robot current position: {robot_position}")
                    print(f"→ Translated vector (LED - Robot): {translated_vector}")

                    # Optional: Save or send this translation
                    # await websocket.send("exit")
                    return  # stop calibration loop
                else:
                    print("Failed to get robot position.")
            else:
                print("Failed to connect to robot.")
            # return
        except Exception as err:
            print(f"{__file__} error: {err}")
            return


#
#
# async def mark_new_point(marking, websocket, tool=None):
#
#     tip_local, model_leds = load_calibration(filename=tool)
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#     MARK_FILE = os.path.join(
#         current_dir, "..", "..", "registration_data", f"Marking_{timestamp}.csv"
#     )
#
#
#     with open(MARK_FILE, mode='w', newline='') as file:
#         writer = csv.writer(file)
#         writer.writerow(["X", "Y", "Z"])  # CSV header
#
#     count = 0
#
#     while True:
#         try:
#             if page != 'pointer.html':
#                 return
#             detections_left, detections_right = await marking.handle_irq_average(n=10)
#             if len(detections_left) != 3 or len(detections_right) != 3:
#                 await asyncio.sleep(0.1)
#                 continue
#
#             detections_left = sorted(detections_left, key=lambda d: d[0])
#             detections_right = sorted(detections_right, key=lambda d: d[0])
#
#             femur = TriangleTracker(detections_right, detections_left)
#             current_leds = femur.getLEDcordinates()
#
#             tip_global = estimate_tip_position(current_leds, model_leds, tip_local)
#             print(f"Marked point: {tip_global}")
#             writer.writerow([round(x, 8) for x in tip_global])
#             data = { "x": 1, "y": 2, "z": 3 }
#             await websocket.send_json(data)
#             utils.play_notification_sound()
#             count = count + 1
#             if count == 25:
#                 break
#         except Exception as e:
#             print(f"Error in mark_new_point: {e}")
#             await asyncio.sleep(0.5)


async def mark_new_point(marking, websocket, tool=None):
    tip_local, model_leds = load_calibration(filename=tool)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    current_dir = os.path.dirname(os.path.abspath(__file__))  # Ensure current_dir is defined
    MARK_FILE = os.path.join(current_dir, "..", "..", "registration_data", f"Marking_{timestamp}.csv")
    os.makedirs(os.path.dirname(MARK_FILE), exist_ok=True)

    count = 0

    try:
        with open(MARK_FILE, mode='w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(["X", "Y", "Z"])  # CSV header

            while True:
                try:
                    if page != 'pointer.html':
                        print("Exiting mark_new_point: page is not 'pointer.html'")
                        break

                    detections_left, detections_right = await marking.handle_irq_average(n=10)
                    if len(detections_left) != 3 or len(detections_right) != 3:
                        await asyncio.sleep(0.1)
                        continue

                    detections_left = sorted(detections_left, key=lambda d: d[0])
                    detections_right = sorted(detections_right, key=lambda d: d[0])

                    femur = TriangleTracker(detections_right, detections_left)
                    current_leds = femur.getLEDcordinates()
                    #print(current_leds)     # for print robot Tracker points after calibratin  ####################################################################################

                    tip_global = estimate_tip_position(current_leds, model_leds, tip_local)
                    print(f"Marked point: {tip_global}")

                    # Write point to file
                    writer.writerow([round(x, 8) for x in tip_global])
                    file.flush()

                    # Send dummy data via WebSocket
                    data = {"x": tip_global[0], "y": tip_global[1], "z": tip_global[2]}
                    await websocket.send_json(data)

                    utils.play_notification_sound()
                    count += 1
                    if count == 25:
                        print("25 points marked, exiting.")
                        break

                except Exception as e:
                    print(f"Inner error in mark_new_point loop: {e}")
                    await asyncio.sleep(0.5)

    except Exception as e:
        print(f"File open/write error in mark_new_point: {e}")


async def primary_kneeTibiaCut(marking, websocket):
    print("Entered in Tebia procecc")
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)

        try:
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_tc = pickle.load(tibia_handle)
                print(f" tibia_tc {tibia_tc}")
            # Tibia TC variables
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_dict = pickle.load(tibia_handle)
                print(f" tibia_dict {tibia_dict}")

        except:
            print("Error loading femur variables")

        omega = utils.GetplanningInput(filename="tibia_input1")
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        while True:
            try:
                arm = XArmAPI(arm_ip)
                arm.connect()
                if arm.connected:
                    break
            except Exception as e:
                print(f'Robot is not Enable or Power off')
                await asyncio.sleep(1)
    while True:
        try:

            while True:
                if page != "femur-inner-page2.html":
                    return
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:

                    await asyncio.sleep(0.2)
                    continue

            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            three_right = detections_right[0:3]
            three_left = detections_left[0:3]
            # three_right = sorted(three_right, key=lambda x: x[0])
            # three_left = sorted(three_left, key=lambda x: x[0])

            Pointer = TriangleTracker(
                three_right, three_left
            )
            R_points = Pointer.getLEDcordinates()

            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            # detections_right = detections_right[3:]
            # detections_left = detections_left[3:]

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[3:6]
            first_three_left = detections_left[3:6]
            next_three_right = detections_right[-3:]
            next_three_left = detections_left[-3:]
            femure = TriangleTracker(
                first_three_right, first_three_left, "F"
            )

            F_points = femure.getLEDcordinates()

            Tebia = TriangleTracker(
                next_three_right, next_three_left, "T"
            )

            T_points = Tebia.getLEDcordinates()
            if T_points[1][1] >= T_points[0][1]:
                tebia_Plane = [T_points[2], T_points[0], T_points[1]]
            else:
                tebia_Plane = [T_points[2], T_points[1], T_points[0]]
            # print(f'  tebia_Plane original  {tebia_Plane}')
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tracker_plane,
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)

            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=Newtranslated_plane,
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )
            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            tebia_Plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tebia_Plane, tibia_tc["TC"]["C"]
            )

            tebia_Plane[2] = TC
            # print(f'  tebia_Plane  {tebia_Plane}')
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tebia_Plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tebia_Plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tebia_Plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tebia_Plane, tibia_dict["LM"]["C"]
            )
            ANC = (np.array(MM) + np.array(LM)) / 2

            a = distance(T_points[0], T_points[1])
            b = distance(T_points[1], T_points[2])
            c = distance(T_points[2], T_points[0])
            # Identify the unique (base) and repeated (equal legs) lengths
            sides = [a, b, c]
            sides_rounded = np.round(sides, decimals=5)  # for floating point stability
            counted = Counter(sides_rounded)
            leg_length, base_length = 0, 0
            for length, count in counted.items():
                if count == 2:
                    leg_length = length
                elif count == 1:
                    base_length = length
            real_base_length = 64  # for example, 50 mm in real world
            # Calculate scale factor (real-world units per unit in camera space)
            scale = real_base_length / base_length

            cutting_points_scalling = 10 / scale
            direction_vector = TC - ANC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling

            # Calculate the projection point
            #
            # TC = np.array([570.24474024 ,489.94027216, 323.00401793])
            projection_point = TC - scaled_unit_vector
            print(f'projection_point {projection_point} ANC  {ANC}  MM {MM} LM {LM}  TC {TC}')
            Mid_point_RoboTracker = (
                                            np.array(tracker_plane[1]) + np.array(tracker_plane[2])
                                    ) / 2
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]
            v1 = TC - ANC
            R_points = np.array(R_points)
            # R_points
            mid_idx = np.argmin(R_points[:, 1])
            mid = R_points[mid_idx]
            remaining = [R_points[i] for i in range(3) if i != mid_idx]

            # Step 2: Sort remaining two by X to get low and high
            low, high = sorted(remaining, key=lambda p: p[0])
            v2 = high - low
            R_rel = rotation_matrix_between_vectors(v1, v2)

            def rotation_angle_from_matrix(R):
                angle = np.arccos((np.trace(R) - 1) / 2)
                return np.degrees(angle)

            angle_deg1 = rotation_angle_from_matrix(R_rel)
            # print("Rotation angle (degrees):", angle_deg)

            x_mid = (low + high) / 2.0
            y_axis = mid - x_mid

            v3 = np.cross(v2, y_axis)
            # Calculate dot product and magnitudes
            dot_product = np.dot(v3, v1)
            magnitude_v3 = np.linalg.norm(v3)
            magnitude_tibia_x_axis = np.linalg.norm(v1)

            # Calculate the cosine of the angle
            cos_angle = dot_product / (magnitude_v3 * magnitude_tibia_x_axis)

            # Ensure the cosine value is within the valid range of -1 to 1 due to floating point errors
            cos_angle = np.clip(cos_angle, -1.0, 1.0)

            # Calculate the angle in radians
            angle_radians = np.arccos(cos_angle)

            # Optionally, convert to degrees
            varus = 90 - np.degrees(angle_radians)

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            try:
                flexion = abs(vector2_signed_angle_xy(RoboTracker_y, v1)) - 90
                # print(f'flexion {flexion}')
            except Exception as e:
                print(f"Error {e}")
                time.sleep(2)
            # projection_point=np.array([569.58733012, 493.52064494, 324.65579388])
            projection_point[2], projection_point[1] = (
                projection_point[1],
                projection_point[2],
            )
            projection_point = np.array(projection_point).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(projection_point)

            print(
                f" projection_point {projection_point} predicted_dest  {predicted_dest} varus ,flexion {varus} {flexion}"
            )
            if arm.connected and USE_ROBOT:
                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                # code = 0
                code = arm.set_position(
                    x=predicted_dest[0][0],
                    y=predicted_dest[0][1],
                    z=predicted_dest[0][2],
                    roll=90 + varus,
                    pitch=flexion,
                    yaw=90,
                    speed=ROBOT_SPEED,
                    mvacc=50,
                    wait=True,
                )
                if code == 0:
                    print("Successfully moved to the desired position!")
                    await asyncio.sleep(1)
                else:
                    print(f"Failed to move the arm. Error code: {code}")
            await asyncio.sleep(1)
            # return
        except Exception as e:
            print(f"Error {e}")


async def DistalFemureCutVerification(marking, websocket):
    try:
        await send_tuple(websocket, ("femur-inner-page5", 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    # omega = utils.GetplanningInput(filename="femure_input1")
    while True:
        try:
            if page != "femur-inner-page5.html":
                return
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 12 and len(detections_left) == 12:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    if page != "femur-inner-page5.html":
                        return
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            # Extract elements in groups of 3
            detection_groups = {
                # "R": (detections_right[:3], detections_left[:3]),  # Robot
                "F": (detections_right[3:6], detections_left[3:6]),  # Femur
                "V": (detections_right[6:9], detections_left[6:9]),  # Verification Tool
                # "T": (detections_right[9:12], detections_left[9:12]),  # Tibia
            }

            # Store the results
            tracked_points = {}

            for label, (right, left) in detection_groups.items():
                label_to_pass = "F" if label == "F" else "T"

                # Instantiate the tracker with the appropriate label
                tracker = TriangleTracker(right, left, label_to_pass)

                # Get the tracked points
                tracked_points[label] = tracker.getLEDcordinates()
            # Extract individual results
            # R_points = tracked_points["R"]
            F_points = tracked_points["F"]
            Tool_points = tracked_points["V"]
            # T_points = tracked_points["T"]
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            # print(f'F_points {F_points}')
            a = distance(F_points[0], F_points[1])  # 64 mm
            b = distance(F_points[1], F_points[2])  # 113.6 mm
            c = distance(F_points[2], F_points[0])  # 111.26 mm
            # Real-world side lengths in mm
            real_a = 64.0
            real_b = 113.6
            real_c = 111.26

            # Heron's formula - camera space
            s_cam = (a + b + c) / 2
            area_cam = np.sqrt(s_cam * (s_cam - a) * (s_cam - b) * (s_cam - c))

            # Heron's formula - real-world space
            s_real = (real_a + real_b + real_c) / 2
            area_real = np.sqrt(s_real * (s_real - real_a) * (s_real - real_b) * (s_real - real_c))

            # Scale factor (mm per unit)
            scale = np.sqrt(area_real / area_cam)

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )

            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )

            print(f' Tool_points {Tool_points}')
            Mid_Tool_points = (np.array(Tool_points[0]) + np.array(Tool_points[1])) / 2

            # Step 2: y vector calculation
            toolTracker_y = Mid_Tool_points - Tool_points[1]

            # Step 3: x vector calculation
            toolracker_x = np.array(Tool_points[2]) - np.array(Mid_Tool_points)

            # Step 4: Norms (just for debug, we won't use them for cross product)
            toolrackernorm_x = np.linalg.norm(toolracker_x)

            toolrackernorm_y = np.linalg.norm(toolTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            toolracker_z = np.cross(toolTracker_y, toolracker_x)

            # Step 6: FCS vectors
            x_FCS = FC - HC
            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, ME, LE):

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                ME_translated = ME - mid_point
                LE_translated = LE - mid_point

                # Step 5: Apply rotation (World to Local)
                ME_local = R.T @ ME_translated
                LE_local = R.T @ LE_translated

                # Step 6: Compute ME→LE vector in LCS
                V_local = LE_local - ME_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(toolTracker_y, x_FCS)
                varus = vector3_signed_angle(tracker=Tool_points, ME=ME, LE=LE)

                print(f"flexion  {flexion} varus {varus}")

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")

            # Scale values
            cutting_points_scalling = 50 / scale
            cutting_points = omega / scale

            # Primary direction and scaled projection
            direction_vector = HC - FC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points
            projection_point = LDC + scaled_unit_vector

            # Tool vector and its projected point
            direction_vector2 = Tool_points[0] - Mid_Tool_points
            unit_vector2 = direction_vector2 / np.linalg.norm(direction_vector2)
            projection_point2 = Mid_Tool_points + unit_vector2 * cutting_points_scalling

            # Plane normal and distance calculation
            normal_vector = np.cross(direction_vector, scaled_unit_vector)
            A, B, C = normal_vector
            D = -np.dot(normal_vector, projection_point)

            x1, y1, z1 = projection_point2
            distance_mm = abs(A * x1 + B * y1 + C * z1 + D) / np.linalg.norm(normal_vector)

            def round_to_two_decimals(value):
                return round(value, 2)

            distance_mm = round_to_two_decimals(distance_mm)
            print(
                f"Distance between projection_point2 and the plane in mm: {distance_mm:.2f} mm"
            )
            await send_tuple(
                websocket, ("femur-inner-page5", flexion, varus, distance_mm)
            )
            await asyncio.sleep(0.1)
        except Exception as e:
            print(f"Error {e}")


async def TibiaCutVerification(marking, websocket):
    try:
        await send_tuple(websocket, ("tibia-inner-page3", 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")

            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_tc = pickle.load(tibia_handle)
            # Tibia TC variables
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_dict = pickle.load(tibia_handle)
                print(f" tibia_dict {tibia_dict}")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    omega = utils.GetplanningInput(filename="tibia_input1")
    while True:
        try:
            if page != "tibia-inner-page3.html":
                return
            while True:
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 12 and len(detections_left) == 12:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    if page != "tibia-inner-page3.html":
                        return
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            # Extract elements in groups of 3
            detection_groups = {
                # "R": (detections_right[:3], detections_left[:3]),  # Robot
                "F": (detections_right[3:6], detections_left[3:6]),  # Femur
                "V": (detections_right[6:9], detections_left[6:9]),  # Verification Tool
                "T": (detections_right[9:12], detections_left[9:12]),  # Tibia
            }

            # Store the results
            tracked_points = {}

            # Iterate over detection groups and process each one
            for label, (right, left) in detection_groups.items():
                # Normalize label input
                normalized_label = "F" if label == "V" else label

                tracker = TriangleTracker(right, left, normalized_label)
                tracked_points[label] = tracker.getLEDcordinates()
            # Extract individual results
            # R_points = tracked_points["R"]
            F_points = tracked_points["F"]
            Tool_points = tracked_points["V"]
            T_points = tracked_points["T"]
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            if T_points[1][1] >= T_points[0][1]:
                tebia_Plane = [T_points[2], T_points[0], T_points[1]]
            else:
                tebia_Plane = [T_points[2], T_points[1], T_points[0]]

            # print(f'F_points {F_points}')
            a = distance(F_points[0], F_points[1])  # 64 mm
            b = distance(F_points[1], F_points[2])  # 113.6 mm
            c = distance(F_points[2], F_points[0])  # 111.26 mm
            # Real-world side lengths in mm
            real_a = 64.0
            real_b = 113.6
            real_c = 111.26

            # Heron's formula - camera space
            s_cam = (a + b + c) / 2
            area_cam = np.sqrt(s_cam * (s_cam - a) * (s_cam - b) * (s_cam - c))

            # Heron's formula - real-world space
            s_real = (real_a + real_b + real_c) / 2
            area_real = np.sqrt(s_real * (s_real - real_a) * (s_real - real_b) * (s_real - real_c))

            # Scale factor (mm per unit)
            scale = np.sqrt(area_real / area_cam)

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tebia_Plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tebia_Plane, tibia_tc["TC"]["C"]
            )

            tebia_Plane[2] = TC
            # print(f'  tebia_Plane  {tebia_Plane}')
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tebia_Plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tebia_Plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tebia_Plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tebia_Plane, tibia_dict["LM"]["C"]
            )
            ANC = (np.array(MM) + np.array(LM)) / 2

            print(f'Tool_points {Tool_points}')
            print(f'femur_plane {femur_plane}')
            print(f'tebia_Plane {tebia_Plane}')


            Mid_Tool_points = (np.array(Tool_points[0]) + np.array(Tool_points[1])) / 2

            # Step 2: y vector calculation
            toolTracker_y = Mid_Tool_points - Tool_points[1]

            # Step 3: x vector calculation
            toolracker_x = np.array(Mid_Tool_points) - np.array(Tool_points[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            toolrackernorm_x = np.linalg.norm(toolracker_x)

            toolrackernorm_y = np.linalg.norm(toolTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            toolracker_z = np.cross(toolTracker_y, toolracker_x)

            # Step 6: FCS vectors
            x_FCS = ANC - TC
            z_FCS = LC - MC

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, MC, LC):
                # print(f'*' * 100)
                # print(f'tracker {tracker}  ME {MC}  LE {LC}')
                # print(f'*' * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                MC_translated = MC - mid_point
                LC_translated = LC - mid_point

                # Step 5: Apply rotation (World to Local)
                MC_local = R.T @ MC_translated
                LC_local = R.T @ LC_translated

                # Step 6: Compute MC→LC vector in LCS
                V_local = LC_local - MC_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(toolTracker_y, x_FCS)
                varus = vector3_signed_angle(tracker=Tool_points, MC=MC, LC=LC)

                # print(f"flexion  {flexion}")


                await asyncio.sleep(1)
            except Exception as e:
                print(f"ang Error {e}")

            # Step 1: Compute scaled values
            cutting_points_scaling = 50 / scale
            cutting_points = omega / scale

            # Step 2: Direction vector from TC to ANC
            # print(f' TC {TC}  AC {ANC}')
            direction_vector = ANC - TC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points

            # Step 3: First projection point (on cutting line)
            projection_point = TC + scaled_unit_vector

            # Step 4: Tool direction and second projection point
            direction_vector2 = Tool_points[2] - Mid_Tool_points

            # print(f' Tool_points[2] {Tool_points[2]}  Mid_Tool_points {Mid_Tool_points}')

            unit_vector2 = direction_vector2 / np.linalg.norm(direction_vector2)
            projection_point2 = Tool_points[2] + unit_vector2 * cutting_points_scaling

            # Step 5: Plane normal from cross product
            normal_vector = np.cross(direction_vector, direction_vector2)

            # Check for degeneracy
            if np.linalg.norm(normal_vector) == 0:
                raise ValueError("Degenerate plane: direction vectors are collinear.")

            # Step 6: Normalize normal vector
            normal_unit = normal_vector / np.linalg.norm(normal_vector)

            # Step 7: Distance between the two parallel planes
            vector_between_points = projection_point2 - projection_point
            signed_distance = np.dot(vector_between_points, normal_unit) * scale
            distance_mm = round(abs(signed_distance), 2)

            print(f"flexion  {flexion} varus {varus}  distance_mm {distance_mm}")
            await send_tuple(
                websocket, ("tibia-inner-page3", flexion, distance_mm, varus)
            )
            await asyncio.sleep(0.1)
        except Exception as e:

            print(f"Error {e}")


import numpy as np


def create_triangle_model(A, B, C):
    """
    Creates a triangle model from points A, B, C by storing
    the relative vector from A to C in the triangle's local frame.

    Returns:
        A dictionary with triangle model info to be used later.
    """
    AB = B - A
    AC = C - A

    # Create triangle-local orthonormal basis
    x_axis = AB / np.linalg.norm(AB)
    z_axis = np.cross(AB, AC)
    z_axis /= np.linalg.norm(z_axis)
    y_axis = np.cross(z_axis, x_axis)

    # Project AC into this local basis
    local_coords_C = np.array([
        np.dot(AC, x_axis),
        np.dot(AC, y_axis),
        np.dot(AC, z_axis)
    ])

    return {
        "local_coords_C": local_coords_C,
        "A": A,
        "B": B
    }


def reconstruct_third_point(model, A_new, B_new):
    """
    Reconstructs the third point C' of the triangle using the model and new A', B'.
    """
    AB_new = B_new - A_new
    x_axis = AB_new / np.linalg.norm(AB_new)

    # Use original triangle to get correct orientation
    AB_orig = model["B"] - model["A"]
    AC_orig = model["local_coords_C"][0] * (AB_orig / np.linalg.norm(AB_orig))
    normal = np.cross(AB_orig, AC_orig)
    normal_unit = normal / np.linalg.norm(normal)
    y_axis = np.cross(normal_unit, x_axis)
    z_axis = np.cross(x_axis, y_axis)

    # Use saved local coordinates to reconstruct C'
    dx, dy, dz = model["local_coords_C"]
    C_new = A_new + dx * x_axis + dy * y_axis + dz * z_axis
    return C_new


async def primary_robot_position(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")

    except Exception as e:
        print(f"Error {e}")
    if USE_ROBOT:
        while True:
            try:
                arm = XArmAPI(arm_ip)
                arm.connect()
                if arm.connected:
                    break
            except Exception as e:
                print(f'Robot is not Enable or Power off')
                await asyncio.sleep(1)
    firsttime = False
    threshold_x = 35
    threshold_y = 150
    threshold_z = 25

    # Define your target position
    target_pose = (253, 537, 130, 140, -80, 53.6)

    def is_same_position(current, target, tolerance=1e-2):
        """Check if current and target poses are the same within a small tolerance."""
        return all(abs(c - t) < tolerance for c, t in zip(current, target))

    while True:
        try:
            if page not in ["robot_position.html" , "revrobot_position.html"]:
                return

            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 9 and len(detections_left) == 9:
                detections_right = sorted(
                    detections_right, key=lambda detection: detection[0]
                )
                detections_left = sorted(
                    detections_left, key=lambda detection: detection[0]
                )

                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                robot_tracker = TriangleTracker(
                    first_three_right,
                    first_three_left,
                )

                R_points = robot_tracker.getLEDcordinates()
                if R_points[1][1] >= R_points[0][1]:
                    tracker_plane = [R_points[2], R_points[0], R_points[1]]
                else:
                    tracker_plane = [R_points[2], R_points[1], R_points[0]]

                detections_left = detections_left[3:]
                detections_right = detections_right[3:]
                femure = TriangleTracker(
                    detections_right[:3],
                    detections_left[:3],
                    mode="F",
                )

                F_points = femure.getLEDcordinates()
                # print(f'F_points {F_points}')
                # return
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]

                FC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )

                centroid = utils.calculate_centroid(tracker_plane)
                FemureTrackerCentroid = utils.calculate_centroid(femur_plane)
                if not firsttime:
                    # Step 1: Build the triangle model
                    model = create_triangle_model(A=FC, B=FemureTrackerCentroid, C=HC)
                    firsttime = True

                # Step 3: Reconstruct C'
                new_HC = reconstruct_third_point(model, FC, FemureTrackerCentroid)
                diff = centroid - HC
                dx, dy, dz = diff

                # X-axis (Left/Right)
                if abs(dx) > threshold_x:
                    if dx > 0:
                        print(f"Move robot right {dx}-->")
                        await send_tuple(websocket, ('data', '<-- RIGHT', 5.6, 7.3))
                    else:
                        print(f"Move robot left {dx}-->")
                        await send_tuple(websocket, ('data', 'LEFT -->', 5.6, 7.3))

                # Y-axis (Table Up/Down)
                if abs(dy) > threshold_y:
                    if dy > 0:
                        print(f"Move table UP {dy}")
                        await send_tuple(websocket, ('data', 'TABLE UP ', 5.6, 7.3))
                    else:
                        print(f"Move table DOWN {dy}")
                        await send_tuple(websocket, ('data', 'TABLE DOWN', 5.6, 7.3))

                # Z-axis (Towards/Away from Camera)
                if abs(dz) > threshold_z:
                    if dz > 0:
                        print(f"Move towards Camera {dz}")
                        await send_tuple(websocket, ('data', 'TOWARDS CAMERA', 5.6, 7.3))
                    else:
                        print(f"Move away from Camera {dz}")
                        await send_tuple(websocket, ('data', 'AWAY FROM CAMERA', 5.6, 7.3))

                # If the differences are within threshold, print OK
                if abs(dx) <= threshold_x and abs(dy) <= threshold_y and abs(dz) <= threshold_z:
                    print("OK: C is within threshold and aligned with HC")
                    await send_tuple(websocket, ('data', 'OK', 5.6, 7.3))

                    if arm.connected and USE_ROBOT:

                        arm.motion_enable(enable=True)  # Enable motion
                        arm.set_mode(0)  # Set to position control mode
                        arm.set_state(0)  # Set to ready state
                        current_pose = arm.get_position(is_radian=False)  # Should return a tuple or list

                        if is_same_position(current_pose, target_pose):
                            print("Arm is already at the desired position. Skipping move.")
                        else:
                            ret_code = arm.set_servo_angle(servo_id=1, angle=90)
                            if ret_code != 0:
                                print(f"Error: Failed to move J1. Error code: {ret_code}")
                                # Optionally, handle the error (retry, abort, log, etc.)
                            else:
                                print("J1 successfully moved to 90 degrees.")

                            ret_code = arm.set_servo_angle(servo_id=3, angle=-50)
                            # Check if the operation was successful
                            if ret_code != 0:
                                print(f"Error: Failed to move J1. Error code: {ret_code}")
                                # Optionally, handle the error (retry, abort, log, etc.)
                            else:
                                print("J3 successfully moved to 90 degrees.")

                            code = arm.set_position(
                                x=target_pose[0],
                                y=target_pose[1],
                                z=target_pose[2],
                                roll=target_pose[3],
                                pitch=target_pose[4],
                                yaw=target_pose[5],
                                speed=ROBOT_SPEED,
                                mvacc=50,
                                wait=True
                            )
                            if code == 0:
                                print("Successfully moved to the desired position!")
                                await send_tuple(websocket, ('exit', 'OK', 5.6, 7.3))
                                await asyncio.sleep(0.1)
                                await send_tuple(websocket, ('data', 'exit', 5.6, 7.3))
                                arm.disconnect()
                                return
                            else:
                                print(f"Failed to move the arm. Error code: {code}")
                                await send_tuple(websocket, ('exit', 'FAIL', 5.6, 7.3))
            else:
                await asyncio.sleep(0.05)
        except Exception as e:
            print(f"Error {e}")
            await asyncio.sleep(1)


async def primary_robot_workspace(marking, websocket):
    from scipy.spatial.transform import Rotation as R
    # Count occurrences
    from collections import Counter

    try:
        from datetime import datetime
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")

        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f'femur_dict  {femur_dict}')
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        with open(pickle_path_femur_hc, "rb") as handle:
            HC = np.array(pickle.load(handle))
        print("Femur HC variables loaded successfully")

        # Tibia variables
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_tc_handle:
            tibia_tc = pickle.load(tibia_tc_handle)

        pickle_path_tibia = os.path.join(base_dir, "tibia_variables.pickle")
        with open(pickle_path_tibia, "rb") as tibia_handle:
            tibia_dict = pickle.load(tibia_handle)
        print(f'tibia_dict  {tibia_dict}')
    except Exception as e:
        print(f"Error loading femur or tibia variables: {e}")
        return -1

    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        while True:
            try:
                arm = XArmAPI(arm_ip)
                arm.connect()
                if arm.connected:
                    break
            except Exception as e:
                print(f'Robot is not Enable or Power off')
                await asyncio.sleep(1)
    while True:
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            print(f'{len(detections_right)}    {len(detections_left)}')
            if not (len(detections_right) == 9 and len(detections_left) == 9):
                await asyncio.sleep(0.1)
                continue
            if page not in ['robot_workspace.html', 'revrobot_workspace.html']:
                return

            detections_right = sorted(
                detections_right, key=lambda detection: detection[0]
            )
            detections_left = sorted(
                detections_left, key=lambda detection: detection[0]
            )

            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            robot_tracker = TriangleTracker(
                first_three_right,
                first_three_left,
            )

            R_points = robot_tracker.getLEDcordinates()
            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]

            detections_left = detections_left[3:]
            detections_right = detections_right[3:]
            femure = TriangleTracker(
                detections_right[:3],
                detections_left[:3],
                mode="F",
            )

            F_points = femure.getLEDcordinates()
            # print(f'F_points {F_points}')
            # return
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            tebia = TriangleTracker(
                detections_right[3:],
                detections_left[3:],
                mode="T",
            )
            T_Points = tebia.getLEDcordinates()
            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
            else:
                tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]
            tibia_plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
            )
            tibia_plane[2] = TC
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tibia_plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tibia_plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tibia_plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tibia_plane, tibia_dict["LM"]["C"]
            )
            ANC = (np.array(MM) + np.array(LM)) / 2

            a = distance(F_points[0], F_points[1])
            b = distance(F_points[1], F_points[2])
            c = distance(F_points[2], F_points[0])

            # Identify the unique (base) and repeated (equal legs) lengths
            sides = [a, b, c]
            sides_rounded = np.round(sides, decimals=5)  # for floating point stability

            counted = Counter(sides_rounded)
            leg_length, base_length = 0, 0
            for length, count in counted.items():
                if count == 2:
                    leg_length = length
                elif count == 1:
                    base_length = length

            real_base_length = 64  # for example, 50 mm in real world

            # Calculate scale factor (real-world units per unit in camera space)
            scale = real_base_length / base_length

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
            )

            print(
                f" translation_vector {translation_vector} Newtranslated_plane{Newtranslated_plane}"
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            reachable_points_to_check = {
                'FC': FC,
                'TC': TC,
                'LDC': LDC,
                'ME': ME,
                'LE': LE,
                'MC': MC,
                'LC': LC
            }

            reachable_count = 0
            unreachable_points = []

            for label, point in reachable_points_to_check.items():
                print(f'Checking point {label}: {point}')

                # Swap Y and Z
                point[2], point[1] = point[1], point[2]
                point_np = np.array(point).reshape(1, -1)
                predicted_dest = updated_model.predict(point_np)

                if arm.connected and USE_ROBOT:
                    pose = [
                        predicted_dest[0][0],  # x
                        predicted_dest[0][1],  # y
                        predicted_dest[0][2],  # z
                        90,  # roll
                        0,  # pitch
                        90  # yaw
                    ]
                    code, angles = arm.get_inverse_kinematics(pose)

                    if code == 0:
                        print(f"Reachable ✅: {label} -> {predicted_dest[0]}")
                        reachable_count += 1
                        await send_tuple(websocket, ('data', f'{label} InProgress....', 5.6, 7.3))
                        await asyncio.sleep(0.5)
                    else:
                        print(f"Unreachable ❌: {label} -> {predicted_dest[0]}, Error code: {code}")
                        unreachable_points.append(label)
                        await send_tuple(websocket, ('data', f'{label} Unreachable ❌', 5.6, 7.3))
                        await send_tuple(websocket, ('data', f'{label} adjust position', 5.6, 7.3))

            if reachable_count == len(reachable_points_to_check):
                await send_tuple(websocket, ('data', 'OK', 5.6, 7.3))
                await asyncio.sleep(0.1)
                await send_tuple(websocket, ('data', 'exit', 5.6, 7.3))
            else:
                print("Adjust robot for points:", unreachable_points)
                await asyncio.sleep(0.1)
        except Exception as err:
            print(f"{__file__} e {err}")
            return


def rotation_matrix_from_axis_angle(axis, theta):
    axis = axis / np.linalg.norm(axis)
    K = np.array([[0, -axis[2], axis[1]],
                  [axis[2], 0, -axis[0]],
                  [-axis[1], axis[0], 0]])
    return np.eye(3) + np.sin(theta) * K + (1 - np.cos(theta)) * (K @ K)


def rotation_matrix_between_vectors(v1, v2):
    v1 = v1 / np.linalg.norm(v1)
    v2 = v2 / np.linalg.norm(v2)

    cross = np.cross(v1, v2)
    dot = np.dot(v1, v2)

    if np.allclose(cross, 0):
        if dot > 0:
            # Vectors are already aligned
            return np.eye(3)
        else:
            # Vectors are opposite
            # Find a perpendicular vector
            perp = np.array([1, 0, 0]) if not np.allclose(v1, [1, 0, 0]) else np.array([0, 1, 0])
            axis = np.cross(v1, perp)
            axis = axis / np.linalg.norm(axis)
            return rotation_matrix_from_axis_angle(axis, np.pi)

    skew = np.array([[0, -cross[2], cross[1]],
                     [cross[2], 0, -cross[0]],
                     [-cross[1], cross[0], 0]])

    R = np.eye(3) + skew + (skew @ skew) * ((1 - dot) / (np.linalg.norm(cross) ** 2))
    return R


def normalize(v):
    """Normalize vector to unit length"""
    return v / np.linalg.norm(v)


def rotation_matrix(axis, theta):
    """Rodrigues' rotation formula (corrected)"""
    axis = normalize(axis)
    K = np.array([[0, -axis[2], axis[1]],
                  [axis[2], 0, -axis[0]],
                  [-axis[1], axis[0], 0]])
    return np.eye(3) + np.sin(theta) * K + (1 - np.cos(theta)) * (K @ K)


def calculate_z_angle(femur, robot):
    """
    Aligns X/Y axes and returns Z-axis angle difference
    Inputs:
        femur: (X, Y, Z) vectors as numpy arrays
        robot: (X, Y, Z) vectors as numpy arrays
    Returns:
        Angle between Z-axes in degrees after X/Y alignment
    """
    # Normalize input vectors
    X_f, Y_f, Z_f = [normalize(v) for v in femur]
    X_r, Y_r, Z_r = [normalize(v) for v in robot]

    # Align X-axes
    cross = np.cross(X_f, X_r)
    if np.linalg.norm(cross) < 1e-8:  # Parallel case
        R1 = np.eye(3)
    else:
        theta_x = np.arccos(np.clip(X_f @ X_r, -1, 1))
        R1 = rotation_matrix(cross, theta_x)

    # Align Y-axes using X-axis rotation
    Y_rot = R1 @ Y_f
    theta_y = np.arccos(np.clip(Y_rot @ Y_r, -1, 1))
    R2 = rotation_matrix(X_r, theta_y)  # Rotate about aligned X-axis

    # Transform Z-axis and calculate angle
    Z_transformed = R2 @ R1 @ Z_f
    return np.degrees(np.arccos(np.clip(Z_transformed @ Z_r, -1, 1)))


async def primary_kneeDistalFemureCut(marking, websocket):
    global arm
    from scipy.spatial.transform import Rotation as R
    # Count occurrences
    from collections import Counter

    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, ""
                                             "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()

    if USE_ROBOT:
        while True:
            try:
                arm = XArmAPI(arm_ip)
                arm.connect()
                break
            except Exception as e:
                print(f'Check for the robotic Not connected / power off')
                await asyncio.sleep(1)
                continue
    omega = utils.GetplanningInput(filename="femure_input1")
    previous_data = {
        'flexion': 0,
        'varus': 0,
        'markpoint': {'X': 0, 'Y': 0, 'Z': 0}
    }
    target_pose = (253, 537, 130, 140, -80, 53.6)

    def is_same_position(current, target, tolerance=1e-2):
        """Check if current and target poses are the same within a small tolerance."""
        return all(abs(c - t) < tolerance for c, t in zip(current, target))

    arm.motion_enable(enable=True)  # Enable motion
    arm.set_mode(0)  # Set to position control mode
    arm.set_state(0)  # Set to ready state
    current_pose = arm.get_position(is_radian=False)  # Should return a tuple or list

    if is_same_position(current_pose, target_pose):
        print("Arm is already at the desired position. Skipping move.")
    else:
        ret_code = arm.set_servo_angle(servo_id=1, angle=90)
        if ret_code != 0:
            print(f"Error: Failed to move J1. Error code: {ret_code}")
            # Optionally, handle the error (retry, abort, log, etc.)
        else:
            print("J1 successfully moved to 90 degrees.")

        ret_code = arm.set_servo_angle(servo_id=3, angle=-53)
        # Check if the operation was successful
        if ret_code != 0:
            print(f"Error: Failed to move J1. Error code: {ret_code}")
            # Optionally, handle the error (retry, abort, log, etc.)
        else:
            print("J1 successfully moved to 90 degrees.")

        code = arm.set_position(
            x=target_pose[0],
            y=target_pose[1],
            z=target_pose[2],
            roll=target_pose[3],
            pitch=target_pose[4],
            yaw=target_pose[5],
            speed=ROBOT_SPEED,
            mvacc=50,
            wait=True
        )
        if code == 0:
            print("Successfully moved to the desired position!")
        else:
            print(f"Failed to move the arm. Error code: {code}")
            return
    while True:
        try:
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            print(f'{len(detections_right)}    {len(detections_left)}')
            if not (len(detections_right) == 9 and len(detections_left) == 9):
                await asyncio.sleep(0.1)
                continue
            if page != "femur-distal-femur-cut.html":
                return

            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            robot_detection_right = detections_right[:3]
            robot_detection_left = detections_left[:3]

            robot = TriangleTracker(
                robot_detection_right, robot_detection_left
            )
            R_points = robot.getLEDcordinates()
            print(f'R_points   {R_points}')
            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            femure_detection_right = detections_right[3:6]
            femure_detection_left = detections_left[3:6]

            femure = TriangleTracker(
                femure_detection_right,
                femure_detection_left,
                "F",
            )

            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
            )

            print(
                f" translation_vector {translation_vector} Newtranslated_plane{Newtranslated_plane}"
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )
            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]
            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            # print(f"femur_plane {femur_plane}")
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )
            # print(f'HC {HC}  FC {FC}  LDC {LDC}')
            # print(f'ME {ME}  LE {LE}')

            # print(f'F_points {F_points}')
            a = distance(F_points[0], F_points[1])  # 64 mm
            b = distance(F_points[1], F_points[2])  # 113.6 mm
            c = distance(F_points[2], F_points[0])  # 111.26 mm
            # Real-world side lengths in mm
            real_a = 64.0
            real_b = 113.6
            real_c = 111.26

            # Heron's formula - camera space
            s_cam = (a + b + c) / 2
            area_cam = np.sqrt(s_cam * (s_cam - a) * (s_cam - b) * (s_cam - c))

            # Heron's formula - real-world space
            s_real = (real_a + real_b + real_c) / 2
            area_real = np.sqrt(s_real * (s_real - real_a) * (s_real - real_b) * (s_real - real_c))

            # Scale factor (mm per unit)
            scale = np.sqrt(area_real / area_cam)

            cutting_points_scalling = omega / scale
            direction_vector = HC - FC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            projection_point = LDC + scaled_unit_vector
            # print(f' projection_point {projection_point}')
            # print(f"*" * 100)
            # print(f"omega {omega} LDC {LDC}  projection_point {projection_point}")
            # print(f"*" * 100)

            # Step 1: Construct femurbone base coordinate system
            femurbone_x_axis = normalize(FC - HC)
            femurbone_z_axis = normalize(ME - LE)

            # Orthonormal Y axis using cross product
            femurbone_y_axis = normalize(np.cross(femurbone_z_axis, femurbone_x_axis))

            # # Recompute Z to ensure orthogonality
            # femurbone_z_axis = normalize(np.cross(femurbone_y_axis, femurbone_x_axis))

            # Final rotation matrix (column-wise: X, Y, Z)
            # R_B = np.column_stack((femurbone_x_axis, femurbone_y_axis, femurbone_z_axis))

            v1 = FC - HC
            R_points = np.array(R_points)
            # R_points
            mid_idx = np.argmin(R_points[:, 1])
            mid = R_points[mid_idx]
            remaining = [R_points[i] for i in range(3) if i != mid_idx]

            # Step 2: Sort remaining two by X to get low and high
            low, high = sorted(remaining, key=lambda p: p[0])
            v2 = high - low
            R_rel = rotation_matrix_between_vectors(v1, v2)

            def rotation_angle_from_matrix(R):
                angle = np.arccos((np.trace(R) - 1) / 2)
                return np.degrees(angle)

            angle_deg1 = rotation_angle_from_matrix(R_rel)
            # print("Rotation angle (degrees):", angle_deg)

            x_mid = (low + high) / 2.0
            y_axis = mid - x_mid

            v3 = np.cross(v2, y_axis)
            femurbone_x_axis = FC - HC

            # Calculate dot product and magnitudes
            dot_product = np.dot(v3, femurbone_x_axis)
            magnitude_v3 = np.linalg.norm(v3)
            magnitude_femurbone_x_axis = np.linalg.norm(femurbone_x_axis)

            # Calculate the cosine of the angle
            cos_angle = dot_product / (magnitude_v3 * magnitude_femurbone_x_axis)

            # Ensure the cosine value is within the valid range of -1 to 1 due to floating point errors
            cos_angle = np.clip(cos_angle, -1.0, 1.0)

            # Calculate the angle in radians
            angle_radians = np.arccos(cos_angle)

            # Optionally, convert to degrees
            varus = 90 - np.degrees(angle_radians)
            ####################################################################################################
            Mid_point_RoboTracker = (
                                            np.array(tracker_plane[1]) + np.array(tracker_plane[2])
                                    ) / 2

            # Step 2: y vector calculation
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

            # Step 3: x vector calculation
            RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

            RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

            # Step 6: FCS vectors
            x_FCS = FC - HC
            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(RoboTracker_y, x_FCS)
            except Exception as e:
                print(f"Error {e}")
            #     time.sleep(2)
            ###########################################################################
            projection_point[2], projection_point[1] = (
                projection_point[1],
                projection_point[2],
            )
            projection_point = np.array(projection_point).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(projection_point)

            print(
                f" projection_point {projection_point} predicted_dest  {predicted_dest} flexion {flexion} varus {varus}"
            )
            if arm.connected and USE_ROBOT:

                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                adjustedAngle = utils.GetplanningInput(filename="femure_input2")

                pose = [
                    predicted_dest[0][0],  # x
                    predicted_dest[0][1],  # y
                    predicted_dest[0][2],  # z
                    90 - varus,  # roll (in degrees by default)
                    flexion,  # pitch
                    90 + adjustedAngle  # yaw
                ]
                code, angles = arm.get_inverse_kinematics(pose)
                print(f'varus {varus}   flexion {flexion}')
                if code == 0:
                    if abs(flexion - previous_data.get('flexion', flexion)) >= 0.15:
                        # arm.set_pause(True)
                        arm.set_state(state=4)
                        # # Optionally re-enable if needed
                        # arm.motion_enable(enable=True)
                        arm.set_state(state=0)
                        # await asyncio.sleep(0.1)  # Small delay before re-command
                        previous_data['flexion'] = flexion  # Update the stored flexion
                        point = np.array([predicted_dest[0][0], predicted_dest[0][1], predicted_dest[0][2]])
                        prev_point = np.array([
                            previous_data['markpoint']['X'],
                            previous_data['markpoint']['Y'],
                            previous_data['markpoint']['Z']
                        ])

                        point_distance = np.linalg.norm(point - prev_point)

                        position = arm.get_position()
                        if position[0] == 0:  # Check if fetching position was successful
                            x, y, z, roll, pitch, yaw = position[1]

                        code = arm.set_position(
                            x=predicted_dest[0][0],
                            y=predicted_dest[0][1],
                            z=predicted_dest[0][2],
                            roll=-90 + varus,
                            pitch=flexion + 6,
                            yaw=-90,
                            speed=ROBOT_SPEED,
                            mvacc=50,
                            radius=5,
                            wait=False,
                        )
                        if code == 0:
                            print(f"Successfully moved to the desired position! point_distance {point_distance}")
                            # await asyncio.sleep(0.1)
                        else:
                            print(f"Failed to move the arm. Error code: {code}")
                else:
                    print("Unreachable ❌:", predicted_dest[0], "Error code:", code)
                    continue
            await asyncio.sleep(0.1)
            return
        except Exception as e:
            print(f"Error {e}")


async def primary_kneeAnteriorCut(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        while True:
            try:
                arm = XArmAPI(arm_ip)
                arm.connect()
                if arm.connected:
                    break
            except Exception as e:
                print(f'Robot is not Enable or Power off')
                await asyncio.sleep(1)
    previous_data = {
        'flexion': 0,
        'varus': 0,
        'markpoint': {'X': 0, 'Y': 0, 'Z': 0}
    }

    while True:
        try:

            if page != "femur-anterior-resection.html":
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            if len(detections_right) == 9 and len(detections_left) == 9:

                # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
                # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                three_right = detections_right[0:3]
                three_left = detections_left[0:3]
                three_right = sorted(three_right, key=lambda x: x[0])
                three_left = sorted(three_left, key=lambda x: x[0])
                Pointer = TriangleTracker(
                    three_right, three_left
                )
                R_points = Pointer.getLEDcordinates()

                if R_points[1][1] >= R_points[0][1]:
                    tracker_plane = [R_points[2], R_points[0], R_points[1]]
                else:
                    tracker_plane = [R_points[2], R_points[1], R_points[0]]
                # print(f'robotTracker   {tracker_plane}')
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                # next_three_right = detections_right[3:6]
                # next_three_left = detections_left[3:6]
                femure = TriangleTracker(
                    first_three_right, first_three_left, "F"
                )

                F_points = femure.getLEDcordinates()
                print(f'F_points {F_points}')

                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]

                # tracker_plane = [R_points[0], R_points[1], R_points[2]]

                new_origin = utils.find_new_point_location(
                    old_plane_points=initial_data["robot_tracker_plane"],
                    new_plane_points=tuple(tracker_plane),
                    old_marked_point=initial_data["old_mark_point"],
                )

                # Step 1: Compute centroid
                centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
                # R_points
                # Step 2: Compute translation vector
                translation_vector = new_origin - centroid

                # Step 3: Translate the plane
                Newtranslated_plane = (
                        initial_data["robot_tracker_plane"] + translation_vector
                )

                robot_calib_data = os.path.join(
                    current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
                )
                led_points, robot_points = load_data(file_path=robot_calib_data)
                transformed_led_points = np.array(
                    [
                        utils.find_new_point_location(
                            old_plane_points=initial_data["translated_plane"],
                            new_plane_points=tuple(Newtranslated_plane),
                            old_marked_point=led,
                        )
                        for led in led_points
                    ]
                )

                # Swap Y and Z back to match coordinate system
                transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

                updated_model = LinearRegression()
                updated_model.fit(transformed_led_points, robot_points)
                FC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                LDC = utils.find_new_point_location(
                    femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
                )
                LE = utils.find_new_point_location(
                    femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
                )
                ME = utils.find_new_point_location(
                    femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
                )
                ME = utils.find_new_point_location(
                    femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
                )
                AC = utils.find_new_point_location(
                    femur_dict["AC"]["Plane"], femur_plane, femur_dict["AC"]["C"]
                )

                M1 = (FC[1] - HC[1]) / (FC[0] - HC[0])  # DFC_slope represnets M1
                M2 = -1 / M1  # DFC_target slope represnets M2

                a = distance(F_points[0], F_points[1])
                b = distance(F_points[1], F_points[2])
                c = distance(F_points[2], F_points[0])
                # Identify the unique (base) and repeated (equal legs) lengths
                sides = [a, b, c]
                sides_rounded = np.round(sides, decimals=5)  # for floating point stability
                counted = Counter(sides_rounded)
                leg_length, base_length = 0, 0
                for length, count in counted.items():
                    if count == 2:
                        leg_length = length
                    elif count == 1:
                        base_length = length
                real_base_length = 64  # for example, 50 mm in real world
                # Calculate scale factor (real-world units per unit in camera space)
                scale = real_base_length / base_length

                cutting_points_scalling = omega / scale
                direction_vector = HC - FC
                unit_vector = direction_vector / np.linalg.norm(direction_vector)
                scaled_unit_vector = unit_vector * cutting_points_scalling
                # print(f'scaled_unit_vector {scaled_unit_vector}')
                # Calculate the projection point
                projection_point = LDC + scaled_unit_vector
                # print(f' projection_point {projection_point}')

                femurbone_x_axis = normalize(FC - HC)
                femurbone_z_axis = normalize(ME - LE)

                # Orthonormal Y axis using cross product
                femurbone_y_axis = normalize(np.cross(femurbone_z_axis, femurbone_x_axis))

                # # Recompute Z to ensure orthogonality
                # femurbone_z_axis = normalize(np.cross(femurbone_y_axis, femurbone_x_axis))

                # Final rotation matrix (column-wise: X, Y, Z)
                # R_B = np.column_stack((femurbone_x_axis, femurbone_y_axis, femurbone_z_axis))

                v1 = FC - HC
                R_points = np.array(R_points)
                # R_points
                mid_idx = np.argmin(R_points[:, 1])
                mid = R_points[mid_idx]
                remaining = [R_points[i] for i in range(3) if i != mid_idx]

                # Step 2: Sort remaining two by X to get low and high
                low, high = sorted(remaining, key=lambda p: p[0])
                v2 = high - low
                # R_rel = rotation_matrix_between_vectors(v1, v2)
                #
                # def rotation_angle_from_matrix(R):
                #     angle = np.arccos((np.trace(R) - 1) / 2)
                #     return np.degrees(angle)
                #
                # angle_deg1 = rotation_angle_from_matrix(R_rel)
                # print("Rotation angle (degrees):", angle_deg)

                x_mid = (low + high) / 2.0
                y_axis = mid - x_mid

                v3 = np.cross(v2, y_axis)
                femurbone_x_axis = FC - HC

                # Calculate dot product and magnitudes
                dot_product = np.dot(v3, femurbone_x_axis)
                magnitude_v3 = np.linalg.norm(v3)
                magnitude_femurbone_x_axis = np.linalg.norm(femurbone_x_axis)

                # Calculate the cosine of the angle
                cos_angle = dot_product / (magnitude_v3 * magnitude_femurbone_x_axis)

                # Ensure the cosine value is within the valid range of -1 to 1 due to floating point errors
                cos_angle = np.clip(cos_angle, -1.0, 1.0)

                # Calculate the angle in radians
                angle_radians = np.arccos(cos_angle)

                # Optionally, convert to degrees
                varus = 90 - np.degrees(angle_radians)

                Mid_point_RoboTracker = (
                                                np.array(tracker_plane[1]) + np.array(tracker_plane[2])
                                        ) / 2

                # Step 2: y vector calculation
                RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

                # Step 3: x vector calculation
                RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

                # Step 4: Norms (just for debug, we won't use them for cross product)
                RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

                RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

                # Step 5: z vector (cross product of y and x vectors)
                RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

                # Step 6: FCS vectors
                x_FCS = FC - HC
                z_FCS = LE - ME

                def vector2_signed_angle_xy(A, B):
                    # Create 2D projections on XY plane
                    AProjXY = np.array([A[0], A[1]])
                    BProjXY = np.array([B[0], B[1]])

                    # Calculate signed angle
                    cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                    dot = np.dot(AProjXY, BProjXY)

                    angle = np.degrees(np.arctan2(cross_z, dot))
                    angle_rounded = np.round(angle * 2) / 2

                    return angle_rounded

                try:
                    flexion = vector2_signed_angle_xy(RoboTracker_y, x_FCS)
                except Exception as e:
                    print(f"Error {e}")
                    return
                AC[2], AC[1] = AC[1], AC[2]
                print(f'AC {AC}')
                projection_point = np.array(AC).reshape(1, -1)
                # point1 = point1 - old_origin_optical + new_origin
                predicted_dest = updated_model.predict(projection_point)

                print(
                    f" projection_point {projection_point} predicted_dest  {predicted_dest} varus ,flexion {varus} {flexion}"
                )

                if arm.connected and USE_ROBOT:

                    arm.motion_enable(enable=True)  # Enable motion
                    arm.set_mode(0)  # Set to position control mode
                    arm.set_state(0)  # Set to ready state
                    pose = [
                        predicted_dest[0][0],  # x
                        predicted_dest[0][1],  # y
                        predicted_dest[0][2],  # z
                        90 - varus,  # roll (in degrees by default)
                        flexion,  # pitch
                        90  # yaw
                    ]
                    code, angles = arm.get_inverse_kinematics(pose)
                    if code == 0:
                        if abs(flexion - previous_data.get('flexion', flexion)) >= 0.5:
                            # arm.set_pause(True)
                            arm.set_state(state=4)
                            # # Optionally re-enable if needed
                            # arm.motion_enable(enable=True)
                            arm.set_state(state=0)
                            # await asyncio.sleep(0.1)  # Small delay before re-command
                            previous_data['flexion'] = flexion  # Update the stored flexion
                            point = np.array([predicted_dest[0][0], predicted_dest[0][1], predicted_dest[0][2]])
                            prev_point = np.array([
                                previous_data['markpoint']['X'],
                                previous_data['markpoint']['Y'],
                                previous_data['markpoint']['Z']
                            ])

                            point_distance = np.linalg.norm(point - prev_point)
                            code = arm.set_position(
                                x=predicted_dest[0][0],
                                y=predicted_dest[0][1],
                                z=predicted_dest[0][2],
                                roll=90 + varus,
                                pitch=-90 + abs(flexion) + 6,
                                yaw=90,
                                speed=ROBOT_SPEED,
                                mvacc=50,
                                radius=5,
                                wait=False,
                            )
                            if code == 0:
                                print(f"Successfully moved to the desired position! point_distance {point_distance}")
                                # await asyncio.sleep(0.1)
                            else:
                                print(f"Failed to move the arm. Error code: {code}")
                    else:
                        print("Unreachable ❌:", predicted_dest[0], "Error code:", code)
            else:
                await asyncio.sleep(0.05)
        except Exception as e:
            print(f"Error {e}")


async def primary_kneefemur_posterior(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        while True:
            try:
                arm = XArmAPI(arm_ip)
                arm.connect()
                if arm.connected:
                    break
            except Exception as e:
                print(f'Robot is not Enable or Power off')
                await asyncio.sleep(1)

    previous_data = {
        'flexion': 0,
        'varus': 0,
        'markpoint': {'X': 0, 'Y': 0, 'Z': 0}
    }

    while True:
        try:
            while True:
                if page != "femur-posterior-resection.html":
                    return
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    print(f'R {len(detections_right)}    L{len(detections_left)}')
                    continue

            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            three_right = detections_right[0:3]
            three_left = detections_left[0:3]
            three_right = sorted(three_right, key=lambda x: x[0])
            three_left = sorted(three_left, key=lambda x: x[0])
            Pointer = TriangleTracker(
                three_right, three_left
            )
            R_points = Pointer.getLEDcordinates()

            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            # print(f'robotTracker   {tracker_plane}')
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            next_three_right = detections_right[3:6]
            next_three_left = detections_left[3:6]
            femure = TriangleTracker(
                first_three_right, first_three_left, "F"
            )

            F_points = femure.getLEDcordinates()
            # print(f'F_points {F_points}')

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            # tracker_plane = [R_points[0], R_points[1], R_points[2]]

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            LPC = utils.find_new_point_location(
                femur_dict["LPC"]["Plane"], femur_plane, femur_dict["LPC"]["C"]
            )
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            AC = utils.find_new_point_location(
                femur_dict["AC"]["Plane"], femur_plane, femur_dict["AC"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )
            Mid_point_RoboTracker = (
                                            np.array(tracker_plane[1]) + np.array(tracker_plane[2])
                                    ) / 2

            # Step 2: y vector calculation
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

            # Step 3: x vector calculation
            RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

            RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

            v1 = FC - HC
            R_points = np.array(R_points)
            # R_points
            mid_idx = np.argmin(R_points[:, 1])
            mid = R_points[mid_idx]
            remaining = [R_points[i] for i in range(3) if i != mid_idx]

            # Step 2: Sort remaining two by X to get low and high
            low, high = sorted(remaining, key=lambda p: p[0])
            v2 = high - low

            x_mid = (low + high) / 2.0
            y_axis = mid - x_mid

            v3 = np.cross(v2, y_axis)
            femurbone_x_axis = FC - HC

            # Calculate dot product and magnitudes
            dot_product = np.dot(v3, femurbone_x_axis)
            magnitude_v3 = np.linalg.norm(v3)
            magnitude_femurbone_x_axis = np.linalg.norm(femurbone_x_axis)

            # Calculate the cosine of the angle
            cos_angle = dot_product / (magnitude_v3 * magnitude_femurbone_x_axis)

            # Ensure the cosine value is within the valid range of -1 to 1 due to floating point errors
            cos_angle = np.clip(cos_angle, -1.0, 1.0)

            # Calculate the angle in radians
            angle_radians = np.arccos(cos_angle)

            # Optionally, convert to degrees
            varus = 90 - np.degrees(angle_radians)

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            try:
                flexion = vector2_signed_angle_xy(RoboTracker_y, v1)
                # print(f'flexion {flexion}')
            except Exception as e:
                print(f"Error {e}")

            AC[2], AC[1] = AC[1], AC[2]
            point_a = np.array(AC).reshape(1, -1)
            point_a_dest = updated_model.predict(point_a)
            LPC[2], LPC[1] = LPC[1], LPC[2]
            point_b = np.array(LPC).reshape(1, -1)
            point_b_dest = updated_model.predict(point_b)

            femuresize = utils.distance_3d(
                np.array(point_a_dest.flatten()), np.array(point_b_dest.flatten())
            )
            result = get_size(femuresize)
            print(
                f"******************** femuresize {femuresize} get_size {result} ********************"
            )

            fsize = get_offset(result)
            print(f"Offset for {result}: {fsize}")
            # Swap second and third elements before conversion
            FC[1], FC[2] = FC[2], FC[1]
            HC[1], HC[2] = HC[2], HC[1]
            ME[1], ME[2] = ME[2], ME[1]
            LE[1], LE[2] = LE[2], LE[1]

            # Convert to NumPy arrays after swapping
            FC = np.array(FC).reshape(1, -1)
            HC = np.array(HC).reshape(1, -1)
            ME = np.array(ME).reshape(1, -1)
            LE = np.array(LE).reshape(1, -1)

            # Model predictions
            x_FCS_robot = (
                    updated_model.predict(FC).flatten()
                    - updated_model.predict(HC).flatten()
            )
            z_FCS_robot = (
                    updated_model.predict(LE).flatten()
                    - updated_model.predict(ME).flatten()
            )

            x_unit = x_FCS_robot / np.linalg.norm(x_FCS_robot)
            z_unit = z_FCS_robot / np.linalg.norm(z_FCS_robot)
            y_unit = np.cross(z_unit, x_unit)
            AC[2], AC[1] = AC[1], AC[2]

            offset = y_unit * fsize

            # Calculate the new target point
            predicted_dest = point_b_dest.flatten() + 9
            print("predicted_dest:", predicted_dest)
            print("AC:", AC)
            print("offset:", offset)

            if arm.connected and USE_ROBOT:

                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                pose = [
                    predicted_dest[0],  # x
                    predicted_dest[1],  # y
                    predicted_dest[2],  # z
                    90 - varus,  # roll (in degrees by default)
                    (-90 + flexion + 6),  # pitch
                    90  # yaw
                ]
                code, angles = arm.get_inverse_kinematics(pose)
                if code == 0:
                    if abs(flexion - previous_data.get('flexion', flexion)) >= 0.5:
                        # arm.set_pause(True)
                        arm.set_state(state=4)
                        # # Optionally re-enable if needed
                        # arm.motion_enable(enable=True)
                        arm.set_state(state=0)
                        # await asyncio.sleep(0.1)  # Small delay before re-command
                        previous_data['flexion'] = flexion  # Update the stored flexion
                        point = np.array([predicted_dest[0], predicted_dest[1], predicted_dest[2]])
                        prev_point = np.array([
                            previous_data['markpoint']['X'],
                            previous_data['markpoint']['Y'],
                            previous_data['markpoint']['Z']
                        ])

                        point_distance = np.linalg.norm(point - prev_point)
                        code = arm.set_position(
                            x=predicted_dest[0],
                            y=predicted_dest[1],
                            z=predicted_dest[2],
                            roll=-90 + varus,
                            pitch=-90 + flexion + 6,
                            yaw=-90,
                            speed=ROBOT_SPEED,
                            mvacc=50,
                            radius=5,
                            wait=False,
                        )
                        if code == 0:
                            print(f"Successfully moved to the desired position! point_distance {point_distance}")
                            # await asyncio.sleep(0.1)
                        else:
                            print(f"Failed to move the arm. Error code: {code}")
                else:
                    print("Unreachable ❌:", predicted_dest, "Error code:", code)
        except Exception as e:
            print(f"Error {e}")
        return


async def primary_kneeAnteriorChamferCut(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC 1 variables loaded successfully")
        except:
            print("Error loading femur variables")
        omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        while True:
            try:
                arm = XArmAPI(arm_ip)
                arm.connect()
                if arm.connected:
                    break
            except Exception as e:
                print(f'Robot is not Enable or Power off')
                await asyncio.sleep(1)
    while True:
        try:
            while True:
                if page != "femur-anterior-chamfer-resection.html":
                    arm.disconnect()
                    return
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    print(f" {len(detections_right)}  {len(detections_left)}")
                    await asyncio.sleep(0.2)
                    continue
            print(f" {len(detections_right)}  {len(detections_left)}")
            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            three_right = detections_right[0:3]
            three_left = detections_left[0:3]
            three_right = sorted(three_right, key=lambda x: x[0])
            three_left = sorted(three_left, key=lambda x: x[0])
            Pointer = TriangleTracker(
                three_right, three_left
            )
            R_points = Pointer.getLEDcordinates()

            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            print(f"robotTracker   {tracker_plane}")
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            next_three_right = detections_right[3:6]
            next_three_left = detections_left[3:6]
            femure = TriangleTracker(
                first_three_right, first_three_left, "F"
            )

            F_points = femure.getLEDcordinates()
            print(f"F_points {F_points}")
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            # tracker_plane = [R_points[0], R_points[1], R_points[2]]

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )

            M1 = (FC[1] - HC[1]) / (FC[0] - HC[0])  # DFC_slope represnets M1
            M2 = -1 / M1  # DFC_target slope represnets M2

            a = distance(F_points[0], F_points[1])
            b = distance(F_points[1], F_points[2])
            c = distance(F_points[2], F_points[0])
            # Identify the unique (base) and repeated (equal legs) lengths
            sides = [a, b, c]
            sides_rounded = np.round(sides, decimals=5)  # for floating point stability
            counted = Counter(sides_rounded)
            leg_length, base_length = 0, 0
            for length, count in counted.items():
                if count == 2:
                    leg_length = length
                elif count == 1:
                    base_length = length
            real_base_length = 64  # for example, 50 mm in real world
            # Calculate scale factor (real-world units per unit in camera space)
            scale = real_base_length / base_length

            cutting_points_scalling = omega / scale
            direction_vector = HC - FC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            # print(f'scaled_unit_vector {scaled_unit_vector}')
            # Calculate the projection point
            # projection_point = LDC + scaled_unit_vector
            # print(f' projection_point {projection_point}')

            Mid_point_RoboTracker = (
                                            np.array(tracker_plane[1]) + np.array(tracker_plane[2])
                                    ) / 2

            # Step 2: y vector calculation
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

            # Step 3: x vector calculation
            RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

            RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

            v1 = FC - HC
            R_points = np.array(R_points)
            # R_points
            mid_idx = np.argmin(R_points[:, 1])
            mid = R_points[mid_idx]
            remaining = [R_points[i] for i in range(3) if i != mid_idx]

            # Step 2: Sort remaining two by X to get low and high
            low, high = sorted(remaining, key=lambda p: p[0])
            v2 = high - low
            # R_rel = rotation_matrix_between_vectors(v1, v2)
            #
            # def rotation_angle_from_matrix(R):
            #     angle = np.arccos((np.trace(R) - 1) / 2)
            #     return np.degrees(angle)
            #
            # angle_deg1 = rotation_angle_from_matrix(R_rel)
            # print("Rotation angle (degrees):", angle_deg)

            x_mid = (low + high) / 2.0
            y_axis = mid - x_mid

            v3 = np.cross(v2, y_axis)
            femurbone_x_axis = FC - HC

            # Calculate dot product and magnitudes
            dot_product = np.dot(v3, femurbone_x_axis)
            magnitude_v3 = np.linalg.norm(v3)
            magnitude_femurbone_x_axis = np.linalg.norm(femurbone_x_axis)

            # Calculate the cosine of the angle
            cos_angle = dot_product / (magnitude_v3 * magnitude_femurbone_x_axis)

            # Ensure the cosine value is within the valid range of -1 to 1 due to floating point errors
            cos_angle = np.clip(cos_angle, -1.0, 1.0)

            # Calculate the angle in radians
            angle_radians = np.arccos(cos_angle)

            # Optionally, convert to degrees
            varus = 90 - np.degrees(angle_radians)

            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(RoboTracker_y, v1)
            except Exception as e:
                print(f"Error {e}")
                # time.sleep(2)
            LDC[2], LDC[1] = LDC[1], LDC[2]
            projection_point = np.array(LDC).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(projection_point)

            print(
                f" projection_point {projection_point} predicted_dest  {predicted_dest} varus ,flexion {varus} {flexion}"
            )
            if arm.connected and USE_ROBOT:

                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                code = 0
                code = arm.set_position(
                    x=predicted_dest[0][0],
                    y=predicted_dest[0][1],
                    z=predicted_dest[0][2],
                    roll=-90 + varus,
                    pitch=-135 + flexion + 6,
                    yaw=-90,
                    speed=ROBOT_SPEED,
                    mvacc=50,
                    wait=True,
                )
                if code == 0:
                    print("Successfully moved to the desired position!")
                else:
                    print(f"Failed to move the arm. Error code: {code}")
            return
        except Exception as e:
            print(f"Error {e}")


async def primary_kneefemur_posterior_chamfer(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        try:
            arm = XArmAPI(arm_ip)
            arm.connect()
        except Exception as e:
            print(f'SDK Error reconnecting ')

    while True:
        try:
            while True:
                if page != "femur-posterior-chamfer-resection.html":
                    return
                detections_left, detections_right = await marking.handle_irq_average(n=10)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    continue

            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            three_right = detections_right[0:3]
            three_left = detections_left[0:3]
            three_right = sorted(three_right, key=lambda x: x[0])
            three_left = sorted(three_left, key=lambda x: x[0])
            Pointer = TriangleTracker(
                three_right, three_left, )

            R_points = Pointer.getLEDcordinates()

            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            # print(f'robotTracker   {tracker_plane}')
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            next_three_right = detections_right[3:6]
            next_three_left = detections_left[3:6]
            femure = TriangleTracker(
                first_three_right, first_three_left, "F"
            )

            F_points = femure.getLEDcordinates()
            # print(f'F_points {F_points}')

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            # tracker_plane = [R_points[0], R_points[1], R_points[2]]

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )

            M1 = (FC[1] - HC[1]) / (FC[0] - HC[0])  # DFC_slope represnets M1
            M2 = -1 / M1  # DFC_target slope represnets M2

            a = distance(F_points[0], F_points[1])
            b = distance(F_points[1], F_points[2])
            c = distance(F_points[2], F_points[0])
            # Identify the unique (base) and repeated (equal legs) lengths
            sides = [a, b, c]
            sides_rounded = np.round(sides, decimals=5)  # for floating point stability
            counted = Counter(sides_rounded)
            leg_length, base_length = 0, 0
            for length, count in counted.items():
                if count == 2:
                    leg_length = length
                elif count == 1:
                    base_length = length
            real_base_length = 64  # for example, 50 mm in real world
            # Calculate scale factor (real-world units per unit in camera space)
            scale = real_base_length / base_length

            cutting_points_scalling = omega / scale
            direction_vector = HC - FC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            # print(f'scaled_unit_vector {scaled_unit_vector}')
            # Calculate the projection point
            # projection_point = LDC + scaled_unit_vector
            # print(f' projection_point {projection_point}')

            Mid_point_RoboTracker = (
                                            np.array(tracker_plane[1]) + np.array(tracker_plane[2])
                                    ) / 2

            # Step 2: y vector calculation
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

            # Step 3: x vector calculation
            RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

            RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

            # Step 6: FCS vectors

            z_FCS = LE - ME

            v1 = FC - HC
            R_points = np.array(R_points)
            # R_points
            mid_idx = np.argmin(R_points[:, 1])
            mid = R_points[mid_idx]
            remaining = [R_points[i] for i in range(3) if i != mid_idx]

            # Step 2: Sort remaining two by X to get low and high
            low, high = sorted(remaining, key=lambda p: p[0])
            v2 = high - low

            x_mid = (low + high) / 2.0
            y_axis = mid - x_mid

            v3 = np.cross(v2, y_axis)
            femurbone_x_axis = FC - HC

            # Calculate dot product and magnitudes
            dot_product = np.dot(v3, femurbone_x_axis)
            magnitude_v3 = np.linalg.norm(v3)
            magnitude_femurbone_x_axis = np.linalg.norm(femurbone_x_axis)

            # Calculate the cosine of the angle
            cos_angle = dot_product / (magnitude_v3 * magnitude_femurbone_x_axis)

            # Ensure the cosine value is within the valid range of -1 to 1 due to floating point errors
            cos_angle = np.clip(cos_angle, -1.0, 1.0)

            # Calculate the angle in radians
            angle_radians = np.arccos(cos_angle)

            # Optionally, convert to degrees
            varus = 90 - np.degrees(angle_radians)

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(RoboTracker_y, v1)
            except Exception as e:
                print(f"Error {e}")
                time.sleep(2)
            LDC[2], LDC[1] = LDC[1], LDC[2]
            projection_point = np.array(LDC).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(projection_point)

            print(
                f" projection_point {projection_point} predicted_dest  {predicted_dest} varus ,flexion {varus} {flexion}"
            )

            if arm.connected and USE_ROBOT:

                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                code = arm.set_position(
                    x=predicted_dest[0][0],
                    y=predicted_dest[0][1],
                    z=predicted_dest[0][2],
                    roll=-90 + varus,
                    pitch=45 - 90 + flexion + 6,
                    yaw=-90,
                    speed=ROBOT_SPEED,
                    mvacc=50,
                    wait=True,
                )
                if code == 0:
                    print("Successfully moved to the desired position!")
                    await asyncio.sleep(1)
                else:
                    print(f"Failed to move the arm. Error code: {code}")
                await asyncio.sleep(1)
            return
        except Exception as e:
            print(f"Error {e}")


async def planning_screen(websocket):
    while True:
        try:
            data = await websocket.receive_text()  # Receive WebSocket data as text
            data = json.loads(data)  # Parse JSON

            input_id = data.get("inputId")
            input_value = data.get("inputValue")

            SetplanningInputs(filename=str(input_id), data=str(input_value))
            print(f"Received ID: {input_id}, Value: {input_value}")
            await asyncio.sleep(0.1)

        except json.JSONDecodeError:
            print("Error: Received invalid JSON")
        except Exception as e:
            print(f"WebSocket error: {e}")
            return


async def robot_calibration(marking, websocket):
    from scipy.stats.qmc import LatinHypercube
    from xarm.wrapper import XArmAPI
    global initial_data
    arm = XArmAPI('*************')
    arm.connect()

    def move_to_position(x, y, z, roll, pitch, yaw, speed=70):
        """Move robot arm to the specified position and check success."""
        code = arm.set_position(x=x, y=y, z=z, roll=roll, pitch=pitch, yaw=yaw, speed=speed, mvacc=70, wait=True)
        time.sleep(3)
        return code == 0

    # Define cube parameters
    # center_x, center_y, center_z = 50, -400, 280
    #center_x, center_y, center_z = (317, 249, 145)
    center_x, center_y, center_z = 454, 66, 247

    #center_roll, center_pitch, center_yaw = -172, 0, 90
    center_roll, center_pitch, center_yaw = 48, 73, 53
    half_range = 100  # 600mm total, so ±300mm
    angle_variation = 10  # ±10° for roll, pitch, yaw

    # Define corner positions
    corners = [
        (center_x - half_range, center_y - half_range, center_z - half_range),
        (center_x + half_range, center_y - half_range, center_z - half_range),
        (center_x - half_range, center_y + half_range, center_z - half_range),
        (center_x + half_range, center_y + half_range, center_z - half_range),
        (center_x - half_range, center_y - half_range, center_z + half_range),
        (center_x + half_range, center_y - half_range, center_z + half_range),
        (center_x - half_range, center_y + half_range, center_z + half_range),
        (center_x + half_range, center_y + half_range, center_z + half_range)
    ]
 
    f_pixel = (1936 / 2) / np.tan(np.radians(37.88) / 2)
    baseline = 50.8  # 10 cm baseline
    if arm.connected:
        arm.motion_enable(enable=True)  # Enable motion
        arm.set_mode(0)  # Set to position control mode
        arm.set_state(0)  # Set to ready state
        try:
            num_samples = 50
            lhs = LatinHypercube(d=6).random(n=num_samples)
            lower_bounds = np.array(
                [center_x, center_y, center_z, center_roll, center_pitch, center_yaw]) - np.array(
                [half_range] * 3 + [angle_variation] * 3)
            upper_bounds = np.array(
                [center_x, center_y, center_z, center_roll, center_pitch, center_yaw]) + np.array(
                [half_range] * 3 + [angle_variation] * 3)
            sampled_points = lower_bounds + lhs * (upper_bounds - lower_bounds)

            for i, point in enumerate(sampled_points):
                x, y, z, roll, pitch, yaw = point
                print(f"Moving to LHS Point {i + 1}: X={x}, Y={y}, Z={z}")
                if move_to_position(x, y, z, roll, pitch, yaw):
                    print(f"Reached Point {i + 1}, capturing LED data...")
                x, y, z, roll, pitch, yaw = point
                print(f"Moving to LHS Point {i + 1}: X={x}, Y={y}, Z={z}")
                if move_to_position(x, y, z, roll, pitch, yaw):
                    print(f"Reached Point {i + 1}, capturing LED data...")
                while True:
                    if page != 'robot_calibration':
                        return
                    detections_left, detections_right = await marking.handle_irq_average(n=5)
                    print(f'{len(detections_right)} {len(detections_left)}')
                    if not (len(detections_right) == 1 and len(detections_left) == 1):
                        await asyncio.sleep(0.1)
                        continue
                    if len(detections_right) == 1 and 1 == len(detections_left):
                        x_right = detections_right[0]
                        x_left = detections_left[0]
                        disparity = (x_left[0] - x_right[0])
                        depths = (baseline * f_pixel) / disparity
                        LED = pixel_to_point(detections_left[0], depths)
                        position = arm.get_position()
                        if position[0] == 0:  # Check if fetching position was successful
                            x, y, z, roll, pitch, yaw = position[1]

                        print(f'LED {LED}   {x, y, z}')

                        with open('partialKneeClibration22.txt', 'a') as file:
                            file.write(
                                f'LED {LED}   robot ({x}, {y}, {z})  axis roll pitch yaw ({roll}, {pitch}, {yaw})\n')
                            file.close()
                        break
            arm.disconnect()
        except Exception as err:
            print(f"{__file__} e {err}")
            return


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket handler to process different pipelines based on the page."""
    await websocket.accept()

    # if marking.clients:
    #     old_client = marking.clients.pop()
    #     await old_client.close()
    #     print("Previous client disconnected")
    marking.clients.add(websocket)
    global page  # Track which page the client is on
    results = None
    try:
        while True:

            # Check if the client has sent a message indicating the page
            message = await websocket.receive_text()

            try:
                data = json.loads(message)
                if "file" in data:
                    async with page_lock:  # Ensure thread safety
                        page = data["file"]    # for normal process
                        # page = 'pointer.html'    #for take plane coordinates
                        # page = 'robot_calibration'  #for take 50 points calibration #####################################################################################################
                    print(f"Client is on {page}")
                    # return
                else:
                    continue  # Skip further processing until we have a fra
            except json.JSONDecodeError:
                print("Invalid JSON received")

            # if not marking.frame_queue.empty():
            if page == "pelvis-registration-2.html":
                await hip_pelvis_registration_2(marking, websocket)
            elif page == "handle-position.html":
                await hip_handle_position(marking, websocket)
            elif page == "final-cup-position.html":
                await hip_final_cup_position(marking, websocket)
            elif page == "hipRingPointCollection.html":
                await hipCollectRingPointCloudMarkingPipeline(marking, websocket)
            elif page == "hipFreePointCollection.html":
                await hipFreePointCollection(marking, websocket, rev=None)
            elif page == "revhipFreePointCollection.html":
                await hipFreePointCollection(marking, websocket, rev="rev")
            elif page == "register-acetabulum.html":
                await send_tuple(websocket, ("STEP0", 1, 3, 0))
                await register_acetabulum(marking, websocket)
            elif page == "mid-axial-body-plane.html":
                await send_tuple(websocket, ("NOTE0", 1, 3, 0))
                await hipmidAxialBodyPlane(marking, websocket)
            elif page == "revmid-axial-body-plane.html":
                await send_tuple(websocket, ("NOTE0", 1, 3, 0))
                await hipmidAxialBodyPlane(marking, websocket)
            elif page == "revregister-acetabulum.html":
                await rev_register_acetabulum(marking, websocket)
            elif page == "acetabulum-component-placement.html":
                await send_tuple(websocket, ("STEP0", 1, 3, 0))
                await register_acetabulum(marking, websocket, "postoperation")
            elif page == "system-setup.html" or page == 'system-setup-2.html':
                await pointer_femur_pipeline(marking, websocket)
            elif page == "femur-registration.html":
                await hip_tp_variable_Marking(marking, websocket, point=None)
            elif page == "TP2_marking.html":
                await hip_tp_variable_Marking(marking, websocket, point="TP2")
            elif page == "revTP2_marking.html":
                await hip_tp_variable_Marking(marking, websocket, point="TP2")
            elif page == "revfinal-cup-position.html":
                await revfinal_cup_position(marking, websocket)
            #################################### Primary Knee #############################################
            elif page == "multiple-page2.html":
                await pivoting_procedure(marking, websocket)
                await FemureMarking(marking, websocket)
            elif page == "multiple-page.html":
                await Tebia_marking_tc(marking, websocket)
                await Tebia_marking(marking, websocket)
            elif page == "tkr-screen-2.html":
                await PrimaryKneeLandMarkVerification(marking, websocket)
            elif page == "inner-page6.html":
                await AlignmentAngles_Knee(marking, websocket)
            elif (
                    page == "tkr-screen-3.html"
                    or page == "revtkr-screen-3.html"
                    or page == "revguidance-proximal-tibia-cut.html"
                    or page == "revguidance-distal-femur-cut.html"
            ):
                await planning_screen(websocket)
            elif page in ["robot_position.html", "revrobot_position.html"] :
                await primary_robot_position(marking, websocket)
            elif page == "femur-distal-femur-cut.html":
                await primary_kneeDistalFemureCut(marking, websocket)
            elif page == "femur-inner-page5.html":
                await DistalFemureCutVerification(marking, websocket)
            elif page == "femur-inner-page2.html":
                await primary_kneeTibiaCut(marking, websocket)
            elif page == "tibia-inner-page3.html":
                await TibiaCutVerification(marking, websocket)
            elif page == "femur-anterior-resection.html":
                await primary_kneeAnteriorCut(marking, websocket)
            elif page == "femur-anterior-chamfer-resection.html":
                await primary_kneeAnteriorChamferCut(marking, websocket)
            elif page == "femur-posterior-resection.html":
                await primary_kneefemur_posterior(marking, websocket)
            elif page == "femur-posterior-chamfer-resection.html":
                await primary_kneefemur_posterior_chamfer(marking, websocket)
            # #################################### Revision Knee #############################################
            elif page == "revmultiple-page2.html":
                await pivoting_procedure(marking, websocket)
                await FemureMarking(marking, websocket)
            elif page == "revmultiple-page.html":
                await Tebia_marking_tc(marking, websocket)
                await Tebia_marking(marking, websocket)
            elif page == "revtkr-screen-2.html":
                await PrimaryKneeLandMarkVerification(marking, websocket)
            elif (
                    page == "revinner-page6.html"
                    or page == "revgraph-screen.html"
                    or page == "femur-graph-screen.html"
            ):
                await AlignmentAngles_Knee(marking, websocket)
            elif page == "revml-size-acquisition.html":
                await ml_size_acquisition(marking, websocket)
            elif page == "revFree_point_collection_femur.html":
                await revkneeFree_point_collection_femur(marking, websocket)
            elif page == "revFree_point_collectionTibia.html":
                await revFree_point_collectionTibia(marking, websocket)
            elif page == "revtibia-im-canal-ream.html":
                await revtibia_im_canal_ream(marking, websocket)
            elif page == "revfemur-im-canal-ream.html":
                await revfemur_im_canal_ream(marking, websocket)
            elif page == "revrevision-knee-burr.html":
                await revrevisionKneeTibiaCut(marking, websocket)
            elif page == "revverification-proximal-tibia-cut.html":
                await revverificationTibiaCut(marking, websocket)
            elif page == "revverification-distal-femur-cut.html":
                await revverificationdistalFemure(marking, websocket)
            elif page == "revrevision-knee-burrFemur.html":
                await revDistalFemurCut(marking, websocket)
            #################################### Uni Knee #############################################
            elif page == "unimultiple-page2.html":
                await pivoting_procedure(marking, websocket)
                await FemureMarkingUni(marking, websocket)
            elif page == "uniFree_point_collection_femur.html":
                await uniFree_point_collection_femur(marking, websocket)
            elif page == "unimultiple-page.html":
                await UniTebia_marking_tc(marking, websocket)
                await UniTebia_marking(marking, websocket)
            elif page == "uniFree_point_collectionTibia.html":
                await uniFree_point_collectionTibia(marking, websocket)
            elif page == "unifemur-distal-femur-cut.html":
                await uniKneeDistalFemureCut(marking, websocket)
            elif (page == "uniinner-page6.html" or page == 'uniFinal_leg_Alignment.html'):
                await AlignmentAnglesUni(marking, websocket)
            elif page == "unitkr-screen-2.html":
                await unitkr_screen_2(marking, websocket)
            elif page == "uniaccp_femure_cut.html":
                await accp_femure_cut(marking, websocket)
            elif page == "unitibia-cut.html":
                await unitibia_cut(marking, websocket)
            elif page == "video":
                await stream_video(marking, websocket)
            elif page == 'calibration_robo':
                await EulerAngles(marking, websocket)
            elif page == 'robot_calibration':
                await robot_calibration(marking, websocket)
            elif page == 'robot_tooltipCalibration':
                await robot_offset_calibration(marking, websocket, tool='robot_offset')
            elif page == 'Calibration':
                await Knee_tooltipcalibration(marking, websocket, tool='Pointer')
            elif page == 'pointer.html':
                await mark_new_point(marking, websocket, tool='Pointer')
                # await Knee_tooltipcalibration(marking, websocket, tool='burr')
                # await mark_new_point(marking, websocket, tool='burr')
            elif page in ['robot_workspace.html' , 'revrobot_workspace.html']:
                await primary_robot_workspace(marking, websocket)
            await asyncio.sleep(0.01)
    except WebSocketDisconnect:
        print("Client disconnected")
    except RuntimeError as e:
        print(f"RuntimeError: {e}")
    finally:
        # if websocket in marking.clients:
        marking.clients.discard(websocket)
