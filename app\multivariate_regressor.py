import os
import numpy as np
from sklearn.metrics import mean_squared_error
from sklearn.model_selection import train_test_split
from xarm.wrapper import XArmAPI
from sklearn.linear_model import HuberRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.preprocessing import RobustScaler

def load_data(file_path):
    led_points = []
    robot_points = []
    joint_angles = []

    with open(file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line and "angles" in line:
                # Parse LED point
                led_part = line.split("LED [")[1].split("]")[0].split()
                led_x, led_y, led_z = map(float, led_part)
                led_points.append([led_x, led_y, led_z])

                # Parse robot point
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")
                robot_x, robot_y, robot_z = map(float, robot_part)
                robot_points.append([robot_x, robot_y, robot_z])

                # Parse joint angles
                angles_part = line.split("angles (")[1].split(")")[0].split(", ")
                angles = list(map(float, angles_part))
                joint_angles.append(angles)

    return (
        np.array(led_points),
        np.array(robot_points),
        np.array(joint_angles)
    )

def find_optimal_epsilon_from_files(
    led_file_path,
    robot_file_path,
    eps_range=(1.0, 5.0),
    step=0.05,
    verbose=False
):
    # --- Load LED Points ---
    led_points = np.loadtxt(led_file_path, delimiter=",", skiprows=1)

    # --- Load Robot Points ---
    robot_points = []
    with open(robot_file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line:
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")
                robot_x, robot_y, robot_z = map(float, robot_part)
                robot_points.append([robot_x, robot_y, robot_z])
    robot_points = np.array(robot_points)

    # --- Truncate to match length ---
    min_len = min(len(led_points), len(robot_points))
    led_points = led_points[:min_len]
    robot_points = robot_points[:min_len]

    # --- Swap Y and Z in LED coordinates ---
    led_points[:, [1, 2]] = led_points[:, [2, 1]]

    # --- Standardize ---
    led_scaler = StandardScaler()
    robot_scaler = StandardScaler()
    X = led_scaler.fit_transform(led_points)
    y = robot_scaler.fit_transform(robot_points)

    results = []
    for epsilon in np.arange(eps_range[0], eps_range[1], step):
        huber_models = []
        for i in range(y.shape[1]):
            model = HuberRegressor(epsilon=epsilon)
            model.fit(X, y[:, i])
            huber_models.append(model)

        preds_scaled = np.column_stack([model.predict(X) for model in huber_models])
        preds = robot_scaler.inverse_transform(preds_scaled)

        errors = np.linalg.norm(preds - robot_points, axis=1)
        max_err = np.max(errors)
        mean_err = np.mean(errors)
        rmse = np.sqrt(np.mean(errors ** 2))
        results.append((epsilon, max_err, mean_err, rmse))

    if verbose:
        print("epsilon\tmax_err\tmean_err\trmse")
        for epsilon, max_err, mean_err, rmse in results:
            print(f"{epsilon:.2f}\t{max_err:.4f}\t{mean_err:.4f}\t{rmse:.4f}")

    best = min(results, key=lambda x: x[3])
    if verbose:
        print(f"\nBest epsilon (lowest RMSE): {best[0]:.2f} (RMSE={best[3]:.4f})")

    return best[0]

# Driver code
current_dir = os.path.dirname(os.path.abspath(__file__))

robot_calib_data = os.path.join(
    current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
)
led_points, robot_points, joint_angles = load_data(file_path=robot_calib_data)

transformed_led_points = np.loadtxt(
    os.path.join(current_dir, "transformed_led_points_LHS_50_25_7_huber.txt"),
    delimiter=",",
    skiprows=1  # Skip header if present
)

transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]
# Assume transformed_led_points and joint_angles are already defined as numpy arrays

# LEDs -> Joint angles
led_robust_scaler = RobustScaler()
angles_robust_scaler = RobustScaler()

X_train_robust = led_robust_scaler.fit_transform(led_points)
y_train_robust = angles_robust_scaler.fit_transform(joint_angles)

mlp = MLPRegressor(hidden_layer_sizes=(64, 64), max_iter=1000)
mlp.fit(X_train_robust, y_train_robust)

def predict_joint_angles(points):
    points_scaled = led_robust_scaler.transform(points)
    preds_scaled = mlp.predict(points_scaled)
    return angles_robust_scaler.inverse_transform(preds_scaled)

# LEDs -> robot
huber_led_robust_scaler = RobustScaler()
huber_robot_robust_scaler = RobustScaler()

huber_X_train_robust = huber_led_robust_scaler.fit_transform(led_points)
huber_y_train_robust = huber_robot_robust_scaler.fit_transform(robot_points)

best_eps = find_optimal_epsilon_from_files(
    led_file_path=r"D:\SpineSurgery\pythonProject\app\transformed_led_points_LHS_50_25_7_huber.txt",
    robot_file_path=r"D:\SpineSurgery\robotic_calib_data\robotic_init_data.txt",
    verbose=False
)

huber_models = []
for i in range(robot_points.shape[1]):
    model = HuberRegressor(epsilon=best_eps)
    model.fit(huber_X_train_robust, huber_y_train_robust[:, i])  # CORRECT: scaled data
    huber_models.append(model)

def predict_robot(points):
    points_scaled = huber_led_robust_scaler.transform(points)
    preds_scaled = np.column_stack([model.predict(points_scaled) for model in huber_models])
    return huber_robot_robust_scaler.inverse_transform(preds_scaled)

# test_led = np.array([[543.73466755, 391.61388087, 381.97121537]])
# predicted_angles = predict_joint_angles(test_led)
# print("Predicted angles:", predicted_angles[0])
# predicted_robot = predict_robot(test_led)
# print("Predicted robot:", predicted_robot[0])

# true_angles = np.array([0.299535, 0.299858, -0.342461, -3.098596, 1.86728, 0.286096])
# print("True angles: ", true_angles)
# error = np.abs(predicted_angles[0] - true_angles)
# print("Absolute error:", error)
# rmse = np.sqrt(np.mean((predicted_angles[0] - true_angles) ** 2))
# print("RMSE on test LED:", rmse)

# Split for test (e.g., last sample as test)
X_train, X_test, y_train, y_test = train_test_split(transformed_led_points, joint_angles, test_size=1, shuffle=False)

# Scale data
led_robust_scaler = RobustScaler().fit(X_train)
angles_robust_scaler = RobustScaler().fit(y_train)

X_train_scaled = led_robust_scaler.transform(X_train)
y_train_scaled = angles_robust_scaler.transform(y_train)

# Train MLP
mlp = MLPRegressor(hidden_layer_sizes=(64, 64), max_iter=1000, random_state=0)
mlp.fit(X_train_scaled, y_train_scaled)

# Predict on test sample
y_pred = predict_joint_angles(X_test)

# Output results
print("🔹 Test LED point:", X_test[0])
print("🔹 True joint angles:", y_test[0])
print("🔹 Predicted joint angles:", y_pred[0])

# Optional: Evaluate error
abs_error = np.abs(y_test[0] - y_pred[0])
rmse = np.sqrt(mean_squared_error(y_test[0], y_pred[0]))

print("🔹 Absolute error per joint:", abs_error)
print("🔹 RMSE on test point:", rmse)