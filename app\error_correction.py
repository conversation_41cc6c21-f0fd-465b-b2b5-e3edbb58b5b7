import os
import numpy as np
from xarm.wrapper import XArmAPI
from sklearn.linear_model import HuberRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.preprocessing import RobustScaler

def load_data(file_path):
    led_points = []
    robot_points = []

    with open(file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line:
                led_part = line.split("LED [")[1].split("]")[0].split()
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")

                # Convert to float
                led_x, led_y, led_z = map(float, led_part)
                robot_x, robot_y, robot_z = map(float, robot_part)

                led_points.append([led_x, led_y, led_z])
                robot_points.append([robot_x, robot_y, robot_z])

    return np.array(led_points), np.array(robot_points)

def find_optimal_epsilon_from_files(
    led_file_path,
    robot_file_path,
    eps_range=(1.0, 5.0),
    step=0.05,
    verbose=False
):
    # --- Load LED Points ---
    led_points = np.loadtxt(led_file_path, delimiter=",", skiprows=1)

    # --- Load Robot Points ---
    robot_points = []
    with open(robot_file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line:
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")
                robot_x, robot_y, robot_z = map(float, robot_part)
                robot_points.append([robot_x, robot_y, robot_z])
    robot_points = np.array(robot_points)

    # --- Truncate to match length ---
    min_len = min(len(led_points), len(robot_points))
    led_points = led_points[:min_len]
    robot_points = robot_points[:min_len]

    # --- Swap Y and Z in LED coordinates ---
    led_points[:, [1, 2]] = led_points[:, [2, 1]]

    # --- Standardize ---
    led_scaler = StandardScaler()
    robot_scaler = StandardScaler()
    X = led_scaler.fit_transform(led_points)
    y = robot_scaler.fit_transform(robot_points)

    results = []
    for epsilon in np.arange(eps_range[0], eps_range[1], step):
        huber_models = []
        for i in range(y.shape[1]):
            model = HuberRegressor(epsilon=epsilon)
            model.fit(X, y[:, i])
            huber_models.append(model)

        preds_scaled = np.column_stack([model.predict(X) for model in huber_models])
        preds = robot_scaler.inverse_transform(preds_scaled)

        errors = np.linalg.norm(preds - robot_points, axis=1)
        max_err = np.max(errors)
        mean_err = np.mean(errors)
        rmse = np.sqrt(np.mean(errors ** 2))
        results.append((epsilon, max_err, mean_err, rmse))

    if verbose:
        print("epsilon\tmax_err\tmean_err\trmse")
        for epsilon, max_err, mean_err, rmse in results:
            print(f"{epsilon:.2f}\t{max_err:.4f}\t{mean_err:.4f}\t{rmse:.4f}")

    best = min(results, key=lambda x: x[3])
    if verbose:
        print(f"\nBest epsilon (lowest RMSE): {best[0]:.2f} (RMSE={best[3]:.4f})")

    return best[0]


# Driver code
ip = "*************"
arm = XArmAPI(ip)

current_dir = os.path.dirname(os.path.abspath(__file__))

robot_calib_data = os.path.join(
    current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
)
led_points, robot_points = load_data(file_path=robot_calib_data)

transformed_led_points = np.loadtxt(
    os.path.join(current_dir, "transformed_led_points_LHS_50_23_7_huber.txt"),
    delimiter=",",
    skiprows=1  # Skip header if present
)

transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

led_robust_scaler = RobustScaler()  
robot_robust_scaler = RobustScaler()

X_train_robust = led_robust_scaler.fit_transform(transformed_led_points)
y_train_robust = robot_robust_scaler.fit_transform(robot_points)   

best_eps = find_optimal_epsilon_from_files(
    led_file_path=r"D:\SpineSurgery\pythonProject\app\transformed_led_points_LHS_50_23_7_huber.txt",
    robot_file_path=r"D:\SpineSurgery\robotic_calib_data\robotic_init_data.txt",
    verbose=False
) 

huber_models = []
for i in range(robot_points.shape[1]):
    model = HuberRegressor(epsilon=best_eps)
    # model.fit(transformed_led_points, robot_points[:, i])
    model.fit(X_train_robust, y_train_robust[:, i])  # CORRECT: scaled data
    huber_models.append(model)

huber_models_robot2led = []
for i in range(X_train_robust.shape[1]):
    model = HuberRegressor(epsilon=best_eps)
    model.fit(y_train_robust, X_train_robust[:, i])
    huber_models_robot2led.append(model)

def predict_robot(points):
    points_scaled = led_robust_scaler.transform(points)
    preds_scaled = np.column_stack([model.predict(points_scaled) for model in huber_models])
    return robot_robust_scaler.inverse_transform(preds_scaled)

def predict_led(points):
    points_scaled = robot_robust_scaler.transform(points)
    preds_scaled = [model.predict(points_scaled)[0] for model in huber_models_robot2led]
    return led_robust_scaler.inverse_transform([preds_scaled])[0]

robot_led_point = [[538.06103887, 399.12424787, 406.29463257]]
optical_point = [[536.54836643, 390.95571942, 372.85532534]]
# robot_led_point[0][1], robot_led_point[0][2] = robot_led_point[0][2], robot_led_point[0][1]
# optical_point[0][1], optical_point[0][2] = optical_point[0][2], optical_point[0][1]

code, robot_point = arm.get_position()
if code == 0:
    x, y, z, roll, pitch, yaw = robot_point

current_position = [[x, y, z]]
# led_predicted = predict_led(current_position)
# led_predicted[[1, 2]] = led_predicted[[2, 1]]

# print(f"\nCurrent robot position: {robot_point}")
# print(f"Predicted LED point: {led_predicted}")
# print(f"Optical point: {optical_point}")

# translation_vector = optical_point - led_predicted
# print(f'\nTranslation vector: {translation_vector}')

# robot_translation_vector = predict_robot(translation_vector)
# print(f'Robot translation vector: {robot_translation_vector}')

# new_position = current_position + robot_translation_vector
# print(f'\nNew position: {new_position}')

predicted_robot_point = predict_robot(robot_led_point)
destination = predict_robot(optical_point)
print(f'Predicted robot point: {predicted_robot_point}')
print(f'Destination: {destination}')

translation_vector = destination - predicted_robot_point
print(f'Translation vector: {translation_vector}')

new_position = current_position + translation_vector
print(f'New position: {new_position}')

arm.motion_enable(True)
arm.set_mode(0)
arm.set_state(0)

code = arm.set_position(
    x=new_position[0][0],
    y=new_position[0][1],
    z=new_position[0][2],
    roll=roll,
    pitch=pitch,
    yaw=yaw,
    speed=25,
    mvacc=50,
    wait=True,
)

if code == 0:
    print("Successfully moved to the desired position!")
else:
    print(f"Failed to move the arm. Error code: {code}")