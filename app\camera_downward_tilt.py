import json
import os
import numpy as np

def correct_downward_tilt(points_3d, pitch_deg):
    """
    Corrects 3D points for a known downward tilt (pitch) angle.

    Args:
        points_3d (np.array): Nx3 array of 3D points (X, Y, Z) in camera coordinates.
        pitch_deg (float): Downward tilt (pitch) angle in degrees.

    Returns:
        np.array: Nx3 array of corrected 3D points.
    """
    # Convert pitch angle from degrees to radians
    pitch_rad = np.deg2rad(pitch_deg)

    # Rotation matrix for pitch (about x-axis)
    R_pitch = np.array([
        [1, 0, 0],
        [0, np.cos(pitch_rad), np.sin(pitch_rad)],
        [0, -np.sin(pitch_rad), np.cos(pitch_rad)]
    ])

    # Inverse rotation to correct tilt
    R_inv = np.linalg.inv(R_pitch)
    corrected_points = (R_inv @ points_3d.T).T
    return corrected_points

def compute_rigid_transform(A, B):
    centroid_A = np.mean(A, axis=0)
    centroid_B = np.mean(B, axis=0)
    AA = A - centroid_A
    BB = B - centroid_B
    H = AA.T @ BB
    U, _, Vt = np.linalg.svd(H)
    R = Vt.T @ U.T
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = Vt.T @ U.T
    t = centroid_B - R @ centroid_A
    return R, t

def load_calibration(filename=None):
    CALIBRATION_FILE = os.path.join(
        current_dir, "..", "..", "registration_data", f"knee_tooltip_calibration{filename}.json"
    )
    with open(CALIBRATION_FILE, 'r') as f:
        data = json.load(f)
    tip_local = np.array(data['tip_local'])
    model_leds = np.array(data['model_leds'])
    return tip_local, model_leds

def estimate_tip_position(current_leds, model_leds, tip_local):
    R, t = compute_rigid_transform(model_leds, current_leds)
    return R @ tip_local + t

# Example usage:
pitch_angle = 16  # degrees

current_dir = os.path.dirname(os.path.abspath(__file__))  # Ensure current_dir is defined
tool='Pointer'
tip_local, model_leds = load_calibration(filename=tool)

############# CASE 1
# femur_plane = np.array([
#     [521.69907582, 462.38270248, 289.4177201 ],
#     [520.87936874, 469.02280502, 287.35799545],
#     [532.67618037, 467.4717195 , 287.7749141 ],
# ])
# behind_plane = np.array([
#     [549.11294826, 470.46760683, 324.74157672],
#     [548.15350294, 477.56306697, 323.12749003],
#     [559.26025295, 475.16786619, 322.38581162],
# ])

# front_plane = np.array([
#     [545.67670301, 458.54568533, 260.15703952],
#     [545.17497017, 465.79516844, 258.46653358],
#     [556.186012  , 462.80754237, 258.29646267],
# ])


# corrected_femur_plane = correct_downward_tilt(femur_plane, pitch_angle)
# corrected_behind_plane = correct_downward_tilt(behind_plane, pitch_angle)
# corrected_front_plane = correct_downward_tilt(front_plane, pitch_angle)

# print("Original Femur Plane:", femur_plane)
# print("Corrected Femur Plane:", corrected_femur_plane)
# print('\n')
# print("Original Behind Plane:", behind_plane)
# print("Corrected Behind Plane:", corrected_behind_plane)
# print('\n')
# print("Original Front Plane:", front_plane)
# print("Corrected Front Plane:", corrected_front_plane)

############# CASE 2
# robot_plane = np.array([
#     [471.6461022 , 496.90788391, 294.19337247],
#     [479.37841572, 496.59597892, 293.26009609],
#     [475.63820152, 485.7033275 , 297.64078192],
# ])

# in_plane = np.array([
#     [510.34903196, 496.5048472 , 292.49855873],
#     [517.74545894, 496.49395464, 292.53775543],
#     [514.31475504, 486.06856179, 296.22182857],
# ])

# behind_plane = np.array([
#     [513.84035309, 502.80471601, 321.45131828],
#     [521.18422788, 502.82126903, 321.42783999],
#     [517.89023789, 492.25556602, 324.9324282 ],
# ])

# front_plane = np.array([
#     [508.08083357, 490.62921165, 276.59281452],
#     [515.49199994, 491.24413834, 276.68064218],
#     [512.78941375, 480.43621957, 280.44929122],
# ])

# corrected_robot_plane = correct_downward_tilt(robot_plane, pitch_angle)
# corrected_in_plane = correct_downward_tilt(in_plane, pitch_angle)
# corrected_behind_plane = correct_downward_tilt(behind_plane, pitch_angle)
# corrected_front_plane = correct_downward_tilt(front_plane, pitch_angle)

# print("Original Robot Plane:", robot_plane)
# print("Corrected Robot Plane:", corrected_robot_plane)
# print('\n')
# print("Original In Plane:", in_plane)
# print("Corrected In Plane:", corrected_in_plane)
# print('\n')
# print("Original Behind Plane:", behind_plane)
# print("Corrected Behind Plane:", corrected_behind_plane)
# print('\n')
# print("Original Front Plane:", front_plane)
# print("Corrected Front Plane:", corrected_front_plane)


plane_1 = np.array([
    [541.23842228, 470.50389523, 271.57120543], 
    [548.58165169, 470.3305064 , 271.6066118 ], 
    [544.56402928, 459.50978119, 270.3013571 ],
])

plane_2 = np.array([
    [541.01194411, 471.94241285, 276.17292959], 
    [548.29495545, 471.93880841, 275.16371892], 
    [544.5762989 , 460.9082173 , 275.38441421],
])

plane_3 = np.array([
    [541.02474168, 473.07637078, 279.76675656], 
    [548.32985951, 473.08326246, 279.19692456], 
    [544.6544714 , 462.0600289 , 279.45420566],
])

plane_4 = np.array([
    [541.8514996 , 474.08837347, 283.24367465], 
    [549.12575383, 473.94159058, 282.19694483], 
    [545.27364912, 463.09470859, 282.91345855],
])

plane_5 = np.array([
    [543.8227015 , 478.42044678, 302.11997707], 
    [551.2206122 , 478.49938621, 302.5833907 ], 
    [547.61810308, 467.5743177 , 302.18985513],
])

plane_6 = np.array([
    [543.796028  , 480.15845896, 308.62816107], 
    [551.16360358, 480.27552158, 308.63926749], 
    [547.72982534, 469.31864527, 308.90307448],
])

plane_7 = np.array([
    [545.37871777, 482.50685428, 318.32662449], 
    [552.68465487, 482.23409853, 318.21910661], 
    [548.7158853 , 471.46538822, 318.44872721],
])

plane_8 = np.array([
    [546.2169536 , 484.77447629, 327.60006021], 
    [553.549832  , 484.41557465, 327.34733597],
    [549.48776205, 473.73422666, 328.31054598],
])

plane_9 = np.array([
    [546.36655823, 486.51565476, 334.50481381], 
    [553.69857161, 485.94851602, 334.38996825], 
    [549.39101138, 475.47221885, 335.0159469 ],
])

plane_10 = np.array([
    [545.12820744, 488.98632897, 344.34791032], 
    [552.42559557, 488.60219484, 343.64994438], 
    [548.47264647, 478.10386619, 345.45995841],
])


corrected_plane_1 = correct_downward_tilt(plane_1, pitch_angle)
corrected_plane_2 = correct_downward_tilt(plane_2, pitch_angle)
corrected_plane_3 = correct_downward_tilt(plane_3, pitch_angle)
corrected_plane_4 = correct_downward_tilt(plane_4, pitch_angle)
corrected_plane_5 = correct_downward_tilt(plane_5, pitch_angle)
corrected_plane_6 = correct_downward_tilt(plane_6, pitch_angle)
corrected_plane_7 = correct_downward_tilt(plane_7, pitch_angle)
corrected_plane_8 = correct_downward_tilt(plane_8, pitch_angle)
corrected_plane_9 = correct_downward_tilt(plane_9, pitch_angle)
corrected_plane_10 = correct_downward_tilt(plane_10, pitch_angle)

tip_1 = estimate_tip_position(corrected_plane_1, model_leds, tip_local)
tip_2 = estimate_tip_position(corrected_plane_2, model_leds, tip_local)
tip_3 = estimate_tip_position(corrected_plane_3, model_leds, tip_local)
tip_4 = estimate_tip_position(corrected_plane_4, model_leds, tip_local)
tip_5 = estimate_tip_position(corrected_plane_5, model_leds, tip_local)
tip_6 = estimate_tip_position(corrected_plane_6, model_leds, tip_local)
tip_7 = estimate_tip_position(corrected_plane_7, model_leds, tip_local)
tip_8 = estimate_tip_position(corrected_plane_8, model_leds, tip_local)
tip_9 = estimate_tip_position(corrected_plane_9, model_leds, tip_local)
tip_10 = estimate_tip_position(corrected_plane_10, model_leds, tip_local)

print('================== Original planes ================== ')
print('plane_1 : \n', plane_1, '\n')
print('plane_2 : \n', plane_2, '\n')
print('plane_3 : \n', plane_3, '\n')
print('plane_4 : \n', plane_4, '\n')
print('plane_5 : \n', plane_5, '\n')
print('plane_6 : \n', plane_6, '\n')
print('plane_7 : \n', plane_7, '\n')
print('plane_8 : \n', plane_8, '\n')
print('plane_9 : \n', plane_9, '\n')
print('plane_10 : \n', plane_10, '\n')
print('\n')

print('================== Corrected planes ================== ')
print('plane_1 : \n', corrected_plane_1, '\n')
print('plane_2 : \n', corrected_plane_2, '\n')
print('plane_3 : \n', corrected_plane_3, '\n')
print('plane_4 : \n', corrected_plane_4, '\n')
print('plane_5 : \n', corrected_plane_5, '\n')
print('plane_6 : \n', corrected_plane_6, '\n')
print('plane_7 : \n', corrected_plane_7, '\n')
print('plane_8 : \n', corrected_plane_8, '\n')
print('plane_9 : \n', corrected_plane_9, '\n')
print('plane_10 : \n', corrected_plane_10, '\n')
print('\n')

print('================== Corrected tip ================== ')
print('tip_1 : ', tip_1, '\n')
print('tip_2 : ', tip_2, '\n')
print('tip_3 : ', tip_3, '\n')
print('tip_4 : ', tip_4, '\n')
print('tip_5 : ', tip_5, '\n')
print('tip_6 : ', tip_6, '\n')
print('tip_7 : ', tip_7, '\n')
print('tip_8 : ', tip_8, '\n')
print('tip_9 : ', tip_9, '\n')
print('tip_10 : ', tip_10, '\n')

print(plane_1)