import cv2
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KM<PERSON><PERSON>

def analyze_fiducial_image(image_path):
    """
    Analyze an image containing fiducial markers (IR LEDs on black background)
    """
    
    # Read the image
    img = cv2.imread(image_path)
    if img is None:
        print(f"Could not load image: {image_path}")
        return None
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Apply threshold to isolate bright points (LEDs)
    _, binary = cv2.threshold(gray, 50, 255, cv2.THRESH_BINARY)
    
    # Find contours
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Extract centroid points of bright regions
    points = []
    for contour in contours:
        # Only consider significant contours (area > 5 pixels)
        if cv2.contourArea(contour) > 5:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                points.append([cx, cy])
    
    # Convert to numpy array
    points = np.array(points)
    
    if len(points) < 3:
        print(f"Found only {len(points)} points, need at least 3")
        return None
    
    print(f"Found {len(points)} bright points in image")
    
    # If more than 3 points, cluster them to get 3 main groups
    if len(points) > 3:
        kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
        labels = kmeans.fit_predict(points)
        
        # Get centroid of each cluster
        clustered_points = []
        for i in range(3):
            cluster_points = points[labels == i]
            if len(cluster_points) > 0:
                centroid = np.mean(cluster_points, axis=0)
                clustered_points.append(centroid)
        
        points = np.array(clustered_points)
    
    if len(points) != 3:
        print(f"Could not reduce to exactly 3 points: got {len(points)}")
        return None
    
    # Sort points (optional - for consistency)
    # Assign points based on position
    points = points.astype(float)
    
    # Create 3D points (assuming z=0 for 2D image)
    F_points = np.array([[p[0], p[1], 0] for p in points])
    
    # Print analysis
    print("F_points triangle analysis:")
    print(f"F_points[0]: {F_points[0]}")
    print(f"F_points[1]: {F_points[1]}") 
    print(f"F_points[2]: {F_points[2]}")
    print(f"Side lengths:")
    print(f"  F1-F2: {np.linalg.norm(F_points[0]-F_points[1]):.2f}")
    print(f"  F2-F3: {np.linalg.norm(F_points[1]-F_points[2]):.2f}")
    print(f"  F3-F1: {np.linalg.norm(F_points[2]-F_points[0]):.2f}")
    print(f"  Perimeter: {np.linalg.norm(F_points[0]-F_points[1]) + np.linalg.norm(F_points[1]-F_points[2]) + np.linalg.norm(F_points[2]-F_points[0]):.2f}")
    
    # Calculate area using cross product
    v1 = F_points[1] - F_points[0]
    v2 = F_points[2] - F_points[0]
    area = 0.5 * np.linalg.norm(np.cross(v1, v2))
    print(f"Triangle area: {area:.2f} pixel²")
    
    # Visualize
    plt.figure(figsize=(12, 8))
    plt.imshow(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    plt.scatter(points[:, 0], points[:, 1], c='red', s=100, marker='x')
    for i, point in enumerate(points):
        plt.annotate(f'P{i}', (point[0], point[1]), xytext=(5, 5), 
                    textcoords='offset points', color='yellow', fontsize=12)
    
    # Draw triangle
    triangle_points = np.vstack([points, points[0]])  # Close the triangle
    plt.plot(triangle_points[:, 0], triangle_points[:, 1], 'yellow', linewidth=2)
    
    plt.title('Fiducial Marker Analysis')
    plt.axis('off')
    plt.show()
    
    return F_points

# Usage example:
if __name__ == "__main__":
    # Replace with your image path
    import cv2
    import os

    # Check if file exists
    image_path = "img.bmp"
    if os.path.exists(image_path):
        print(f"File found: {image_path}")
        img = cv2.imread(image_path)
        if img is not None:
            print(f"Image loaded successfully: {img.shape}")
        else:
            print("File exists but couldn't be read - might be corrupted")
    else:
        print(f"File not found: {image_path}")
        print("Files in current directory:")
        for file in os.listdir('.'):
            print(f"  {file}")
    
    F_points = analyze_fiducial_image(image_path)
    
    if F_points is not None:
        # Calculate scale factor (you'll need to provide real_area)
        real_area = 3446.65  # Your known real area in mm²
        v1 = F_points[1] - F_points[0]
        v2 = F_points[2] - F_points[0]
        camera_area = 0.5 * np.linalg.norm(np.cross(v1, v2))
        
        if camera_area > 1e-6:
            scale_factor = np.sqrt(real_area / camera_area)
            print(f"\nScale factor calculation:")
            print(f"Camera area: {camera_area:.2f} pixel²")
            print(f"Real area: {real_area} mm²")
            print(f"Scale factor: {scale_factor:.2f} mm/pixel")
        else:
            print("Cannot calculate scale factor - degenerate triangle")