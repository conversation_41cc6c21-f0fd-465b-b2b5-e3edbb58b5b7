<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                         <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">07/01/2025</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul>
                                <li>Orient Robotic Arm in Field</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center justify-content-center">
                                        <div class="col-6">
                                            <div class="Bone-box-style pb-80">
                                                <!--div class="Bone-text">
                                                    <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                        <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                        <span id="textbox1">0.0</span>
                                                        <sup class="fs-14">0</sup>
                                                        <span class="input-btn-text text-white">ext</span>
                                                        <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                    </div>
                                                </div--> 
                                                <!-- Replace robot image and canvas block with a single grey canvas -->
                                                <div style="position: relative; display: inline-block;">
                                                    <canvas id="robotCanvas" width="800" height="500" style="background: #888; display: block;"></canvas>
                                                </div>
                                                <!--div class="Bone-text bottom-input">
                                                    <div class="d-flex align-items-center">
                                                        <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between br-rounded">
                                                            <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                            <span id="textbox2">0.0</span>
                                                            <sup class="fs-14">0</sup>
                                                            <span class="input-btn-text text-white">mm</span>
                                                            <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                        </div>
                                                        <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between bl-rounded">
                                                            <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                            <span id="textbox3">0.0</span>
                                                            <sup class="fs-14">0</sup>
                                                            <span class="input-btn-text text-white">mm</span>
                                                            <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                        </div>
                                                    </div>
                                                </div--> 
                                            </div>
                                        </div>
                                    </div>
									<div class="note">
										<p class="note_txt">Notes:</p>
										<p>
											<ul>
												<li>Position Robot in the field as illustrated </li>
											</ul>
										</p>
									</div>
                                </div>
                                <div class="bottom_btn">
                                    <!--div class="btn">
										Robot in position
                                        
                                    </div-->
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="tkr-screen-3.html">
                                            <span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a href="robot_position.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
				</div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../static/images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../static/images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>
<script>
   document.addEventListener('DOMContentLoaded', function() {
    if (window.wsHandler) {
        setTimeout(function() {
            window.wsHandler.send({ file: "Robot_intoduction.html" });
            console.log("Sent page name to backend");
        }, 500); // Wait 500ms to ensure connection is open
    }
});
</script>
<script>  
    // Listen for WebSocket messages
    window.addEventListener('websocket-message', function(event) {
        console.log("WebSocket message received:", event.detail);
        const data = event.detail;
        const [type, rest] = data.split(",(");
        const arrayStrings = rest
        .slice(0, -1) // remove trailing ')'
        .match(/array\(\[.*?\]\)/g); // match all array([...]) parts

        const points = arrayStrings.map(str => {
        const inside = str.match(/\[(.*?)\]/)[1]; // get inside brackets
        return inside.split(',').map(Number); // convert to numbers
        });

        const result = {
        type,
        points
        };

        // If you use send_tuple(websocket, ("robot_points", Robot_points))
        if (result.type === "robot_points") {
            drawRobotPoints(result.points);
        }
        else{
            console.log("Received data is not an array or does not start with 'robot_points'");
        }
    });
</script>
<script>
    // Preload the robot image
    const robotImage = new Image();
    robotImage.src = "../static/images/robo/R_r.png"; // Path to the image
    
    function drawRobotPoints(points) {
        const canvas = document.getElementById('robotCanvas');
        const ctx = canvas.getContext('2d');

        // Canvas dimensions
        const canvasWidth = canvas.width; // 800
        const canvasHeight = canvas.height; // 500

        // Optical (real world) range - set these to your expected min/max for x and z
        // You may need to adjust these for your actual data range
        const opticalXMin = 350; // example min x
        const opticalXMax = 600; // example max x
        const opticalZMin = 415; // example min z (close)
        const opticalZMax = 440; // example max z (far)

        // Clear previous drawings
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // Calculate average z for scaling
        const zValues = points.map(pt => Array.isArray(pt) ? pt[2] : (pt[2] ?? pt.z ?? 0));
        const avgZ = zValues.reduce((a, b) => a + b, 0) / zValues.length;

        // Map z to a scale factor: closer (smaller z) = larger, farther (bigger z) = smaller
        // You can tweak minScale and maxScale for effect
        const minScale = 2; // largest size (closest)
        const maxScale = 0.7; // smallest size (farthest)
        let scale = maxScale + (minScale - maxScale) * (opticalZMax - avgZ) / (opticalZMax - opticalZMin);
        scale = Math.max(Math.min(scale, minScale), maxScale); // clamp

        // Map points: x (optical x) -> canvas x, z (optical z) -> canvas y
        const mappedPoints = points.map(pt => {
            let x, z;
            if (Array.isArray(pt)) {
                [x, , z] = pt;
            } else if (typeof pt === 'object' && pt !== null) {
                x = pt[0] ?? pt.x;
                z = pt[2] ?? pt.z;
            }
            x = Number(x) || 0;
            z = Number(z) || 0;

            // Map x from opticalXMin..opticalXMax to 0..canvasWidth
            let mappedX = ((x - opticalXMin) / (opticalXMax - opticalXMin)) * canvasWidth;
            // Map z from opticalZMin..opticalZMax to 0..canvasHeight (invert so closer is lower y)
            let mappedY = ((z - opticalZMin) / (opticalZMax - opticalZMin)) * canvasHeight;
            mappedY = canvasHeight - mappedY; // invert y so closer is lower on canvas

            // Center scaling around canvas center
            const centerX = canvasWidth / 2;
            const centerY = canvasHeight / 2;
            mappedX = centerX + (mappedX - centerX) * scale;
            mappedY = centerY + (mappedY - centerY) * scale;

            return [mappedX, mappedY];
        });

        // Draw triangle if at least 3 points are available
        if (mappedPoints.length >= 3) {
            const [p1, p2, p3] = mappedPoints;

            // Draw the triangle
            ctx.beginPath();
            ctx.moveTo(p1[0], p1[1]);
            ctx.lineTo(p2[0], p2[1]);
            ctx.lineTo(p3[0], p3[1]);
            ctx.closePath();

            // Style the triangle
            ctx.strokeStyle = 'blue';
            ctx.lineWidth = 5;
            ctx.stroke();

            // Fill the triangle with a semi-transparent color
            ctx.fillStyle = 'rgba(0, 0, 255, 1)';
            ctx.fill();
        }
    }

</script>   
<script type="text/javascript">
</script>
</html>