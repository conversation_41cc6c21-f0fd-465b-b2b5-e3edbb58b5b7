import os
import numpy as np
from xarm.wrapper import XArmAPI
from sklearn.linear_model import HuberRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.preprocessing import RobustScaler

def load_data(file_path):
    led_points = []
    robot_points = []
    joint_angles = []

    with open(file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line and "angles" in line:
                # Parse LED point
                led_part = line.split("LED [")[1].split("]")[0].split()
                led_x, led_y, led_z = map(float, led_part)
                led_points.append([led_x, led_y, led_z])

                # Parse robot point
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")
                robot_x, robot_y, robot_z = map(float, robot_part)
                robot_points.append([robot_x, robot_y, robot_z])

                # Parse joint angles
                angles_part = line.split("angles (")[1].split(")")[0].split(", ")
                angles = list(map(float, angles_part))
                joint_angles.append(angles)

    return (
        np.array(led_points),
        np.array(robot_points),
        np.array(joint_angles)
    )

def find_optimal_epsilon_from_files(
    led_file_path,
    robot_file_path,
    eps_range=(1.0, 5.0),
    step=0.05,
    verbose=False
):
    # --- Load LED Points ---
    led_points = np.loadtxt(led_file_path, delimiter=",", skiprows=1)

    # --- Load Robot Points ---
    robot_points = []
    with open(robot_file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line:
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")
                robot_x, robot_y, robot_z = map(float, robot_part)
                robot_points.append([robot_x, robot_y, robot_z])
    robot_points = np.array(robot_points)

    # --- Truncate to match length ---
    min_len = min(len(led_points), len(robot_points))
    led_points = led_points[:min_len]
    robot_points = robot_points[:min_len]

    # --- Swap Y and Z in LED coordinates ---
    led_points[:, [1, 2]] = led_points[:, [2, 1]]

    # --- Standardize ---
    led_scaler = StandardScaler()
    robot_scaler = StandardScaler()
    X = led_scaler.fit_transform(led_points)
    y = robot_scaler.fit_transform(robot_points)

    results = []
    for epsilon in np.arange(eps_range[0], eps_range[1], step):
        huber_models = []
        for i in range(y.shape[1]):
            model = HuberRegressor(epsilon=epsilon)
            model.fit(X, y[:, i])
            huber_models.append(model)

        preds_scaled = np.column_stack([model.predict(X) for model in huber_models])
        preds = robot_scaler.inverse_transform(preds_scaled)

        errors = np.linalg.norm(preds - robot_points, axis=1)
        max_err = np.max(errors)
        mean_err = np.mean(errors)
        rmse = np.sqrt(np.mean(errors ** 2))
        results.append((epsilon, max_err, mean_err, rmse))

    if verbose:
        print("epsilon\tmax_err\tmean_err\trmse")
        for epsilon, max_err, mean_err, rmse in results:
            print(f"{epsilon:.2f}\t{max_err:.4f}\t{mean_err:.4f}\t{rmse:.4f}")

    best = min(results, key=lambda x: x[3])
    if verbose:
        print(f"\nBest epsilon (lowest RMSE): {best[0]:.2f} (RMSE={best[3]:.4f})")

    return best[0]

# Driver code
current_dir = os.path.dirname(os.path.abspath(__file__))

robot_calib_data = os.path.join(
    current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
)
led_points, robot_points, joint_angles = load_data(file_path=robot_calib_data)

transformed_led_points = np.loadtxt(
    os.path.join(current_dir, "transformed_led_points_LHS_50_23_7_huber.txt"),
    delimiter=",",
    skiprows=1  # Skip header if present
)

transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]
# Assume transformed_led_points and joint_angles are already defined as numpy arrays
led_robust_scaler = RobustScaler()
angles_robust_scaler = RobustScaler()

X_train_robust = led_robust_scaler.fit_transform(transformed_led_points)
y_train_robust = angles_robust_scaler.fit_transform(joint_angles)

best_eps = find_optimal_epsilon_from_files(
    led_file_path=r"D:\SpineSurgery\pythonProject\app\transformed_led_points_LHS_50_23_7_huber.txt",
    robot_file_path=r"D:\SpineSurgery\robotic_calib_data\robotic_init_data.txt",
    verbose=False
) 

huber_models_angles = []
for i in range(joint_angles.shape[1]):  # 6 joint angles
    model = HuberRegressor(epsilon=best_eps)
    model.fit(X_train_robust, y_train_robust[:, i])
    huber_models_angles.append(model)

def predict_joint_angles(led_points):
    # led_points: shape (N, 3)
    led_scaled = led_robust_scaler.transform(led_points)
    pred_scaled = np.column_stack([model.predict(led_scaled) for model in huber_models_angles])
    return angles_robust_scaler.inverse_transform(pred_scaled)

test_led = np.array([[524.63996315, 386.25593328, 394.25265392]])
predicted_angles = predict_joint_angles(test_led)
print("Predicted angles:", predicted_angles[0])
true_angles = np.array([0.299535, 0.299858, -0.342461, -3.098596, 1.86728, 0.286096])
print("True angles: ", true_angles)
error = np.abs(predicted_angles[0] - true_angles)
print("Absolute error:", error)
rmse = np.sqrt(np.mean((predicted_angles[0] - true_angles) ** 2))
print("RMSE on test LED:", rmse)
