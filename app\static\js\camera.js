let activeWebSocket = null;

function openVideoStream() {
    const modal = document.getElementById('cameraModal');
    modal.style.display = 'block';
    connectWebSocket();
}

function closeVideoStream() {
    if (activeWebSocket) {
        activeWebSocket.close();
        activeWebSocket = null;
    }
    const modal = document.getElementById('cameraModal');
    modal.style.display = 'none';
}

function connectWebSocket() {
    if (activeWebSocket) {
        activeWebSocket.close();
    }

    activeWebSocket = new WebSocket('ws://127.0.0.1:8000/ws');

    activeWebSocket.onopen = function() {
        console.log('WebSocket connected');
        activeWebSocket.send(JSON.stringify({ file: "video" }));
    };

    activeWebSocket.onmessage = function(event) {
        const imageElement = document.getElementById('videoStream');
        if (imageElement) {
            imageElement.src = 'data:image/jpeg;base64,' + event.data;
        }
    };

    activeWebSocket.onerror = function(error) {
        console.error('WebSocket error:', error);
    };

    activeWebSocket.onclose = function() {
        console.log('WebSocket closed');
        activeWebSocket = null;
    };
}

// Attach event listeners
document.addEventListener('DOMContentLoaded', function() {
    document.querySelector('.btn.first').addEventListener('click', openVideoStream);
    document.querySelector('.close-camera').addEventListener('click', closeVideoStream);

    // Click outside modal to close
    window.onclick = function(event) {
        const modal = document.getElementById('cameraModal');
        if (event.target === modal) {
            closeVideoStream();
        }
    };

    // Clean up WebSocket on page unload
    window.onbeforeunload = function() {
        if (activeWebSocket) {
            activeWebSocket.close();
        }
    };
});
