import os
import threading
from collections import Counter
from scipy.spatial import cKDTree
import scipy.signal as scig

os.environ['OMP_NUM_THREADS'] = '1'
from sklearn.neighbors import NearestNeighbors
import cv2
import math
import warnings

warnings.simplefilter(action='ignore', category=FutureWarning)
from matplotlib import pyplot as plt
from sklearn.cluster import KMeans, DBSCAN

# import triangulation as tri
import itertools
from skimage.filters import threshold_otsu
import numpy as np

# from playsound import playsound

# REAL_LIFE_DISTANCE_OF_POINTER = 103.39
REAL_LIFE_DISTANCE_OF_POINTER_TIP = 168.88

REAL_LIFE_DISTANCE_OF_POINTER = 101.11
# REAL_LIFE_DISTANCE_OF_POINTER_TIP = 133.58

# B = 22.0              #Distance between the cameras [cm]
B = 17.5  #Distance between the cameras [cm]  111.70
f = 12  # Camera lense's focal length [mm]
alpha = 39.0  # Camera field of view in the horisontal plane [degrees] 37.88

d1 = 700  # mm //distance of Rec tracker in X
d2 = 430  # mm distance of Rec tracker in Y
r = 400  # mm Robo arm radius

from skimage import measure

MIN_THRESH = 160

refvec = [0, 1]
import simpleaudio as sa


def cleanup_old_data(point_data=''):
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, '../../kneeSequence/register_points/pointRegistered.txt')
    # Ensure the directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    if not os.path.exists(file_path):
        with open(file_path, 'w') as file:  # Create an empty file
            file.write('')  # Optionally, write initial content
        return  # or handle this case as needed
    with open(file_path, 'w') as f:
        f.write(point_data)  # Convert data to string and write

    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, '../../kneeSequence/register_points/data.txt')
    if not os.path.exists(file_path):
        return  # or
    os.remove(file_path)


def SetplanningInputs(filename='', data=''):
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, f'../../kneeSequence/registration_data/{filename}.txt')
    with open(file_path, 'w') as f:
        f.write(data)  # Convert data to string and write


def GetplanningInput(filename=''):
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, f'../../kneeSequence/registration_data/{filename}.txt')

    default_values = {
        'femure_input1': 9.0,
        'femure_input2': 0.0,
        'femure_input3': 3.0,
        'femure_input4': 0.0,
        'femure_input5': 0.0,
        'femure_input6': 0.0,
        'tibia_input1': 0.0,
        'tibia_input2': 0.0,
        'tibia_input3': 3.0
    }

    valid_ranges = {
        'femure_input1': (5.0, 13.0),
        'femure_input2': (-3.0, 3.0),
        'femure_input3': (-3.0, 3.0),
        'femure_input4': (0.0, 5.0),
        'femure_input5': (-1.0, 3.0),
        'femure_input6': (-1.0, 2.0),
        'tibia_input1': (-3.0, 3.0),
        'tibia_input2': (5.0, 13.0),
        'tibia_input3': (0.0, 13.0)
    }

    if not os.path.exists(file_path):
        return default_values.get(filename, None)

    with open(file_path, 'r') as f:
        value = f.readline().strip()

    try:
        value = float(value)
        min_val, max_val = valid_ranges.get(filename, (None, None))
        if min_val is not None and min_val <= value <= max_val:
            return value
    except ValueError:
        pass

lock = threading.Lock()


def SetLoopExitCondition(value):
    with lock:
        current_dir = os.path.dirname(__file__)
        file_path = os.path.join(current_dir, '../../kneeSequence/register_points/control.txt')
        with open(file_path, 'w') as f:
            f.write(str(value))  # Convert the value to string before writing


def checkLoopExitCondition():
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, '../../kneeSequence/register_points/control.txt')
    with open(file_path, 'r') as f:
        content = f.read().strip()  # Strip to remove any extra spaces or newlines
    return content


# Create a lock object
lock_write = threading.Lock()


def writeRegisteredPoint(point_data):
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, '../../kneeSequence/register_points/pointRegistered.txt')

    # Ensure the directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Acquire the lock before writing to the file
    with lock_write:
        # Write data to the file
        with open(file_path, 'w') as f:
            f.write(str(point_data))  # Convert data to string and write


def getRegisteredPoint():
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, '../../kneeSequence/register_points/pointRegistered.txt')

    # Acquire the lock before reading the file
    with lock_write:
        # Check if the file exists
        if not os.path.exists(file_path):
            return -1

        with open(file_path, 'r') as f:
            content = f.read().strip()  # Strip to remove any extra spaces or newlines
            if len(content) > 1:
                print(f'Content {content}')
                return content
            else:
                return -1


lock1 = threading.Lock()


def SendDataToFrontEnd(point_data):
    with lock1:
        current_dir = os.path.dirname(__file__)
        file_path = os.path.join(current_dir, '../../kneeSequence/register_points/data.txt')
        # Ensure the directory exists
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        # print(f'written data point_data {point_data}')
        # Write data to the file
        with open(file_path, 'w') as f:
            f.write(str(point_data))  # Convert data to string and write


def GetDataFromBackEnd():
    with lock1:
        current_dir = os.path.dirname(__file__)
        file_path = os.path.join(current_dir, '../../kneeSequence/register_points/data.txt')
        if not os.path.exists(file_path):
            return -1

        with open(file_path, 'r') as f:
            angle = f.read().strip()  # Strip to remove any extra spaces or newlines
            if angle == 'exit':
                return 'exit'
            try:
                angle = eval(angle)
                return angle
            except Exception:
                return angle


def SendAnglesToFrontEnd(point_data):
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, '../../kneeSequence/register_points/angles.txt')
    # Ensure the directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Write data to the file
    with open(file_path, 'w') as f:
        f.write(str(point_data))  # Convert data to string and write


def getanglefromBackend(file='angles'):
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, f'../../kneeSequence/register_points/{file}.txt')
    if not os.path.exists(file_path):
        return -1

    with open(file_path, 'r') as f:
        angle = f.read().strip()  # Strip to remove any extra spaces or newlines
        try:
            angle = eval(angle)
            return angle
        except Exception as e:
            return -1


def SetangletoFrontEnd(file='angles', data=''):
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, f'../../kneeSequence/register_points/{file}.txt')
    # Ensure the directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Write data to the file
    with open(file_path, 'w') as f:
        f.write(str(data))  # Convert data to string and write


#
def play_notification_sound():
    current_dir = os.path.dirname(__file__)
    waveFilePath = os.path.join(current_dir, './notification_sounds/success.wav')
    wave_obj = sa.WaveObject.from_wave_file(waveFilePath)
    # wave_obj = sa.WaveObject.from_wave_file(
    #     '../../common/notification_sounds/success.wav')
    play_obj = wave_obj.play()
    play_obj.wait_done()


current_dir = os.path.dirname(__file__)
xml_path = os.path.join(current_dir, '../camera/Calibration_data/stereoCalibration.xml')

cv_file1 = cv2.FileStorage()
cv_file1.open(xml_path, cv2.FileStorage_READ)


def clockwiseangle_and_distance(point, center):
    # Vector between point and the origin: v = p - o
    vector = [point[0] - center[0], point[1] - center[1]]
    # Length of vector: ||v||
    lenvector = math.hypot(vector[0], vector[1])
    # If length is zero there is no angle
    if lenvector == 0:
        return -math.pi, 0
    # Normalize vector: v/||v||
    normalized = [vector[0] / lenvector, vector[1] / lenvector]
    dotprod = normalized[0] * refvec[0] + normalized[1] * refvec[1]  # x1*x2 + y1*y2
    diffprod = refvec[1] * normalized[0] - refvec[0] * normalized[1]  # x1*y2 - y1*x2
    angle = math.atan2(diffprod, dotprod)
    # Negative angles represent counter-clockwise angles so we need to subtract them
    # from 2*pi (360 degrees)
    if angle < 0:
        return 2 * math.pi + angle, lenvector
    # I return first the angle because that's the primary sorting criterium
    # but if two vectors have the same angle then the shorter distance should come first.
    return angle, lenvector


# previous_contour=None
# pixel_threshold=0.25  # Adjust threshold as needed
def is_significant_change(new_contour, previous_contour, threshold):
    if previous_contour is None:
        return True  # If no previous contour, consider it a significant change
    for new_point, prev_point in zip(new_contour, previous_contour):
        distance = ((new_point[0] - prev_point[0]) ** 2 + (new_point[1] - prev_point[1]) ** 2) ** 0.5
        if distance > threshold:
            return True
    return False


################################11052024
def apply_noise_reduction(image):
    # Apply denoising algorithm (e.g., Non-Local Means Denoising)
    denoised_image = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
    return denoised_image


# Function to check pixel accuracy
def check_pixel_accuracy(image):
    # Get image dimensions
    height, width, _ = image.shape

    # Calculate pixel accuracy
    pixel_accuracy = width / 1000  # Assume 1000 pixels correspond to 1 unit (e.g., 1 mm)

    return pixel_accuracy


# Function to apply temporal filtering
def apply_temporal_filtering(positions):
    # Apply temporal filtering techniques (e.g., moving average)
    filtered_positions = np.convolve(positions, np.ones(5) / 5, mode='valid')
    return filtered_positions


def calc_cos_phi(a, b, c):
    return c / math.sqrt(a * a + b * b + c * c)


def calc_sin_phi(a, b, c):
    return math.sqrt((a * a + b * b) / (a * a + b * b + c * c))


def calc_u1(a, b, c):
    return b / math.sqrt(a * a + b * b)


def calc_u2(a, b, c):
    return -a / math.sqrt(a * a + b * b)


def get_transform_matrix(plane):
    a, b, c = plane
    cos_phi = calc_cos_phi(a, b, c)
    sin_phi = calc_sin_phi(a, b, c)
    u1 = calc_u1(a, b, c)
    u2 = calc_u2(a, b, c)
    out = np.array([
        [cos_phi + u1 * u1 * (1 - cos_phi), u1 * u2 * (1 - cos_phi), u2 * sin_phi],
        [u1 * u2 * (1 - cos_phi), cos_phi + u2 * u2 * (1 - cos_phi), -u1 * sin_phi],
        [-u2 * sin_phi, u1 * sin_phi, cos_phi]
    ])
    return out


def transform_plane(plane):
    t = get_transform_matrix(plane)
    t_inv = np.linalg.inv(t)
    new_plane = np.dot(plane, t_inv)
    return new_plane, t_inv


def save_contours(contours, filename):
    with open(filename, 'w') as file:
        for contour in contours:
            for point in contour:
                file.write(str(point[0][0]) + ',' + str(point[0][1]) + '\n')


def load_contours(filename):
    contours = []
    with open(filename, 'r') as file:
        current_contour = []
        for line in file:
            x, y = map(int, line.split(','))
            current_contour.append([[x, y]])
        contours.append(np.array(current_contour))
    return contours


def compare_contours(previous_contours, current_contours, threshold=0.5):
    if len(previous_contours) != len(current_contours):
        print(f'test1 {len(previous_contours)} {len(previous_contours)}')
        return True

    for prev_contour, curr_contour in zip(previous_contours, current_contours):
        if cv2.contourArea(prev_contour) != cv2.contourArea(curr_contour):
            print('test2')
            return True

        for prev_point, curr_point in zip(prev_contour, curr_contour):
            if abs(prev_point[0][0] - curr_point[0][0]) > threshold or abs(
                    prev_point[0][1] - curr_point[0][1]) > threshold:
                print('test3')
                return True

    return False


#
#
def detect_objects_test(frame):
    # blurred = cv2.medianBlur(frame, 11)
    blurred = cv2.GaussianBlur(frame, (9, 9), 0)
    # blurred = cv2.GaussianBlur(frame, (11, 11), 0)
    # cv2.imwrite('frame_with_contours.png', blurred)
    max_intensity = np.max(blurred)
    _, thresh_image = cv2.threshold(blurred, max_intensity - 1, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    kernel = np.ones((1, 1
                      ), np.uint8)  # Define a kernel for erosion
    edges, org = ghosal_edge_v2(thresh_image, Ks=9, kmin=0, kmax=1000, lmax=0.5, phimin=1, thresholding=True,
                                debug=False, mirror=False)

    # Convert edges to contours
    contours = []
    for edge in edges:
        contour = np.array([edge])
        contours.append(contour)
    return contours


def calculate_angle(point, centroid):
    delta_x = point[0] - centroid[0]
    delta_y = point[1] - centroid[1]
    angle = np.arctan2(delta_y, delta_x)
    return angle


def assign_unique_ids(sorted_contours):
    ids = {}
    for idx, contour in enumerate(sorted_contours):
        point = tuple(contour[0][0])
        ids[idx] = point
    return ids


# def detect_objects_test(frame):
#     blurred = cv2.GaussianBlur(frame, (5, 5), 0)
#
#     _, thresh_image = cv2.threshold(blurred, 250, 255, cv2.THRESH_BINARY)
#
#     cv2.imwrite(f'resh_image.png', thresh_image)
#
#     # adaptive_thresh = cv2.adaptiveThreshold(blurred, 200, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 19, 2)
#
#     # Morphological operations
#     # kernel = np.ones((2, 2), np.uint8)
#     # morph = cv2.morphologyEx(thresh_image, cv2.MORPH_CLOSE, kernel)
#
#     # Retrieving outer-edge coordinates in the new threshold image
#     contours, hierarchy = cv2.findContours(thresh_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
#
#     # sure_bg = cv2.dilate(opened, kernel, iterations=3)
#     # dist_transform = cv2.distanceTransform(opened, cv2.DIST_L2, 5)
#     # ret, sure_fg = cv2.threshold(dist_transform, 0.7 * dist_transform.max(), 255, 0)
#
#     return contours


# def detect_objects_test(frame):
#     # Step 1: Apply Gaussian blur to reduce noise
#     blurred = cv2.GaussianBlur(frame, (5, 5), 0)
#
#     # Step 2: Apply Non-Local Means Denoising
#     denoised = cv2.fastNlMeansDenoisingColored(blurred, None, 10, 10, 7, 21)
#
#     # Step 2: Apply adaptive thresholding to create a binary image
#     _, thresh_image = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
#
#     # Step 3: Morphological operations to enhance object contours
#     kernel = np.ones((3, 3), np.uint8)
#     morph = cv2.morphologyEx(thresh_image, cv2.MORPH_CLOSE, kernel)
#
#     # Step 4: Find contours in the processed image
#     contours, hierarchy = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#
#     # Optionally, draw contours on the original frame (for visualization)
#     frame_with_contours = frame.copy()
#     cv2.drawContours(frame_with_contours, contours, -1, (0, 255, 0), 2)  # Draw all contours
#
#     # Save the frame with contours (optional for debugging)
#     cv2.imwrite('frame_with_contours.png', frame_with_contours)
#
#     return contours

# def detect_objects(frame):
#     contours = []
#     blurred = cv2.medianBlur(frame, 7)
#     blurred = cv2.GaussianBlur(blurred, (7, 7), 3)
#     # blurred = cv2.GaussianBlur(frame, (11, 11), 0)
#     # cv2.imwrite('frame_with_contours.png', blurred)
#     max_intensity = np.max(frame)
#     if max_intensity < 150:
#         return contours
#     _, thresh_image = cv2.threshold(blurred, max_intensity - 1, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
#     # kernel = np.ones((1, 1
#     #                   ), np.uint8)  # Define a kernel for erosion
#     # morph = cv2.erode(thresh_image, kernel, iterations=6)
#     contours, hierarchy = cv2.findContours(thresh_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#     return contours
#
#
#
#     res = np.zeros(blurred.shape[:2], dtype=np.uint8)
#     # find countours. use a more complex CHAIN_APPROX if SIMPLE is not enough
#     contours, hier = cv2.findContours(blurred, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#     # draw contour
#     for cnt in contours:
#         cv2.drawContours(res, [cnt], 0, (255,255,255), thickness=6)  # Ensure correct color format
#
#         # Invert input image to create mask
#     img_inverted = cv2.bitwise_not(blurred)
#
#     # Apply mask to get routing path
#     path = cv2.bitwise_and(res, res, mask=img_inverted)
#
#     # Join drawn contour and input image to get solid
#     solid = cv2.bitwise_or(blurred, res)
#
#     # kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
#     # opening = cv2.morphologyEx(solid, cv2.MORPH_OPEN, kernel, iterations=1)
#
#     # Find contours again on the solid image
#     contours, hier = cv2.findContours(solid, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#
#
#
#
#     # cv2.imshow('Input', frame)
#     # cv2.imshow('Path', path)
#     # cv2.imshow('Solid', solid)
#     # cv2.waitKey(0)
#     # cv2.destroyAllWindows()
#
#     return contours

def detect_objects10032025(gray):
    # Convert to grayscale
    # gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian blur (optional: use MedianBlur if noise is high)
    blurred = cv2.GaussianBlur(gray, (7, 7), 3)

    # Compute max intensity in grayscale
    max_intensity = np.max(gray)
    if max_intensity < 150:
        return []  # Return empty list if brightness is too low

    # Apply Otsu's Thresholding
    _, thresh_image = cv2.threshold(blurred, max_intensity - 1, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Apply morphological opening to remove small noise
    kernel = np.ones((3, 3), np.uint8)
    morph = cv2.morphologyEx(thresh_image, cv2.MORPH_OPEN, kernel, iterations=2)

    # Find contours
    contours, hierarchy = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    return contours


def ghosal_edge_v2(img, Ks, kmin=0, kmax=1000, lmax=0.5, phimin=1, thresholding=True, debug=False, mirror=False):
    """
    implementation of the subpixel edge detection method of [2]. The
    pixels are projected into a new orthogonal domain where a parameter
    k defines the intesity of the edge. By filtering out edges of small
    k the relevant ones remain. The extracted parameters are enough to
    define a straight edge.
    img: the image to be treated.
    Ks: kernel size
    thr: threshold paramter limiting the minimum of k
    kmax/min: threshold limiting k. This is usefull to define the edge
        intensity range that we are looking for. Neither too strong nor
        too weak
    lmax: is the maximum distance between the pixel center and the
        detected edge. This avoids that big kernels do errors, when
        close to multiple edges. It also avoids values of l which are
        nonesense, since they only make sense for real edges.
    phimin: allows the user to define a minimum angle in radians
        measured between the y-axis and the edge.
    thresholding: in case no thresholding is desired, might make sense
        for debugging or for post-processing raw data of edges
    debug: will output k, l and phi matrices
    mirror: mirror the limits of the image during convolution
        so that no aliasing happens during the convolution.
        Convolution time is doubled for some cases if this is activated.
    """
    # gather image properties before its altered
    ni, nj = np.shape(img)
    # Ks must be odd
    if Ks % 2 != 1:
        print("Ks must be odd! Continuing with Ks = Ks-1")
        Ks = Ks - 1
    # define the rectangular kernels
    # Vc00 = np.zeros((Ks,Ks),dtype=complex) # not needed
    Vc11 = np.zeros((Ks, Ks), dtype=complex)
    Vc20 = np.zeros((Ks, Ks), dtype=complex)
    ofs = 1 * (1 - 1 / Ks)  # offset for centering kernel around 0,0
    for i in range(Ks):
        for j in range(Ks):
            Kx = 2 * j / Ks - ofs  # limits of integration between -1 and 1
            Ky = 2 * i / Ks - ofs
            if Kx ** 2 + Ky ** 2 <= 1:  # only a circle
                # Vc00[i,j] = 1 # the conjugate of V00 # not needed
                Vc11[i, j] = Kx - Ky * 1j  # ...
                Vc20[i, j] = 2 * Kx ** 2 + 2 * Ky ** 2 - 1
    # mirror the edges to avoid edge effects from convolution
    if mirror:
        thick = int((Ks - 1) / 2)
        img = np.concatenate((img[:, (thick - 1)::-1], img, img[:, :-(thick + 1):-1]), 1)
        img = np.concatenate((img[(thick - 1)::-1, :], img, img[:-(thick + 1):-1, :]), 0)
        mode = "valid"
    else:
        mode = "same"

    # do the convolution with the images to get the zernike moments
    Anorm = lambda n: (n + 1) / np.pi  # a normalization value
    # A00 = scig.convolve2d(img,Vc00,mode='same') # not needed
    A11 = Anorm(1) * scig.oaconvolve(img, Vc11, mode=mode)
    A20 = Anorm(2) * scig.oaconvolve(img, Vc20, mode=mode)
    SMALL = 2.220446049250313e-16

    def zero_to_small(A):
        """
        take array A and values whose abs is smalleer than SMALL
        are converted to SMALL
        """

        A[(A < SMALL) & (A >= 0)] = SMALL
        A[(A > -SMALL) & (A < 0)] = -SMALL
        return A

    phi = np.arctan(np.imag(A11) / zero_to_small(np.real(A11)))
    Al11 = np.real(A11) * np.cos(phi) + np.imag(A11) * np.sin(phi)
    l = np.real(A20) / Al11  # A20 has no imaginary component so A20 = A'20
    l = np.minimum(l, 1 - SMALL)  # chop off those that go beyond the kernel boundaries
    l = np.maximum(l, -1 + SMALL)
    k = abs(3 * Al11 / (2 * (1 - l ** 2) ** (3 / 2)))

    if thresholding == True:
        # conditions
        phi_c = abs(phi) > phimin
        l_c = abs(l) < lmax
        k_c = (k < kmax) & (k > kmin)
        valid = phi_c & (k_c & l_c)
    elif not thresholding:
        valid = np.ones_like(k)
    # define a grid of pixel positions
    i, j = np.meshgrid(np.arange(nj), np.arange(ni))

    # get a list of the valid relevant parameters
    i = i[valid]
    j = j[valid]
    #	k = k[valid] # not necessary
    l = l[valid]
    phi = phi[valid]

    # convert to the subpixel position
    i_s = i + l * Ks / 2 * np.cos(phi)
    j_s = j + l * Ks / 2 * np.sin(phi)

    # put all detected points in a vector of (x,y) values
    edg = np.squeeze((j_s, i_s)).transpose()
    org = np.squeeze((j, i)).transpose()
    if debug == True:
        return edg, org, k, l, phi
    else:
        return edg, org


def moving_average(data, window_size):
    weights = np.repeat(1.0, window_size) / window_size
    return np.convolve(data, weights, 'valid')


def calculate_average_coordinates(data_sets):
    averages = []
    num_data_sets = len(data_sets)
    num_coordinates = len(data_sets[0])  # Assuming all data sets have the same number of coordinates
    # print(f'test {num_coordinates}')
    # Initialize sum of coordinates for each index (x, y), (x1, y1), (x2, y2), (x3, y3) based on num_data_sets
    sum_coordinates = [(0, 0) for _ in range(num_coordinates)]
    # print(f'sum_coordinates {sum_coordinates}')
    # Calculate sum of coordinates for each index
    for data_set in data_sets:
        for i, coordinates in enumerate(data_set):
            sum_coordinates[i] = (sum_coordinates[i][0] + coordinates[0],
                                  sum_coordinates[i][1] + coordinates[1])

    # Calculate averages for each index (x, y), (x1, y1), (x2, y2), (x3, y3)
    for coordinate_sum in sum_coordinates:
        average_x = coordinate_sum[0] / num_data_sets
        average_y = coordinate_sum[1] / num_data_sets
        averages.append((average_x, average_y))

    return averages


# def calculate_average_coordinates(data_sets):
#     averages = []
#     num_data_sets = len(data_sets)
#
#     # Initialize sum of coordinates for each index (x, y), (x1, y1), (x2, y2), (x3, y3)
#     sum_coordinates = [(0, 0), (0, 0), (0, 0), (0, 0)]
#
#     # Calculate sum of coordinates for each index
#     for data_set in data_sets:
#         for i, coordinates in enumerate(data_set):
#             sum_coordinates[i % 4] = (
#             sum_coordinates[i % 4][0] + coordinates[0], sum_coordinates[i % 4][1] + coordinates[1])
#
#     # Calculate averages for each index (x, y), (x1, y1), (x2, y2), (x3, y3)
#     for coordinate_sum in sum_coordinates:
#         average_x = coordinate_sum[0] / num_data_sets
#         average_y = coordinate_sum[1] / num_data_sets
#         averages.append((average_x, average_y))
#
#     return averages

# def CalculateSimpleMovingavg(detections):
#     print(f'detections {detections}')
#     window_size = 3
#     i = 0
#     # Initialize an empty list to store moving averages
#     moving_averages = []
#     # print('test')
#     while i < len(detections) - window_size + 1:
#         # Store elements from i to i+window_size
#         # in list to get the current window
#         window = detections[i: i + window_size]
#         # print(f'test1 {window} window_size {window_size}')
#         # Calculate the average of current window
#         # window_average = round(sum(window) / window_size, 2)
#         # window_average = round(sum(coord[0] for coord in window) / window_size, 2)
#         window_average = [round(sum(coord[0] for coord in window_item) / window_size, 2) for window_item in window]
#
#         # print('test2')
#         # Store the average of current
#         # window in moving average list
#         moving_averages.append(window_average)
#         # print('test3')
#         # Shift window to right by one position
#         i += 1
#     # print('test4')
#     # print(moving_averages)
#     return moving_averages

def detect_circles(image):
    blurred = cv2.GaussianBlur(image, (5, 5), 0)

    # Applying threshold
    threshold = cv2.threshold(blurred, 100, 255,
                              cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)[1]

    # Apply the Component analysis function
    analysis = cv2.connectedComponentsWithStats(threshold,
                                                4,
                                                cv2.CV_32S)
    (totalLabels, label_ids, values, centroid) = analysis

    output = np.zeros(threshold.shape, dtype="uint8")

    # Loop through each component
    for i in range(1, totalLabels):

        # Area of the component
        area = values[i, cv2.CC_STAT_AREA]

        if (area > 140) and (area < 400):
            componentMask = (label_ids == i).astype("uint8") * 255
            output = cv2.bitwise_or(output, componentMask)
    print(f'output {output}')
    return None


def calculate_hu_moments(contour):
    # Calculate moments of the contour
    moments = cv2.moments(contour)

    # Calculate Hu Moments from the moments
    hu_moments = cv2.HuMoments(moments)

    # Log transform Hu Moments to make them scale-invariant
    for i in range(0, 7):
        hu_moments[i] = -1 * cv2.contourArea(contour) * np.log(abs(hu_moments[i]))

    return hu_moments


def calculate_hu_moments(contour):
    # Calculate moments of the contour
    moments = cv2.moments(contour)

    # Calculate Hu Moments from the moments
    hu_moments = cv2.HuMoments(moments)

    # Log transform Hu Moments to make them scale-invariant
    for i in range(0, 7):
        if hu_moments[i] != 0:
            hu_moments[i] = -1 * np.sign(hu_moments[i]) * np.log(abs(hu_moments[i]))
        else:
            # Handle the case where the Hu Moment is zero
            hu_moments[i] = 0

    return hu_moments


def calculate_similarity(hu_moments1, hu_moments2):
    # Calculate Euclidean distance between two sets of Hu Moments
    distance = np.sqrt(np.sum((hu_moments1 - hu_moments2) ** 2))
    return distance


# def detect_objects(frame):
#     blur = cv2.GaussianBlur(frame, (5, 5), 0)
#     _, binary_image = cv2.threshold(blur, 127, 255, cv2.THRESH_BINARY)
#
#     # Find contours
#     contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#
#     # Calculate Hu Moments for each contour
#     # hu_moments_list = [calculate_hu_moments(contour) for contour in contours]
#
#     return contours


def detect_led_formations(image, min_area_threshold):
    # Convert image to grayscale
    _, binary = cv2.threshold(image, 150, 255, cv2.THRESH_BINARY)

    # Find contours
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter contours based on area or other criteria
    led_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area_threshold]

    # Extract LED coordinates from contours
    led_coordinates = []
    for contour in led_contours:
        M = cv2.moments(contour)
        cx = int(M['m10'] / M['m00'])
        cy = int(M['m01'] / M['m00'])
        led_coordinates.append((cx, cy))

    # Convert to numpy array for clustering
    led_coordinates = np.array(led_coordinates)

    # Perform K-means clustering to separate into two groups
    kmeans = KMeans(n_clusters=2)
    kmeans.fit(led_coordinates)
    labels = kmeans.labels_

    # Separate LED coordinates based on clustering labels
    object1_leds = []
    object2_leds = []
    for i, (x, y) in enumerate(led_coordinates):
        if labels[i] == 0:
            object1_leds.append((x, y))
        else:
            object2_leds.append((x, y))

    return object1_leds, object2_leds


# Step 4: Extract Coordinates
def extract_coordinates(contours):
    coordinates = []
    for contour in contours:
        # Calculate centroid of contour
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cX = int(M["m10"] / M["m00"])
            cY = int(M["m01"] / M["m00"])
            coordinates.append((cX, cY))

    return coordinates


def draw_led_coordinates(image, led_coordinates, color):
    for coord in led_coordinates:
        cv2.circle(image, coord, 5, color, -1)  # Draw a filled circle at each LED coordinate
    plt.imshow(image)  # Assuming the image is grayscale
    plt.axis('off')  # Turn off axis
    plt.show()


def detect_and_extract_objects(image):
    object_coordinates = []
    for _ in range(2):  # Assuming there are two objects
        # Detect LED formations
        object1_leds, object2_leds = detect_led_formations(image, min_area_threshold=2.0)

        draw_led_coordinates(image, object1_leds, object2_leds, (0, 255, 0))
        # draw_led_coordinates(image, object2_leds, (0, 255, 0))

        # cv2.drawContours(image, object2_leds, -1, (0, 0, 0), -1)  # Draw filled contours to mask them

    return object1_leds, object2_leds


# def detect_objects(frame):
#     object_coordinates = detect_and_extract_objects(frame)
#     for i, coordinates in enumerate(object_coordinates):
#         print("Object", i + 1, "Coordinates:", coordinates)


'''def detect_objects(frame):
    blur = cv2.GaussianBlur(frame, (3, 3), 0)

    # Thresholding
    _, thresh = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Morphological transformations
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
    close = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel, iterations=5)

    # Find contours
    cnts, _ = cv2.findContours(close, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Initialize a list to store bounding rects and centroids
    objects = []

    for c in cnts:
        # Get bounding rect for the contour
        x, y, w, h = cv2.boundingRect(c)
        frame = cv2.drawContours(frame, c, 0, (0, 255, 255), 20)
        frame = cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)

        # Find minimum area rectangle
        rec = cv2.minAreaRect(c)
        box = cv2.boxPoints(rec)
        box = np.int0(box)
        img = cv2.drawContours(frame, [box], 0, (0, 0, 255), 2)

        _, buffer = cv2.imencode('.png', img)
        image_png = buffer.tobytes()

        # Display the image


        # Find centroid of the contour
        M = cv2.moments(c)
        if M["m00"] != 0:
            cX = int(M["m10"] / M["m00"])  # x-coordinate of the centroid
            cY = int(M["m01"] / M["m00"])  # y-coordinate of the centroid
        else:
            cX, cY = 0, 0  # Handle the case where contour area (M["m00"]) is zero

        # Store the bounding rect and centroid
        objects.append((x, y, w, h, cX, cY))

    # Sort objects by the x-coordinate of the centroid
    objects = sorted(objects, key=lambda b: b[4])

    # Draw sorted bounding boxes and centroids on the original image
    for i, (x, y, w, h, cX, cY) in enumerate(objects):
        cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 0, 0), 20)
        cv2.circle(frame, (cX, cY), 1, (320, 159, 22), 1)
        cv2.putText(frame, f'Object {i + 1}: ({cX}, {cY})', (x, y - 15), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (100, 255, 100),
                    1)
        print(f'Object {i + 1}: ({cX}, {cY})')
    plt.imshow(frame)  # Assuming the image is grayscale
    plt.axis('off')  # Turn off axis
    plt.show() '''


def compare_dicts(left_dict, right_dict):
    for key in left_dict:
        if key not in right_dict:
            return None  # Return None if key is missing in right_dict
        left_item = left_dict[key]
        right_item = right_dict[key]
        if left_item['area'] != right_item['area'] or left_item['radius'] != right_item['radius']:
            return None  # Return None if area or radius is not equal
    return True  # Return True if all items are equal


def calculate_min_distance(centers):
    min_distance = float('inf')
    num_circles = len(centers)

    # Iterate through each pair of circles
    for (x1, y1), (x2, y2) in itertools.combinations(centers, 2):
        distance = np.sqrt((x2 - x1) ** 2 + ((y2 - y1) ** 2))
        if distance < min_distance:
            min_distance = distance

    return min_distance

# def detect_objects(image):
#     # gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
#     # _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
#     # Apply thresholding to obtain binary image
#     new_width = int(1936 / 4)
#     new_height = int(1464 / 4)
#     image = cv2.resize(image, (new_width, new_height))
#     _, binary = cv2.threshold(image, 200, 255, cv2.THRESH_BINARY)
#
#     # Find contours in the binary image
#     contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#     return contours
def detect_trackerfromconters(contours, numberoftrackers=3, RecTracker=False):
    # contours = [(arr1, arr2) for arr1, arr2 in contours]
    contour_coords = []
    for contour in contours:
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cX = int(M["m10"] / M["m00"])
            cY = int(M["m01"] / M["m00"])
            contour_coords.append([cX, cY])

    # Convert contour coordinates to numpy array
    contour_coords = np.array(contour_coords)

    # Cluster contours using KMeans
    num_clusters = numberoftrackers  # Number of clusters
    kmeans = KMeans(n_clusters=num_clusters, n_init=10)  # Explicitly setting n_init
    kmeans.fit(contour_coords)
    labels = kmeans.labels_

    # Identify objects based on clusters
    objects = {}
    for i in range(num_clusters):
        objects[i] = contour_coords[labels == i]
    sorted_objects = {k: v for k, v in sorted(objects.items(), key=lambda item: np.mean(item[1][:, 0]))}

    reference_point = np.array([0, 0])  # For example, origin

    def distance(point):
        return np.linalg.norm(point - reference_point)

    Femure_tracker_LED = ['D', 'C', 'A', 'B']
    Tibia_tracker_LED = ['B', 'A', 'C', 'D']
    rec_tracker_3LED = ['C', 'A', 'B']
    rec_tracker_4LED = ['C', 'A', 'B', 'D']
    ToolTip_tracker_LED = ['A', 'C', 'B']
    # ToolTip_tracket=['B', 'A', 'C', 'D']
    sorted_objects_distance = {}
    # Display the clustered objects
    for i, points in sorted_objects.items():

        # for i, (obj_x, obj_y) in points:
        distances = [distance(point) for point in points]
        combined = list(zip(distances, points))
        sorted_combined = sorted(combined, key=lambda x: x[0])
        sorted_points = [point for _, point in sorted_combined]
        # print(f"Object {i}: {points}")
        # print(f'sorted {sorted_points}')
        if i == 0:
            Femur = dict(zip(Femure_tracker_LED, sorted_points))
        if i == 2:
            if (RecTracker):
                if (len(sorted_points) == 3):
                    rec = dict(zip(rec_tracker_3LED, sorted_points))
                if (len(sorted_points) == 4):
                    rec = dict(zip(rec_tracker_4LED, sorted_points))
            else:
                if (len(sorted_points) == 3):
                    ToolTip = dict(zip(ToolTip_tracker_LED, sorted_points))

        if i == 1:
            Tibia = dict(zip(Tibia_tracker_LED, sorted_points))

    if (RecTracker):
        return Femur, rec, Tibia
    else:
        return Femur, ToolTip, Tibia


def round_to_nearest(value, precision):
    return np.round(value / precision) * precision


def find_contours(frame):
    # contours = detect_objects(frame)
    contours = detect_objects_test(frame)

    detections = []
    scaling_factor = 1
    # Adjust this factor as needed

    for contour in contours:
        area = cv2.contourArea(contour)
        print(f'area {area}')
        if area < 1:
            continue

        # Instead of minEnclosingCircle, use minMaxLoc to find the bounding rectangle
        x, y, width, height = cv2.boundingRect(contour)

        # Scale the bounding rectangle coordinates
        x_scaled = int(x * scaling_factor)
        y_scaled = int(y * scaling_factor)
        width_scaled = int(width * scaling_factor)
        height_scaled = int(height * scaling_factor)

        # Calculate scaled center and radius
        center_scaled = (x_scaled + width_scaled // 2, y_scaled + height_scaled // 2)
        radius_scaled = max(width_scaled, height_scaled) / 8  # Adjust as needed

        center_with_precision = (np.round(center_scaled[0], 8), np.round(center_scaled[1], 8))
        print(f'radius {radius_scaled}')
        if radius_scaled < 0.5:
            continue

        detections.append(center_with_precision)

    sorted_coordinates = sorted(detections, key=lambda coord: coord[0])
    # print(f'{len(sorted_coordinates)}')
    return sorted_coordinates


def convert_to_ndc(sorted_points, image_width, image_height):
    ndc_points = []
    for point in sorted_points:
        x = point[0]
        y = point[1]
        # Convert to NDC
        x_ndc = (x + (image_width / 2)) / image_width
        y_ndc = (y + (image_height / 2)) / image_height

        ndc_points.append((x_ndc, y_ndc))
    return ndc_points


def convert_to_raster(sorted_points, image_width, image_height):
    raster_points = []
    for point in sorted_points:
        x_ndc = point[0]
        y_ndc = point[1]
        # Convert to raster
        x_raster = x_ndc * image_width
        # y_raster= (1 - y_ndc) * image_height
        y_raster = y_ndc * image_height

        raster_points.append((x_raster, y_raster))
    return raster_points


# def find_contours_Kmeans(image):
#     image_height, image_width = image.shape[:2]
#     contours = detect_objects_test(image)
#     detections = []
#     scaling_factor = 1
#     # Adjust this factor as needed
#
#     for contour in contours:
#         area = cv2.contourArea(contour)
#         if area < 1:
#             continue
#
#         # Instead of minEnclosingCircle, use minMaxLoc to find the bounding rectangle
#         x, y, width, height = cv2.boundingRect(contour)
#
#         # Scale the bounding rectangle coordinates
#         x_scaled = int(x * scaling_factor)
#         y_scaled = int(y * scaling_factor)
#         width_scaled = int(width * scaling_factor)
#         height_scaled = int(height * scaling_factor)
#
#         # Calculate scaled center and radius
#         center_scaled = (x_scaled + width_scaled // 2, y_scaled + height_scaled // 2)
#         radius_scaled = max(width_scaled, height_scaled) / 8  # Adjust as needed
#
#         center_with_precision = (np.round(center_scaled[0], 8), np.round(center_scaled[1], 8))
#         # print(f'radius {radius}')
#         if radius_scaled < 0.5:
#             continue
#
#         detections.append(center_with_precision)
#     dbscan = DBSCAN(eps=150, min_samples=3)
#     labels = dbscan.fit_predict(detections)
#
#     # Initialize lists to store LED coordinates for each cluster
#     clusters = {}
#     for label in set(labels):
#         clusters[label] = []
#
#     # Assign LED coordinates to respective clusters
#     for i, (x, y) in enumerate(detections):
#         cluster_label = labels[i]
#         clusters[cluster_label].append((x, y))
#
#
#
#     cluster_centroids = {}
#
#     # Calculate centroids of clusters
#     for label, points in clusters.items():
#         # Calculate centroid of cluster
#         centroid_x = np.mean([point[0] for point in points])
#         centroid_y = np.mean([point[1] for point in points])
#         centroid = (centroid_x, centroid_y)
#         # Store centroid
#         cluster_centroids[label] = {
#             'centroid': centroid,
#             'centroid_x': centroid_x,  # Adding centroid x-coordinate for sorting
#         }
#
#     # Sort clusters based on centroid x-coordinate
#     sorted_clusters = sorted(cluster_centroids.items(), key=lambda item: item[1]['centroid_x'])
#
#
#
#     sorted_clusters = {}
#
#     for label, points in sorted_clusters.items():
#         # Build a KDTree for efficient nearest neighbor search
#         kdtree = cKDTree(points)
#         # print('test')
#         # Calculate the closest distance for each point
#         closest_distances = []
#         for i, point in enumerate(points):
#             distances, indices = kdtree.query(point, k=2)  # k=2 to exclude the point itself
#             closest_distances.append(distances[1])
#
#         closest_distances = np.array(closest_distances)
#
#         # Sort points based on closest distance to another point
#         sorted_indices = np.argsort(closest_distances)
#         sorted_points = [points[i] for i in sorted_indices]
#         # print(f'test1 {sorted_points}')
#         temp1 = []
#         for item1 in sorted_points:
#             temp1.append([item1[0], 1556 - item1[1]])
#         sorted_points=temp1
#         # Store sorted points
#         sorted_clusters[label] = sorted_points
#     # print(f' clusters {sorted_clusters}')
#     results = []
#     for label, sorted_points in sorted_clusters.items():
#         if label == -1:
#             return -1
#
#         # Create a dictionary for the current iteration
#         iteration_result = {
#             'label': label,
#             'sorted_points': sorted_points,
#         }
#
#         # Append the dictionary to the results list
#         results.append(iteration_result)
#     # print(f'results {results}')
#     return results
#
#
#
#     # cluster_centroids = {}
#     #
#     # # Calculate centroids of clusters
#     # for label, points in clusters.items():
#     #     # Calculate centroid of cluster
#     #     centroid_x = np.mean([point[0] for point in points])
#     #     centroid_y = np.mean([point[1] for point in points])
#     #     centroid = (centroid_x, centroid_y)
#     #
#     #     distances_from_centroid = [np.linalg.norm(np.array(point) - np.array(centroid)) for point in points]
#     #
#     #     # Sort points based on distance from centroid
#     #     sorted_indices = np.argsort(distances_from_centroid)
#     #     sorted_points = [points[i] for i in sorted_indices]
#     #     # sorted_points = convert_to_ndc(sorted_points, image_width, image_height)
#     #     # # print(f' ndc {sorted_points }')
#     #     # sorted_points = convert_to_raster(sorted_points, image_width, image_height)
#     #     # print(f' raster {sorted_points}')
#     #     temp1 = []
#     #     for item1 in sorted_points:
#     #         temp1.append([item1[0], 1556 - item1[1]])
#     #     sorted_points=temp1
#     #
#     #     sorted_distances = [distances_from_centroid[i] for i in sorted_indices]
#     #     # Store centroid
#     #     cluster_centroids[label] = {
#     #         'centroid': centroid,
#     #         'centroid_x': centroid_x,  # Adding centroid x-coordinate for sorting
#     #         'sorted_points': sorted_points,  # Adding centroid x-coordinate for sorting
#     #         'distances_from_centroid': sorted_distances
#     #     }
#     #
#     # # Sort clusters based on centroid x-coordinate
#     # sorted_clusters = sorted(cluster_centroids.items(), key=lambda item: item[1]['centroid_x'])
#     # results = []
#     #
#     # for label, data in sorted_clusters:
#     #     centroid = data['centroid']
#     #     centroid_x = data['centroid_x']
#     #     sorted_points = data['sorted_points']
#     #     distances_from_centroid = data['distances_from_centroid']
#     #
#     #     if label == -1:
#     #         return -1
#     #     # Create a dictionary for the current iteration
#     #     iteration_result = {
#     #         'label': label,
#     #         # 'centroid': centroid,
#     #         # 'centroid_x': centroid_x,
#     #         'sorted_points': sorted_points,
#     #         'distances_from_centroid': distances_from_centroid
#     #     }
#     #
#     #     # Append the dictionary to the results list
#     #     results.append(iteration_result)
#     # return results
#
#
#
#
# # def find_contours(frame,threshold=60):
# #     contours = detect_objects(frame,threshold)
# #     detections = []
# #     for contour in contours:
# #         area = cv2.contourArea(contour)
# #         if area < 0.5:
# #             continue
# #         # Instead of minEnclosingCircle, use minMaxLoc to find the bounding rectangle
# #         x, y, width, height = cv2.boundingRect(contour)
# #         center = (x + width // 2, y + height // 2)  # Approximate center as the center of the bounding rectangle
# #         radius = max(width, height) / 16  # Approximate radius as half of the maximum side of the bounding rectangle
# #
# #         center_with_precision = (np.round(center[0], 8), np.round(center[1], 8))
# #         # print(f'radius {radius}')
# #         if radius < 0.5:
# #             continue
# #         detections.append(center_with_precision)
# #
# #     sorted_coordinates = sorted(detections, key=lambda coord: coord[0])
# #     return sorted_coordinates
#
#
#     '''
#     led_coordinates = []
#     for contour in contours:
#         M = cv2.moments(contour)
#         cx = int(M['m10'] / M['m00'])
#         cy = int(M['m01'] / M['m00'])
#         led_coordinates.append((cx, cy))
#
#     # Convert to numpy array for clustering
#     led_coordinates = np.array(led_coordinates)
#     # Perform K-means clustering to separate into two groups
#     kmeans = KMeans(n_clusters=2)
#     kmeans.fit(led_coordinates)
#     labels = kmeans.labels_
#
#     # Separate LED coordinates based on clustering labels
#     object1_leds = []
#     object2_leds = []
#     # object3_leds = []
#
#     for i, (x, y) in enumerate(led_coordinates):
#         if labels[i] == 0:
#             object1_leds.append((x, y))
#         # if labels[i] == 1:
#         #     object2_leds.append((x, y))
#         else:
#             object2_leds.append((x, y))
#     detections = []
#     detections.extend(object1_leds)
#     detections.extend(object2_leds)
#     # detections.extend(object3_leds)
#     print(f'detections {detections}')
#     return detections '''




from scipy.spatial import KDTree


def sort_and_select(points):
    # Step 1: Sort points based on x-values and extract last three
    sorted_points = sorted(points, key=lambda detection: detection[0])[-3:]

    # Step 2: Find closest distances using KDTree
    kdtree = KDTree(sorted_points)
    closest_distances = []
    for i, point in enumerate(sorted_points):
        distances, indices = kdtree.query(point, k=2)  # k=2 to exclude the point itself
        closest_distances.append(distances[1])

    closest_distances = np.array(closest_distances)

    # Step 3: Sort points based on closest distance to another point
    sorted_indices = np.argsort(closest_distances)
    sorted_points = [sorted_points[i] for i in sorted_indices]

    return sorted_points
# def find_contours_Kmeans10032025(image):
#     contours = detect_objects(image)
#     h, w = image.shape[:2]
#     detections = []
#
#     for contour in contours:
#         area = cv2.contourArea(contour)
#         if area < 1:
#             continue
#         # print(f' contour {contour} Area{area}')
#         center, radius = cv2.minEnclosingCircle(contour)
#         # x, y = center[0], center[1]  # Convert to integer
#         #
#         # center = (x, y)
#         detections.append(center)
#     # print(f'detections {detections}')
#     detections = [[point[0], 1556 - point[1]] for point in detections]
#     # return detections, boudingcir
#     return detections

def detect_objects(gray):
    """ Detects object contours in a grayscale image using thresholding and morphological operations. """
    blurred = cv2.medianBlur(gray, 5)
    # Apply Gaussian blur to smooth the image
    blurred = cv2.GaussianBlur(blurred, (5, 5), 0)

    # Compute max intensity in grayscale
    max_intensity = np.max(gray)
    # min_intensity = np.min(gray)
    if max_intensity < 150:
        return []  # Return empty list if brightness is too low
    # print(f' min_intensity{min_intensity} max_intensity {max_intensity}')
    # Apply Otsu's Thresholding
    _, thresh_image = cv2.threshold(blurred, max_intensity - 10, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # # Morphological opening to remove small noise
    # kernel = np.ones((5, 5), np.uint8)
    # morph = cv2.morphologyEx(thresh_image, cv2.MORPH_OPEN, kernel, iterations=1)

    # Find contours
    contours, _ = cv2.findContours(thresh_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    return contours


def least_squares_circle_fit(x, y):
    """ Computes the least squares fitting for a circle using an algebraic method. """

    # Compute barycenter
    x_m, y_m = np.mean(x), np.mean(y)
    u, v = x - x_m, y - y_m  # Reduced coordinates

    # Compute matrix components
    Suv = np.sum(u * v)
    Suu = np.sum(u ** 2)
    Svv = np.sum(v ** 2)
    Suuv = np.sum(u ** 2 * v)
    Suvv = np.sum(u * v ** 2)
    Suuu = np.sum(u ** 3)
    Svvv = np.sum(v ** 3)

    # Solve linear system
    A = np.array([[Suu, Suv], [Suv, Svv]])
    B = np.array([(Suuu + Suvv) / 2.0, (Svvv + Suuv) / 2.0])
    uc, vc = np.linalg.solve(A, B)

    xc, yc = x_m + uc, y_m + vc  # Convert back to original coordinates

    # Compute radius
    Ri = np.sqrt((x - xc) ** 2 + (y - yc) ** 2)
    R = np.mean(Ri)

    return (xc, yc), R


def find_contours_Kmeans(image):
    """ Detects objects, applies convex hull, and fits circles using Hough Transform & minEnclosingCircle. """

    contours = detect_objects(image)  # Detect contours
    detections = []
    # result_hough = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)  # Convert grayscale to BGR for visualization
    # result_min_circle = result_hough.copy()

    for contour in contours:
        if cv2.contourArea(contour) < 1:
            continue  # Skip small contours

        # Compute convex hull
        # hull = cv2.convexHull(contour)

        hull = cv2.convexHull(contour)
        hull_pts = hull.squeeze().astype(np.float32)  # Shape: (N, 2)

        epsilon = 0.005 * cv2.arcLength(hull_pts, True)
        spline = cv2.approxPolyDP(hull_pts, epsilon, closed=True)
        spline_pts = spline.squeeze().astype(np.float32)  # Shape: (N, 2)
        # Refine hull vertices at subpixel level
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.01)
        # refined_hull = cv2.cornerSubPix(gray, hull_pts, (5, 5), (-1, -1), criteria)
        refined_hull = cv2.cornerSubPix(image, spline_pts, (5, 5), (-1, -1), criteria)
        # refined_hull_int = refined_hull.reshape(-1, 1, 2).astype(np.int32)
        # Compute Min Enclosing Circle
        center, radius = cv2.minEnclosingCircle(refined_hull)
        detections.append((center[0], 1556 - center[1]))

        # cx, cy, rr = int(center[0]), int(center[1]), int(radius)
        # cv2.circle(result_min_circle, (cx, cy), rr, (0, 255, 0), 1)  # Green circle for enclosing
    detections = sorted(
        detections, key=lambda x: x[0]
    )
    return detections
# def find_contours_Kmeans(image):
#     """ Detects object contours and applies least squares circle fitting. """
#
#     contours = detect_objects(image)  # Get contours from grayscale image
#     detections = []
#
#     for contour in contours:
#         area = cv2.contourArea(contour)
#         if area < 1:
#             continue  # Skip small or invalid contours
#
#         # Extract convex hull for better circle fitting
#         hull = cv2.convexHull(contour)
#         x = hull[:, :, 0].flatten()
#         y = hull[:, :, 1].flatten()
#
#         # if len(x) < 5:  # Ensure enough points for fitting
#         #     continue
#
#         # Fit a circle using least squares
#         center, radius = least_squares_circle_fit(x, y)
#
#         # Adjust coordinate system if needed (Y-flipping example)
#         detections.append((center[0], 1556 - center[1]))
#
#     return detections


def find_contours_Kmeanns(image):
    """ Detects objects, applies convex hull, and fits circles using Hough Transform & minEnclosingCircle. """

    contours = detect_objects(image)  # Detect contours
    detections = []
    # result_hough = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)  # Convert grayscale to BGR for visualization
    # result_min_circle = result_hough.copy()

    for contour in contours:
        if cv2.contourArea(contour) < 1:
            continue  # Skip small contours

        # Compute convex hull
        # hull = cv2.convexHull(contour)

        hull = cv2.convexHull(contour)
        hull_pts = hull.squeeze().astype(np.float32)  # Shape: (N, 2)

        epsilon = 0.005 * cv2.arcLength(hull_pts, True)
        spline = cv2.approxPolyDP(hull_pts, epsilon, closed=True)
        spline_pts = spline.squeeze().astype(np.float32)  # Shape: (N, 2)
        # Refine hull vertices at subpixel level
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.01)
        # refined_hull = cv2.cornerSubPix(gray, hull_pts, (5, 5), (-1, -1), criteria)
        refined_hull = cv2.cornerSubPix(image, spline_pts, (5, 5), (-1, -1), criteria)
        # refined_hull_int = refined_hull.reshape(-1, 1, 2).astype(np.int32)
        # Compute Min Enclosing Circle
        center, radius = cv2.minEnclosingCircle(refined_hull)
        detections.append((center[0], 1556 - center[1]))
        # cx, cy, rr = int(center[0]), int(center[1]), int(radius)
        # cv2.circle(result_min_circle, (cx, cy), rr, (0, 255, 0), 1)  # Green circle for enclosing

    return detections


def distance_2d(LED1, LED2):
    if len(LED1) == 3:
        x1, y1, z1 = LED1
    else:
        x1, y1 = LED1
    if len(LED2) == 3:
        x2, y2, z2 = LED2
    else:
        x2, y2 = LED2
    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

    return distance


def distance_2d_detection(LED1, LED2):
    x1, y1 = LED1
    x2, y2 = LED2
    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

    return distance


def distance_3d(LED1, LED2):
    x1, y1, z1 = LED1
    x2, y2, z2 = LED2

    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2 + (z2 - z1) ** 2)

    return distance


def distance_verficiation(variable_dict):
    for key, value in variable_dict.items():
        print(f'For {key} the 3D distance between LEDs is {distance_3d(value[0], value[1])}')


def sort_tracker_pointer(points):
    points.sort(key=lambda a: a[1])
    print(f'{points}')
    point1 = points[-1]
    point3 = points[1]
    return [point1, point3]


def filter_point_registration_data(data=None):
    z_values = np.array([arr[2] for arr in data])

    # Calculate IQR to find outliers
    Q1 = np.percentile(z_values, 35)
    Q3 = np.percentile(z_values, 65)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR

    # Remove outliers
    filtered_data = [arr for arr in data if lower_bound <= arr[2] <= upper_bound]
    # print(f' filtered_data {filtered_data}')
    # To find and group repeated data
    # Define a small epsilon value to consider values as repeated if they are very close
    epsilon = 1e-6

    # Group similar z values
    grouped_data = []
    for arr in filtered_data:
        if not any(np.isclose(arr[2], existing_arr[2], atol=epsilon) for existing_arr in grouped_data):
            grouped_data.append(arr)

    # Calculate the averages of x, y, and z from the grouped data
    if grouped_data:
        averages = np.mean(grouped_data, axis=0)
        average_list = averages.tolist()
    else:
        average_list = []
    return average_list


def filter_point_registration_data_xy(data=None):
    if data is None or len(data) == 0:
        return []

    # Convert to numpy array for easier manipulation
    data_array = np.array(data)

    # Extract x and y values
    x_values = data_array[:, 0]
    y_values = data_array[:, 1]

    # Calculate IQR for x and y values
    def calculate_iqr(values):
        Q1 = np.percentile(values, 25)
        Q3 = np.percentile(values, 75)
        IQR = Q3 - Q1
        return Q1 - 1.5 * IQR, Q3 + 1.5 * IQR

    x_lower_bound, x_upper_bound = calculate_iqr(x_values)
    y_lower_bound, y_upper_bound = calculate_iqr(y_values)

    # Create boolean masks for filtering
    x_mask = (x_values >= x_lower_bound) & (x_values <= x_upper_bound)
    y_mask = (y_values >= y_lower_bound) & (y_values <= y_upper_bound)

    # Combine masks to filter data
    combined_mask = x_mask & y_mask
    filtered_data_array = data_array[combined_mask]

    # Define a small epsilon value to consider values as repeated if they are very close
    epsilon = 1e-6

    # Group similar x and y values
    grouped_data = []
    for arr in filtered_data_array:
        if not any(
                np.isclose(arr[0], existing_arr[0], atol=epsilon) and np.isclose(arr[1], existing_arr[1], atol=epsilon)
                for existing_arr in grouped_data
        ):
            grouped_data.append(arr)

    # Calculate the averages of x and y from the grouped data
    if grouped_data:
        grouped_data_array = np.array(grouped_data)
        averages = np.mean(grouped_data_array, axis=0)
        average_list = averages.tolist()
    else:
        average_list = []

    return average_list


import pandas as pd


def filter_input_data(data_set):
    flat_data = [
        (item[0][0], item[0][1], item[0][2], item[1][0], item[1][1], item[1][2], item[2][0], item[2][1], item[2][2]) for
        item in data_set]

    # Create DataFrame with columns X1, Y1, Z1, X2, Y2, Z2, X3, Y3, Z3
    df = pd.DataFrame(flat_data, columns=['X1', 'Y1', 'Z1', 'X2', 'Y2', 'Z2', 'X3', 'Y3', 'Z3'])

    # Remove outliers based on each column using IQR method
    for column in ['X1', 'Y1', 'Z1', 'X2', 'Y2', 'Z2', 'X3', 'Y3', 'Z3']:
        Q1 = df[column].quantile(0.35)
        Q3 = df[column].quantile(0.65)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        # Remove rows with values outside the bounds
        df = df[(df[column] >= lower_bound) & (df[column] <= upper_bound)]
    # Calculate the mean of each individual column
    column_means = df.mean()

    # Return the final values as a dictionary
    # final_values = {column: mean for column, mean in column_means.items()}

    final_values = [
        np.array([column_means['X1'], column_means['Y1'], column_means['Z1']]),
        np.array([column_means['X2'], column_means['Y2'], column_means['Z2']]),
        np.array([column_means['X3'], column_means['Y3'], column_means['Z3']])
    ]

    return final_values


# def filter_point_registration_data(data_set):
#     # flat_data = [(item[0], item[1], item[2]) for item in data_set]
#     # Create DataFrame
#     # df = pd.DataFrame(flat_data, columns=['X1', 'Y1', 'Z1','X2', 'Y2', 'Z2'])
#
#     flat_data = [(item[0], item[1], item[2], item[3], item[4], item[5], item[6], item[7], item[8]) for item in data_set]
#
#     # Create DataFrame with columns X1, Y1, Z1, X2, Y2, Z2
#     df = pd.DataFrame(flat_data, columns=['X1', 'Y1', 'Z1', 'X2', 'Y2', 'Z2', 'X3', 'Y3', 'Z3'])
#
#     mode_values = df.mode().iloc[0]  # Take the first row (most common values)
#
#     def mode_to_original_format(mode_values):
#         original_data = []
#         for key, value in mode_values.items():
#             original_data.append(value)
#         return original_data
#
#     # Convert mode values to original format
#     original_data = mode_to_original_format(mode_values)
#     return original_data


def filter_plane_registration_list(data):
    # Step 1: Extract z-coordinates
    z_values = []
    for triplet in data:
        for coord in triplet:
            z_values.append(coord[2])

    z_values = np.array(z_values)

    # Step 2: Identify outliers using IQR
    Q1 = np.percentile(z_values, 35)
    Q3 = np.percentile(z_values, 65)
    IQR = Q3 - Q1

    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR

    non_outliers = [[coord for coord in triplet if lower_bound <= coord[2] <= upper_bound] for triplet in data]

    # Step 3: Remove empty triplets and calculate averages
    cleaned_data = [triplet for triplet in non_outliers if len(triplet) == 3]

    if not cleaned_data:
        return None

    # Step 4: Calculate averages for each coordinate position (1st, 2nd, 3rd in triplets)
    average_coords = [np.mean([triplet[i] for triplet in cleaned_data], axis=0) for i in range(3)]

    return average_coords


# def sort_triangle_pointer(points):
#     points.sort(key = lambda a: a[1])
#     point1 = points[-1]
#     point2 = points[-2]
#     point3 = points[0]
#     point4 = points[1]
#     return [point1, point3, point2, point4]
def sort_Rectracker_pointer(points):
    points.sort(key=lambda a: a[1])
    point1 = points[-1]
    point2 = points[-2]
    point3 = points[0]
    point4 = points[1]
    return [point1, point3, point2, point4]


def pixel_to_point(point_2d, depth):
    # Intrinsic camera parameters
    new_focal_length_x = 2611  # Replace with your actual focal length in pixels
    new_focal_length_y = 2615  # Replace with your actual focal length in pixels
    new_principal_point_x = 939  # Replace with your actual principal point in pixels (X-coordinate)
    new_principal_point_y = 778  # Replace with your actual principal point in pixels (Y-coordinate)

    # Camera intrinsic matrix
    K = np.array([[new_focal_length_x, 0, new_principal_point_x],
                  [0, new_focal_length_y, new_principal_point_y],
                  [0, 0, 1]])

    # Convert 2D coordinates with depth to 3D coordinates
    point_2d_homogeneous = np.append(point_2d, 1)
    point_3d = depth * np.dot(np.linalg.inv(K), point_2d_homogeneous)
    point_3d = np.array((point_3d[0] + 500, point_3d[1] + 500, point_3d[2]))

    return point_3d


def align_with_xy_plane(vector):
    # Normalize the vector
    norm = np.linalg.norm(vector)
    if norm == 0:
        return vector  # Handle zero-length vector
    normalized_vector = vector / norm

    # Project the vector onto the xy plane (z-component becomes 0)
    aligned_vector = normalized_vector * np.array([1, 1, 0])

    return aligned_vector


def stylus_point(A, B):
    # Calculate the vector from A to B
    V_AB = B - A

    # Calculate the normalized vector
    V_AB_normalized = V_AB / np.linalg.norm(V_AB)

    # Calculate the coordinates of point C
    C = B + (3 / 5) * V_AB_normalized
    # C = B + 124 * V_AB_normalized
    return C


def find_point_C(A, B):
    # Coordinates of points A and B
    x1, y1, z1 = A
    x2, y2, z2 = B
    # Calculate the coordinates of point C
    x_C = (2 * x2 + 3 * x1) / 5
    y_C = (2 * y2 + 3 * y1) / 5
    z_C = (2 * z2 + 3 * z1) / 5

    return (x_C, y_C, z_C)


def sort_and_split_femur_from_tibia(coordinates):
    # Sort coordinates based on x-values
    sorted_coordinates = sorted(coordinates, key=lambda coord: coord[0])

    femur_points = sorted_coordinates[:4]

    return femur_points


def sort_and_split_triangletracker_fromFemure_callibration(coordinates):
    # Sort coordinates based on y-values
    sorted_coordinates = sorted(coordinates, key=lambda coord: coord[1])

    tracker_points = sorted_coordinates[:3]
    femur_points = sorted_coordinates[3:]

    return femur_points, tracker_points


def sort_and_split_triangletracker_fromFemure(coordinates):
    # Sort coordinates based on x-values
    sorted_coordinates = sorted(coordinates, key=lambda coord: coord[0])

    tracker_points = sorted_coordinates[4:7]
    femur_points = sorted_coordinates[:4]

    return femur_points, tracker_points


def sort_and_extract_tibia(coordinates, flag, squaretracker=False):
    # Sort coordinates based on x-values
    sorted_coordinates = sorted(coordinates, key=lambda coord: coord[0])

    if flag:
        # Find the middle 4 points
        tibia_points = sorted_coordinates[len(sorted_coordinates) // 2 - 1: len(sorted_coordinates) // 2 + 3]

        # Find the last 3 points
        tooltip_points = sorted_coordinates[-3:]

    else:
        # Find the middle 3 points
        tooltip_points = sorted_coordinates[len(sorted_coordinates) // 2 - 1: len(sorted_coordinates) // 2 + 2]

        # Find the last 4 points
        tibia_points = sorted_coordinates[-4:]

    return tooltip_points, tibia_points


# def sort_and_extract_femur(coordinates, flag=False, squaretracker=False):
#     # Calculate the length of coordinates once
#     length = len(coordinates)
#
#     if squaretracker:
#         tooltip_points = coordinates[4:8]
#     else:
#         tooltip_points = coordinates[:3]
#
#     if flag:
#         if not squaretracker:
#             return tooltip_points, coordinates[length // 2 - 1:length // 2 + 2]
#         else:
#             return coordinates[4:7], coordinates[:4]
#
#     return tooltip_points, coordinates[:length // 2 + 3]

def calculate_RoboRectrackerJ1point(B, A):
    # distance between A and B is 65in mm
    distance_AB = distance_2d(A, B)
    scalingfactor = 65 / distance_AB
    tuple((B[0] - d1 * scalingfactor, B[1] + d2 * scalingfactor))
    return B, scalingfactor


def calculate_RoboRectrackerJ2point(B, A):
    B, scale = calculate_RoboRectrackerJ1point(B, A)
    # here r is roboarm_radius
    A = tuple((B[0], B[1] - r * scale))
    return A


def printAngle(A, B, C):
    # Square of lengths be a2, b2, c2
    a2 = distance_2d(B, C)
    b2 = distance_2d(A, C)
    c2 = distance_2d(A, B)

    # length of sides be a, b, c
    a = math.sqrt(a2)
    b = math.sqrt(b2)
    c = math.sqrt(c2)

    # From Cosine law
    alpha = math.acos((b2 + c2 - a2) /
                      (2 * b * c))
    betta = math.acos((a2 + c2 - b2) /
                      (2 * a * c))
    gamma = math.acos((a2 + b2 - c2) /
                      (2 * a * b))

    # Converting to degree
    alpha = alpha * 180 / math.pi
    betta = betta * 180 / math.pi
    gamma = gamma * 180 / math.pi
    return alpha, betta, gamma


# def sort_and_extract_femur(coordinates, flag=False, squaretracker=False):
#     # Sort coordinates based on x-values
#     sorted_coordinates = sorted(coordinates, key=lambda coord: coord[0])
#
#     if flag: #Right to the femur marking
#         if(squaretracker):
#             tooltip_points=sorted_coordinates[:4]
#             femur_points = sorted_coordinates[4:8]
#             return tooltip_points, femur_points
#         else:
#             tooltip_points = sorted_coordinates[len(sorted_coordinates)//2 - 1 : len(sorted_coordinates)//2 + 2]
#         # Find the first 4 points
#         femur_points = sorted_coordinates[:4]
#
#     else:
#         if (squaretracker):
#             tooltip_points = sorted_coordinates[4:8]
#             femur_points = sorted_coordinates[0:4]
#             return tooltip_points, femur_points
#         else:
#             tooltip_points = sorted_coordinates[:3]
#
#         # Find the middle 4 points
#         femur_points = sorted_coordinates[0 : len(sorted_coordinates)//2 + 3]
#
#     return tooltip_points, femur_points
def sort_and_extract_femur(coordinates, flag=False):
    # Sort coordinates based on x-values
    sorted_coordinates = sorted(coordinates, key=lambda coord: coord[0])

    if flag:  # Right to the femur marking
        # Find the middle 3 points
        tooltip_points = sorted_coordinates[len(sorted_coordinates) // 2 - 1: len(sorted_coordinates) // 2 + 2]

        # Find the first 4 points
        femur_points = sorted_coordinates[:4]

    else:
        # Find the first 3 points
        tooltip_points = sorted_coordinates[:3]

        # Find the middle 4 points
        femur_points = sorted_coordinates[len(sorted_coordinates) // 2 - 1: len(sorted_coordinates) // 2 + 3]

    return tooltip_points, femur_points


def sort_and_extract_rob(coordinates, flag=False):
    # Sort coordinates based on x-values
    square_tracker, femur_points = sort_and_extract_femur(coordinates, flag, True)
    sorted_coordinates = sorted(coordinates, key=lambda coord: coord[0])
    if (flag):
        tibia_points = sorted_coordinates[-4:]
    else:
        tibia_points = sorted_coordinates[8:12]
    return femur_points, square_tracker, tibia_points


def calculate_area(point1, point2, point3):
    # Calculate the area of the triangle formed by three points
    return 0.5 * abs(point1[0] * (point2[1] - point3[1]) +
                     point2[0] * (point3[1] - point1[1]) +
                     point3[0] * (point1[1] - point2[1]))


def find_global_point_tibia(points):
    collinear_points = []
    collinear_area = 10000000000000000000000

    # Iterate through all combinations of three points
    for i in range(len(points)):
        for j in range(i + 1, len(points)):
            for k in range(j + 1, len(points)):
                # Calculate the area of the triangle formed by the three points
                area = calculate_area(points[i], points[j], points[k])

                if (area < collinear_area):
                    collinear_points = [points[i], points[j], points[k]]
                    collinear_area = area

    global_point = [x for x in points if x not in collinear_points]

    collinear_points = sorted(collinear_points, key=lambda coord: coord[0])

    return global_point[0], collinear_points[1]


def find_center(coord1, coord2, coord3):
    # Midpoints of sides
    mid1 = ((coord1[0] + coord2[0]) / 2, (coord1[1] + coord2[1]) / 2)
    mid2 = ((coord2[0] + coord3[0]) / 2, (coord2[1] + coord3[1]) / 2)
    mid3 = ((coord3[0] + coord1[0]) / 2, (coord3[1] + coord1[1]) / 2)

    # Slopes of sides
    slope1 = (coord2[1] - coord1[1]) / (coord2[0] - coord1[0]) if coord2[0] != coord1[0] else None
    slope2 = (coord3[1] - coord2[1]) / (coord3[0] - coord2[0]) if coord3[0] != coord2[0] else None
    slope3 = (coord1[1] - coord3[1]) / (coord1[0] - coord3[0]) if coord1[0] != coord3[0] else None

    # If any slope is None (i.e., vertical line), set its reciprocal to 0
    if slope1 is None:
        slope1 = 0
    if slope2 is None:
        slope2 = 0
    if slope3 is None:
        slope3 = 0

    # Intercept of medians
    intercept1 = mid1[1] - slope1 * mid1[0] if slope1 != 0 else mid1[0]
    intercept2 = mid2[1] - slope2 * mid2[0] if slope2 != 0 else mid2[0]
    intercept3 = mid3[1] - slope3 * mid3[0] if slope3 != 0 else mid3[0]

    # Calculate intersection point of medians (centroid)
    center_x = (intercept2 - intercept1) / (slope1 - slope2) if slope1 != slope2 else mid1[0]
    center_y = slope1 * center_x + intercept1

    return center_x, center_y


def find_globalpoint_triangle(coord1, coord2, coord3):
    # print(f'coord1 {coord1} , coord2{coord2} coord3 {coord3}')
    points = [coord1, coord2, coord3]
    center = find_center(coord1, coord2, coord3)
    sorted_points = sorted(points, key=lambda p: clockwiseangle_and_distance(p, center))
    # print(f'sorted_points{sorted_points}')
    return center, sorted_points


def find_global_point_femur(coord1, coord2, coord3, coord4):
    # points = [coord1, coord2, coord3, coord4]
    # center = [sum(p[0] for p in points) / len(points), sum(p[1] for p in points) / len(points)]
    # # print(f'center {center}')
    # # Sort the points based on the clockwise angle and distance from the center
    # sorted_points = sorted(points, key=lambda p: clockwiseangle_and_distance(p, center))
    # # print(f'sorted_points{sorted_points}')
    # return center, sorted_points[0]

    # Calculate the average of x-coordinates
    x_center = (coord1[0] + coord2[0] + coord3[0]) / 3

    # Calculate the average of y-coordinates
    y_center = (coord1[1] + coord2[1] + coord3[1]) / 3

    # Check which point is closest to the calculated center
    points = [coord1, coord2, coord3, coord4]
    center_point = min(points, key=lambda p: (p[0] - x_center) ** 2 + (p[1] - y_center) ** 2)

    points.remove(center_point)

    points = sorted(points, key=lambda coord: coord[1])

    return center_point, points[0]


def find_global_point_square(points):
    collinear_points = []
    collinear_area = float('inf')

    # Iterate through all combinations of four points
    for i in range(len(points)):
        for j in range(i + 1, len(points)):
            for k in range(j + 1, len(points)):
                for l in range(k + 1, len(points)):
                    # Calculate the area of the square formed by the four points
                    area = calculate_area(points[i], points[j], points[k]) + calculate_area(points[j], points[k],
                                                                                            points[l])

                    if area < collinear_area:
                        collinear_points = [points[i], points[j], points[k], points[l]]
                        collinear_area = area

    global_point = [x for x in points if x not in collinear_points]

    collinear_points = sorted(collinear_points, key=lambda coord: coord[0])

    return global_point[0], collinear_points


def sort_points_clockwise(points):
    points = np.array(points)
    # Calculate the centroid of the points
    center = np.mean(points, axis=0)

    # Calculate the angle of each point from the centroid
    angles = np.arctan2(points[:, 1] - center[1], points[:, 0] - center[0])

    # Sort the points based on the angles
    sorted_points = points[np.argsort(angles)]

    return sorted_points


def rob_arm_femur_tibia_split(coordinates, flag=False):
    reference_point = (0, 0)  # Change this to your desired reference point
    sorted_coordinates = sorted(coordinates, key=lambda coord: ((coord[0] - reference_point[0]) ** 2 + (
            coord[1] - reference_point[1]) ** 2) ** 0.5)
    left_four = sorted_coordinates[:4]
    if (flag):
        middle_three = sorted_coordinates[4:7]
        right_four = sorted_coordinates[7:]
        return left_four, middle_three, right_four
    else:
        middle_four = sorted_coordinates[4:8]
        right_four = sorted_coordinates[8:]
        return left_four, middle_four, right_four


def point_registeration_tibia(detections_right, detections_left, fLAG, img_right, img_left):
    op_detections_right = []
    flag = fLAG

    for val in detections_right:
        op_detections_right.append([val[0], val[1]])
    tooltip_tracker_points_right, tibia_tracker_points_right = sort_and_extract_tibia(op_detections_right, flag)

    op_detections_left = []

    for val in detections_left:
        op_detections_left.append([val[0], val[1]])

    tooltip_tracker_points_left, tibia_tracker_points_left = sort_and_extract_tibia(op_detections_left, flag)

    pixel_coord_right = sort_tracker_pointer(tooltip_tracker_points_right)
    pixel_coord_left = sort_tracker_pointer(tooltip_tracker_points_left)

    tibia_point_left, tibia_col_points_left = find_global_point_tibia(tibia_tracker_points_left)
    tibia_point_right, tibia_col_points_right = find_global_point_tibia(tibia_tracker_points_right)

    depth_top = tri.find_depth(pixel_coord_right[0], pixel_coord_left[0], img_right, img_left, B, f, alpha)
    depth_bottom = tri.find_depth(pixel_coord_right[-1], pixel_coord_left[-1], img_right, img_left, B, f, alpha)

    depth_tibia = tri.find_depth(tibia_point_right, tibia_point_left, img_right, img_left, B, f, alpha)
    depth_tibia3 = tri.find_depth(tibia_col_points_right, tibia_col_points_left, img_right, img_left, B, f, alpha)

    # Convert to 3D coordinates using depth and camera matrix.
    A_point = depth_top
    B_point = depth_bottom
    tibia_point = depth_tibia
    tibia_point3 = depth_tibia3

    # A_point = pixel_to_point(pixel_coord_left[0], depth_top)
    # B_point = pixel_to_point(pixel_coord_left[-1], depth_bottom)

    # tibia_point = pixel_to_point(tibia_point_left, depth_tibia)
    # tibia_point3 = pixel_to_point(tibia_col_points_left, depth_tibia3)

    C_point = stylus_point(A_point, B_point)

    tibia_plane_points = [tibia_point, tibia_point3]

    return A_point, B_point, C_point, tibia_plane_points


def translate_point(point, translation_vector):
    # point and translation_vector should be lists or tuples with three elements (x, y, z)
    return (point[0] + translation_vector[0],
            point[1] + translation_vector[1],
            point[2] + translation_vector[2])


def transformation(old_global, new_global, old_tracker):
    # Calculate the translation vector
    translation_vector = (
        new_global[0] - old_global[0],
        new_global[1] - old_global[1],
        new_global[2] - old_global[2]
    )

    # Calculate the new position of the second point
    new_tracker = translate_point(old_tracker, translation_vector)

    return new_tracker


def centroid_of_triangle(coords):
    # Convert NumPy array to a list of tuples if necessary
    if isinstance(coords, np.ndarray):
        coords = coords.tolist()

    # Ensure coordinates are tuples
    coords_list = [tuple(coord) for coord in coords]

    x_coords = [x for x, y, z in coords_list]
    y_coords = [y for x, y, z in coords_list]
    z_coords = [z for x, y, z in coords_list]

    centroid_x = sum(x_coords) / 3
    centroid_y = sum(y_coords) / 3
    centroid_z = sum(z_coords) / 3

    globalpoint = [centroid_x, centroid_y, centroid_z]
    plane = [globalpoint] + coords_list[:3]  # Use slicing to take the first three points
    return plane


def calculate_centroid(vertices):
    """
    Calculate the centroid of a triangle given its vertices.

    Parameters:
    vertices (list): A list of three vertices, each a tuple or list of three coordinates (x, y, z).

    Returns:
    tuple: The centroid of the triangle as a tuple (x, y, z).
    """
    x1, y1, z1 = vertices[0]
    x2, y2, z2 = vertices[1]
    x3, y3, z3 = vertices[2]

    centroid = (
        (x1 + x2 + x3) / 3,
        (y1 + y2 + y3) / 3,
        (z1 + z2 + z3) / 3
    )

    return centroid


def femur_tibia_split(coordinates):
    # Sort the coordinates based on x values
    sorted_coordinates = sorted(coordinates, key=lambda coord: coord[0])

    # Extract left four, middle four, and right four coordinates
    left_four = sorted_coordinates[:4]
    right_four = sorted_coordinates[4:]

    return left_four, right_four


def femur_rob_tibia_split(coordinates):
    # Sort the coordinates based on x values
    sorted_coordinates = sorted(coordinates, key=lambda coord: coord[0])

    # Extract left four, middle four, and right four coordinates
    left_four = sorted_coordinates[:4]
    right_four = sorted_coordinates[4:]

    return left_four, right_four


def find_line_equation(x1, y1, x2, y2):
    # Calculate the slope (m)
    m = (y2 - y1) / (x2 - x1)

    # Calculate the y-intercept (b)
    b = y1 - m * x1

    # Return the line equation in the form y = mx + b
    return m, b


# def find_transformation_matrix(old_points, new_points):
#     old_points = np.array(old_points)
#     new_points = np.array(new_points)
#     centroid_old = np.mean(old_points, axis=0)
#     centroid_new = np.mean(new_points, axis=0)
#     H = np.dot((old_points - centroid_old).T, new_points - centroid_new)
#     # Calculate covariance matrix H
#     # H = np.zeros((3, 3))
#     # for i in range(3):
#     #     H += np.outer((old_points[i] - centroid_old), (new_points[i] - centroid_new))

#     U, S, Vt = np.linalg.svd(H, hermitian=True)
#     R = np.dot(Vt.T, U.T)
#     print('R', R)
#     if np.linalg.det(R) < 0:
#         Vt[2, :] *= -1
#         R = np.dot(Vt.T, U.T)

#     t = centroid_new - np.dot(R, centroid_old)

#     return R, t


def find_transformation_matrix(old_points, new_points):
    old_points = np.array(old_points)
    new_points = np.array(new_points)
    centroid_old = np.mean(old_points, axis=0)
    centroid_new = np.mean(new_points, axis=0)
    H = np.dot((old_points - centroid_old).T, new_points - centroid_new)
    # Calculate covariance matrix H
    # H = np.zeros((3, 3))
    # for i in range(3):
    #     H += np.outer((old_points[i] - centroid_old), (new_points[i] - centroid_new))

    U, S, Vt = np.linalg.svd(H)
    R = np.dot(Vt.T, U.T)

    if np.linalg.det(R) < 0:
        Vt[2, :] *= -1
        R = np.dot(Vt.T, U.T)

    t = centroid_new - np.dot(R, centroid_old)

    return R, t


# def find_new_point_location_pointer(old_plane_points, new_plane_points, old_marked_point):
#     # Example usage:
#     old_plane_points = np.array(old_plane_points)
#     new_plane_points = np.array(new_plane_points)
#     external_point = np.array(old_marked_point)
#
#     vector_to_external_point = external_point - old_plane_points[0]
#     # vector_to_external_point = external_point - old_plane_points[0]
#
#     # Find the transformation matrix and translation vector
#     R, _ = find_transformation_matrix(old_plane_points, new_plane_points)
#
#     # Transform the vector to the new location of the plane
#     transformed_vector_to_external_point = np.dot(R, vector_to_external_point)
#
#     # Calculate magnitude of vector
#     def magnitude(vector):
#         return np.linalg.norm(vector)
#
#     # Calculate magnitude of transformed vector
#     transformed_magnitude = magnitude(transformed_vector_to_external_point)
#
#     # Scale the transformed vector by the original magnitude
#     scaled_transformed_vector = transformed_vector_to_external_point * (
#             transformed_magnitude / magnitude(transformed_vector_to_external_point))
#
#     # new_plane_points = calculate_centroid(new_plane_points)
#     new_external_point = new_plane_points[0] + scaled_transformed_vector
#     # plane, t = CreatePlanefromPoints(new_plane_points)
#     # return np.dot(new_external_point,t)
#     return new_external_point


def find_new_point_location_pointer(old_plane_points, new_plane_points, old_marked_point):
    # Example usage:
    old_plane_points = np.array(old_plane_points)
    new_plane_points = np.array(new_plane_points)
    external_point = np.array(old_marked_point)

    # Calculate the centroids of the old and new planes
    old_centroid = np.mean(old_plane_points, axis=0)
    new_centroid = np.mean(new_plane_points, axis=0)

    # Calculate the vector from the old centroid to the external point
    vector_to_external_point = external_point - old_centroid

    # Find the transformation matrix and translation vector
    R, _ = find_transformation_matrix(old_plane_points, new_plane_points)

    # Transform the vector to the new location of the plane
    transformed_vector_to_external_point = np.dot(R, vector_to_external_point)

    # Calculate magnitude of vector
    def magnitude(vector):
        return np.linalg.norm(vector)

    # Calculate magnitude of transformed vector
    transformed_magnitude = magnitude(transformed_vector_to_external_point)

    # Scale the transformed vector by the original magnitude
    scaled_transformed_vector = transformed_vector_to_external_point * (
            transformed_magnitude / magnitude(transformed_vector_to_external_point))

    # Calculate the new external point location relative to the new centroid
    new_external_point = new_centroid + scaled_transformed_vector

    return new_external_point


def best_fit_transform(A, B):
    assert A.shape == B.shape
    m = A.shape[1]

    # Translate points to their centroids
    centroid_A = np.mean(A, axis=0)
    centroid_B = np.mean(B, axis=0)
    AA = A - centroid_A
    BB = B - centroid_B

    # Rotation matrix
    H = np.dot(AA.T, BB)
    U, S, Vt = np.linalg.svd(H)
    R = np.dot(Vt.T, U.T)

    if np.linalg.det(R) < 0:
        Vt[m - 1, :] *= -1
        R = np.dot(Vt.T, U.T)

    t = centroid_B.T - np.dot(R, centroid_A.T)

    T = np.identity(m + 1)
    T[:m, :m] = R
    T[:m, m] = t

    return T, R, t


def nearest_neighbor(src, dst):
    assert src.shape == dst.shape

    neigh = NearestNeighbors(n_neighbors=1)
    neigh.fit(dst)
    distances, indices = neigh.kneighbors(src, return_distance=True)
    return distances.ravel(), indices.ravel()


def icp(A, B, init_pose=None, max_iterations=20, tolerance=0.001):
    assert A.shape == B.shape
    m = A.shape[1]

    src = np.ones((m + 1, A.shape[0]))
    dst = np.ones((m + 1, B.shape[0]))
    src[:m, :] = np.copy(A.T)
    dst[:m, :] = np.copy(B.T)

    if init_pose is not None:
        src = np.dot(init_pose, src)

    prev_error = 0

    for i in range(max_iterations):
        distances, indices = nearest_neighbor(src[:m, :].T, dst[:m, :].T)

        T, _, _ = best_fit_transform(src[:m, :].T, dst[:m, indices].T)
        src = np.dot(T, src)

        mean_error = np.mean(distances)
        if np.abs(prev_error - mean_error) < tolerance:
            break
        prev_error = mean_error

    T, _, _ = best_fit_transform(A, src[:m, :].T)
    return T, distances, i


# def find_new_point_location(old_plane_points, new_plane_points, old_marked_point):
#     old_plane_points = np.array(old_plane_points)
#     new_plane_points = np.array(new_plane_points)
#     external_point = np.array(old_marked_point)

#     if old_plane_points.shape[1] != 3 or new_plane_points.shape[1] != 3 or len(external_point) != 3:
#         raise ValueError("All input points must be 3-dimensional")

#     vector_to_external_point = external_point - old_plane_points[0]

#     # Find the transformation matrix and translation vector using ICP
#     T, _, _ = icp(old_plane_points, new_plane_points)

#     # Ensure T is a 4x4 matrix
#     if T.shape != (4, 4):
#         raise ValueError("Transformation matrix T must be a 4x4 matrix")

#     # Extract rotation matrix and translation vector from T
#     R = T[:3, :3]
#     t = T[:3, 3]

#     # Convert vector to homogeneous coordinates for transformation
#     vector_to_external_point_homogeneous = np.append(vector_to_external_point, 1)

#     # Transform the vector to the new location of the plane
#     transformed_vector_to_external_point = np.dot(T, vector_to_external_point_homogeneous)[:3]

#     # Calculate the new external point location
#     new_external_point = new_plane_points[0] + transformed_vector_to_external_point
#     return new_external_point

def find_new_point_location(old_plane_points, new_plane_points, old_marked_point):
    # Example usage:
    old_plane_points = np.array(old_plane_points)
    new_plane_points = np.array(new_plane_points)
    external_point = np.array(old_marked_point)
    # Find the vector from a point on the original plane to the external point
    vector_to_external_point = external_point - old_plane_points[0]

    # Find the transformation matrix and translation vector
    R, t = find_transformation_matrix(old_plane_points, new_plane_points)

    # Transform the vector to the new location of the plane
    transformed_vector_to_external_point = np.dot(R, vector_to_external_point)

    # Calculate magnitude of vector
    def magnitude(vector):
        return np.linalg.norm(vector)

    # Calculate magnitude of transformed vector
    transformed_magnitude = magnitude(transformed_vector_to_external_point)

    # Scale the transformed vector by the original magnitude
    scaled_transformed_vector = transformed_vector_to_external_point * (
            transformed_magnitude / magnitude(transformed_vector_to_external_point))

    # Add the scaled transformed vector to the corresponding point on the new plane
    new_external_point = new_plane_points[0] + scaled_transformed_vector
    # plane, t = CreatePlanefromPoints(new_plane_points)
    # return np.dot(new_external_point,t)
    return new_external_point

def calculate_angles(AIN, TTIN, AAN, TTAN):
    # Calculate dot products
    dot_product_AIN_TTIN = np.dot(AIN, TTIN)
    dot_product_AAN_TTAN = np.dot(AAN, TTAN)

    # Calculate magnitudes
    magnitude_AIN_dot_TTIN = np.linalg.norm(AIN) * np.linalg.norm(TTIN)
    magnitude_AAN_dot_TTAN = np.linalg.norm(AAN) * np.linalg.norm(TTAN)

    # Calculate angles (in radians)
    inclination_angle_rad = np.arccos(dot_product_AIN_TTIN / magnitude_AIN_dot_TTIN)
    anteversion_angle_rad = np.arccos(dot_product_AAN_TTAN / magnitude_AAN_dot_TTAN)

    # Convert angles to degrees
    inclination_angle_deg = inclination_angle_rad * 180 / np.pi
    anteversion_angle_deg = anteversion_angle_rad * 180 / np.pi
    # print(inclination_angle_deg)
    # print(anteversion_angle_deg)
    return inclination_angle_deg, anteversion_angle_deg


def transformation_logic_tibia(global_dict_tibia, new_tibia, variable_dict):
    # take the dict, open the variables, transform all and save back in dict and return the dict
    old_tibia = global_dict_tibia['tibia_point']

    for key, value in variable_dict.items():
        new_points = []
        for point in value:
            new_points.append(transformation(old_tibia, new_tibia, point))
        variable_dict[key] = new_points

    return variable_dict


def CreatePlanefromPoints(planepoints):
    point1, point2, point3 = np.array(planepoints)
    # Compute vectors
    v1 = point2 - point1
    v2 = point3 - point1

    # Compute normal vector (cross product)
    normal = np.cross(v1, v2)
    # print(f 'normal{normal}')
    # # Compute D
    # D = np.dot(normal, point1)
    # normal, t = transform_plane(normal)
    return normal


def CreatePlanefromPointsTriTracker(point1, point2, point3):
    # Compute vectors
    v1 = point2 - point1
    v2 = point3 - point1

    # Compute normal vector (cross product)
    normal = np.cross(v1, v2)

    # # Compute D
    # D = np.dot(normal, point1)
    # normal, t = transform_plane(normal)
    return normal


def CreatePlanefromPointsTracker(point1, point2, point3, point4):
    # Compute vectors
    v1 = point1 - point2
    v2 = point3 - point4

    # Compute normal vector (cross product)
    normal = np.cross(v1, v2)

    # # Compute D
    # D = np.dot(normal, point1)
    # normal, t = transform_plane(normal)
    return normal


def calculate_tool_tip(L2, L3, L4, distance=12):
    # Convert points to numpy arrays
    L2 = np.array(L2)
    L3 = np.array(L3)
    L4 = np.array(L4)

    # Calculate vectors in the plane of the triangle
    v1 = L3 - L2
    v2 = L4 - L2

    # Calculate the normal vector of the plane
    n = np.cross(v1, v2)
    n = n / np.linalg.norm(n)

    # Calculate the direction vector of the shaft
    d = L4 - L2
    d = d - np.dot(d, n) * n
    d = d / np.linalg.norm(d)

    # Calculate the tool tip coordinates
    tool_tip = L2 + distance * d

    return tool_tip


def align_with_xy_plane(A, B):
    A = np.array(A)
    B = np.array(B)

    # Translate A to the origin
    translation_to_origin = -A
    B_translated = B + translation_to_origin

    # Calculate rotation to align B_translated with x-axis
    d = np.linalg.norm(B_translated)
    cos_theta = B_translated[0] / d
    sin_theta = B_translated[1] / d

    rotation_matrix = np.array([
        [cos_theta, -sin_theta, 0],
        [sin_theta, cos_theta, 0],
        [0, 0, 1]
    ])

    B_rotated = rotation_matrix @ B_translated
    return translation_to_origin, rotation_matrix, B_rotated


def calculate_point_C_in_aligned_system(d_AC, d_BC, B_rotated):
    Bx = B_rotated[0]

    # Using the circle intersection method to find C
    x = (d_AC * 2 - d_BC + Bx * 2) / (2 * Bx)
    y_squared = d_AC * 2 - x * 2

    if y_squared < 0:
        raise ValueError("No solution found; the given distances are inconsistent.")

    y = np.sqrt(y_squared)

    C1 = np.array([x, y, 0])
    C2 = np.array([x, -y, 0])

    return C1, C2


def transform_back(C, translation_to_origin, rotation_matrix):
    rotation_matrix_inverse = np.linalg.inv(rotation_matrix)
    C_transformed = rotation_matrix_inverse @ C - translation_to_origin
    return C_transformed


def find_third_point(A, B, d_AC, d_BC):
    translation_to_origin, rotation_matrix, B_rotated = align_with_xy_plane(A, B)
    C1_aligned, C2_aligned = calculate_point_C_in_aligned_system(d_AC, d_BC, B_rotated)

    C1 = transform_back(C1_aligned, translation_to_origin, rotation_matrix)
    C2 = transform_back(C2_aligned, translation_to_origin, rotation_matrix)

    return C1, C2


# ANTERVERSION_angle, INCLINATION_angle = utils.calculate_ANTERVERSION_INCLINATION_angles(
#                             RoboTrackernormalized_y, accetabulumnAxisXnorm,
#                             accetabulumnAxis_z_norm)
def calculate_ANTERVERSION_INCLINATION_angles(y_LCSr, x_ACS, z_ACS):
    # Calculate ANTERVERSION angle
    y_LCSr_proj_yz = y_LCSr - np.dot(y_LCSr, x_ACS) * x_ACS
    cos_ANTERVERSION = np.dot(y_LCSr_proj_yz, z_ACS) / (np.linalg.norm(y_LCSr_proj_yz) * np.linalg.norm(z_ACS))
    ANTERVERSION_angle = 90 - np.degrees(np.arccos(np.clip(cos_ANTERVERSION, -1.0, 1.0)))

    # Calculate INCLINATION angle
    y_LCSr_proj_xy = y_LCSr - np.dot(y_LCSr, z_ACS) * z_ACS
    cos_INCLINATION = np.dot(y_LCSr_proj_xy, x_ACS) / (np.linalg.norm(y_LCSr_proj_xy) * np.linalg.norm(x_ACS))
    INCLINATION_angle = 90 - np.degrees(np.arccos(np.clip(cos_INCLINATION, -1.0, 1.0)))

    return ANTERVERSION_angle, INCLINATION_angle


def create_local_coordinate_system_tibia(TC, ANC, MC, LC):
    """
    Create a local coordinate system using TC, ANC, MC, LC as input points,
    and translate the coordinate system such that the origin is at FC.

    Args:
        FC (np.ndarray): The FC point (new origin).
        TC (np.ndarray): The TC point.
        ANC (np.ndarray): The ANC point.
        MC (np.ndarray): The MC point.
        LC (np.ndarray): The LC point.

    Returns:
        tuple: (origin, x_axis, y_axis, z_axis) - The translated origin and local coordinate axes.
    """
    y_axis1 = ANC - TC
    # y_axis = y_axis1 / np.linalg.norm(y_axis1)

    # z-axis is the vector from MC to LC
    z_axis1 = MC - LC
    # z_axis = z_axis1 / np.linalg.norm(z_axis1)

    # y-axis is the cross product of x-axis and z-axis
    x_axis1 = np.cross(y_axis1, z_axis1)
    # x_axis = x_axis1 / np.linalg.norm(x_axis1)

    return x_axis1, y_axis1, z_axis1


def create_local_coordinate_systemFemure(HC, FC, ME, LE):
    """
    Calculate the normalized direction vectors for X, Y, and Z axes.

    Args:
        HC (np.ndarray): The coordinates of the HC point.
        FC (np.ndarray): The coordinates of the FC point.
        ME (np.ndarray): The coordinates of the ME point.
        LE (np.ndarray): The coordinates of the LE point.

    Returns:
        tuple: (femure_X, femure_Y, femure_Z) - The normalized X, Y, and Z axes.
    """
    # Calculate the direction vectors
    Y = HC - FC  # X-Axis direction (vector from FC to HC)
    z = ME - LE  # Y-Axis direction (vector from LE to ME)

    # Normalize the X and Y axes
    # femure_Y = Y / np.linalg.norm(Y)
    # femure_Z = z / np.linalg.norm(z)

    # Compute the Z-Axis as the cross product of X and Y
    x = np.cross(Y, z)

    # Normalize the Z-Axis
    # femure_X = x / np.linalg.norm(x)

    return x, Y, z


def calculate_transformation_matrix(tibia_X, tibia_Y, tibia_Z, femure_X, femure_Y, femure_Z, TC, FC):
    """
    Calculate the transformation matrix between two local coordinate systems (femur and tibia).

    Args:
        tibia_X, tibia_Y, tibia_Z (np.ndarray): The axes of the tibia local coordinate system.
        femure_X, femure_Y, femure_Z (np.ndarray): The axes of the femur local coordinate system.
        TC (np.ndarray): The origin of the tibia coordinate system.
        FC (np.ndarray): The origin of the femur coordinate system.

    Returns:
        tuple: (R, T) - The rotation matrix and translation vector between the two coordinate systems.
    """
    # Step 1: Compute the rotation matrix
    tibia_axes = np.column_stack([tibia_X, tibia_Y, tibia_Z]).T  # Stack the tibia axes in a matrix
    femur_axes = np.column_stack([femure_X, femure_Y, femure_Z]).T  # Stack the femur axes in a matrix

    # Calculate the rotation matrix R = femur_axes * inverse(tibia_axes)
    R = np.dot(femur_axes, np.linalg.inv(tibia_axes))

    # Step 2: Calculate the translation vector
    T = FC - TC  # Translation vector is the difference in the origins

    transformation_matrix = np.eye(4)
    transformation_matrix[:3, :3] = R  # Set the top-left 3x3 block to the rotation matrix
    transformation_matrix[:3, 3] = T  # Set the top-right 3x1 block to the translation vector

    return transformation_matrix[:3, :3]


def calculate_transformation_matrix_svd(tibia_X, tibia_Y, tibia_Z, femure_X, femure_Y, femure_Z, TC, FC):
    """
    Calculate the transformation matrix between two local coordinate systems (femur and tibia) using SVD with scaling.

    Args:
        tibia_X, tibia_Y, tibia_Z (np.ndarray): The axes of the tibia local coordinate system.
        femure_X, femure_Y, femure_Z (np.ndarray): The axes of the femur local coordinate system.
        TC (np.ndarray): The origin of the tibia coordinate system.
        FC (np.ndarray): The origin of the femur coordinate system.

    Returns:
        np.ndarray: The transformation matrix including scaling, rotation, and translation.
    """
    # Combine the tibia and femur coordinate axes into 3x3 matrices
    tibia_axes = np.column_stack([tibia_X, tibia_Y, tibia_Z])
    femur_axes = np.column_stack([femure_X, femure_Y, femure_Z])

    # Step 1: Center the axes (remove the origins)
    tibia_centered = tibia_axes - TC.reshape(3, 1)
    femur_centered = femur_axes - FC.reshape(3, 1)

    # Step 2: Calculate scaling factor
    norm_tibia = np.linalg.norm(tibia_centered, axis=0)
    norm_femur = np.linalg.norm(femur_centered, axis=0)
    scaling_factor = np.mean(norm_femur / norm_tibia)

    # Apply scaling to tibia coordinate system
    tibia_scaled = tibia_centered * scaling_factor

    # Step 3: Compute the rotation matrix using SVD
    H = np.dot(tibia_scaled, femur_centered.T)
    U, _, Vt = np.linalg.svd(H)
    R = np.dot(Vt.T, U.T)

    # Ensure a proper rotation (det(R) should be 1)
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)

    # Step 4: Compute the translation vector
    T = FC - np.dot(R, TC) * scaling_factor

    # Step 5: Construct the 4x4 transformation matrix
    transformation_matrix = np.eye(4)
    transformation_matrix[:3, :3] = R * scaling_factor  # Apply scaling to the rotation
    transformation_matrix[:3, 3] = T

    return transformation_matrix


def transformTibia_axes_to_femur(tibia_X, tibia_Y, tibia_Z, transformation_matrix):
    """
    Transform the tibia axes to the femur coordinate system using the transformation matrix.

    Args:
        tibia_X, tibia_Y, tibia_Z (np.ndarray): The axes of the tibia local coordinate system.
        transformation_matrix (np.ndarray): The 4x4 transformation matrix.

    Returns:
        tuple: Transformed tibia axes (X, Y, Z) in the femur coordinate system.
    """
    # Stack tibia axes as 3x3 matrix
    tibia_axes = np.column_stack([tibia_X, tibia_Y, tibia_Z])

    # Convert to homogeneous coordinates (4xN)
    tibia_axes_homogeneous = np.vstack((tibia_axes, np.ones((1, tibia_axes.shape[1]))))

    # Apply the transformation matrix
    transformed_axes_homogeneous = np.dot(transformation_matrix, tibia_axes_homogeneous)

    # Convert back to 3D coordinates (remove the homogeneous row)
    transformed_axes = transformed_axes_homogeneous[:3, :]

    # Split into individual transformed axes
    tranformed_tibia_X = transformed_axes[:, 0]
    tranformed_tibia_Y = transformed_axes[:, 1]
    tranformed_tibia_Z = transformed_axes[:, 2]

    return tranformed_tibia_X, tranformed_tibia_Y, tranformed_tibia_Z


def compute_transformation_matrix(origin1, origin2, axes1, axes2):
    """
    Compute the transformation matrix to transform from Coordinate 1 to Coordinate 2.

    Parameters:
    origin1 (tuple): The origin of Coordinate 1 (x1, y1, z1).
    origin2 (tuple): The origin of Coordinate 2 (x2, y2, z2).
    axes1 (tuple): The unit vectors for the axes of Coordinate 1, (x1_axis, y1_axis, z1_axis).
    axes2 (tuple): The unit vectors for the axes of Coordinate 2, (x2_axis, y2_axis, z2_axis).

    Returns:
    np.ndarray: The 4x4 transformation matrix.
    """

    # Translation vector (from Coordinate 1 to Coordinate 2)
    translation = np.array(origin2) - np.array(origin1)

    # Rotation matrix (axes of Coordinate 2 in terms of Coordinate 1)
    rotation = np.column_stack([axes2[0], axes2[1], axes2[2]])

    # Transformation matrix
    transformation_matrix = np.eye(4)
    transformation_matrix[:3, :3] = rotation
    transformation_matrix[:3, 3] = translation

    return transformation_matrix[:3, :3]


def midpoint(p1, p2):
    return (np.array(p1) + np.array(p2)) / 2


# Function to compute the axes and origin based on 3 points
def compute_axes_and_origin_tibia(T_Points):
    # Check if the input has exactly 3 points
    if len(T_Points) != 3:
        raise ValueError("T_Points must have exactly 3 points")

    # Step 1: Calculate the midpoint of T_Points[0] and T_Points[1]
    mid_point = midpoint(T_Points[0], T_Points[1])

    # Step 2: Define the Y-axis as the vector from T_Points[2] to the midpoint
    Y_axis = mid_point - np.array(T_Points[2])

    # Step 3: Define the X-axis as the vector from T_Points[0] to T_Points[1]
    X_axis = np.array(T_Points[1]) - np.array(T_Points[0])

    # Step 4: Normalize the X and Y axes
    X_axis = X_axis / np.linalg.norm(X_axis)
    Y_axis = Y_axis / np.linalg.norm(Y_axis)

    # Step 5: Calculate the Z-axis as the cross product of X and Y axes
    Z_axis = np.cross(X_axis, Y_axis)

    # Step 6: Normalize the Z-axis
    Z_axis = Z_axis / np.linalg.norm(Z_axis)

    # The origin is T_Points[2]
    origin = T_Points[2]

    return X_axis, Y_axis, Z_axis, origin


# Function to compute the axes and origin based on 3 points
def compute_axes_and_origin_Femur(F_Points):
    # Check if the input has exactly 3 points
    if len(F_Points) != 3:
        raise ValueError("T_Points must have exactly 3 points")

    # Step 1: Calculate the midpoint of T_Points[0] and T_Points[1]
    mid_point = midpoint(F_Points[0], F_Points[1])

    # Step 2: Define the Y-axis as the vector from T_Points[2] to the midpoint
    Y_axis = np.array(F_Points[2]) - mid_point

    # Step 3: Define the X-axis as the vector from T_Points[0] to T_Points[1]
    X_axis = np.array(F_Points[1]) - np.array(F_Points[0])

    # Step 4: Normalize the X and Y axes
    X_axis = X_axis / np.linalg.norm(X_axis)
    Y_axis = Y_axis / np.linalg.norm(Y_axis)

    # Step 5: Calculate the Z-axis as the cross product of X and Y axes
    Z_axis = np.cross(X_axis, Y_axis)

    # Step 6: Normalize the Z-axis
    Z_axis = Z_axis / np.linalg.norm(Z_axis)

    # The origin is T_Points[2]
    origin = F_Points[2]

    return X_axis, Y_axis, Z_axis, origin


def transform_TCS_to_FemurMarker(TCS, T_TCS_to_TibiaMarker, T_TibiaMarker_to_FemurMarker):
    # Step 1: Transform from TCS to Tibia Marker Coordinate System
    TCS_in_TibiaMarker = np.dot(T_TCS_to_TibiaMarker, TCS)

    # Step 2: Transform from Tibia Marker to Femur Marker Coordinate System
    TCS_in_FemurMarker = np.dot(T_TibiaMarker_to_FemurMarker, TCS_in_TibiaMarker)

    return TCS_in_FemurMarker


#
# def inverse_kinematics(A, B, C):
#     angle1 = math.atan2(B[0], B[1])
#
#     a = distance_3d(B, C)
#     b = distance_3d(C, A)
#     c = distance_3d(A, B)
#
#     def calculate_angle_b(a, b, c):
#         # Law of Cosines formula to calculate angle B
#         cos_angle_b = (a ** 2 + b ** 2 - c ** 2) / (2 * a * b)
#         angle_b_radians = math.acos(cos_angle_b)
#         angle_b_degrees = math.degrees(angle_b_radians)
#         return angle_b_degrees
#
#     angle2 = calculate_angle_b(a=a, b=b, c=c)
#
#     return np.degrees(angle1), angle2


def calculate_varus_angle(y_LCSr, x_FCS, z_FCS):
    # Project y_LCSr onto the yz-plane of FCS
    y_LCSr_proj = y_LCSr - np.dot(y_LCSr, x_FCS) * x_FCS

    # Calculate the angle between the projected vector and z_FCS
    cos_angle = np.dot(y_LCSr_proj, z_FCS) / (np.linalg.norm(y_LCSr_proj) * np.linalg.norm(z_FCS))
    angle_rad = np.arccos(np.clip(cos_angle, -1.0, 1.0))

    # Convert to degrees and subtract from 90°
    varus_angle = 90 - np.degrees(angle_rad)

    return varus_angle


def calculate_varus_flexion_angles(y_LCSr, z_FCS, y_FCS):
    # Calculate varus angle
    y_LCSr_proj_yz = y_LCSr - np.dot(y_LCSr, y_FCS) * y_FCS
    cos_varus = np.dot(y_LCSr_proj_yz, z_FCS) / (np.linalg.norm(y_LCSr_proj_yz) * np.linalg.norm(z_FCS))
    varus_angle = 90 - np.degrees(np.arccos(np.clip(cos_varus, -1.0, 1.0)))

    # Calculate flexion angle
    y_LCSr_proj_xy = y_LCSr - np.dot(y_LCSr, z_FCS) * z_FCS
    cos_flexion = np.dot(y_LCSr_proj_xy, y_FCS) / (np.linalg.norm(y_LCSr_proj_xy) * np.linalg.norm(y_FCS))
    flexion_angle = np.degrees(np.arccos(np.clip(cos_flexion, -1.0, 1.0))) - 90

    return varus_angle, flexion_angle


def inverse_kinematics(x, y, z, l2, l3, r):
    # print(f'{x}')
    # r = np.sqrt(x ** 2 + y**2 + z ** 2)
    r = np.sqrt(r)

    # r is calculated by subtracting x,y, z  coordinates of projected bone point with robot base coordinates

    # Calculate theta1
    theta1 = np.arctan2(z, x)
    D = np.sqrt(y ** 2 + r ** 2)
    theta3 = np.arccos((D ** 2 - l3 ** 2 - l2 ** 2) / (2 * l2 * l3))
    theta2 = np.arctan2(r, y) - np.arctan2(l2 + l3 * np.cos(theta3), l3 * np.sin(theta3))
    # Calculate theta2
    # cos_theta2 = (x**2 + y**2 + z**2 - l2**2 - l3**2) / (2 * l2 * l3)
    # theta2 = -np.arccos(cos_theta2)

    # Calculate theta3
    sin_theta3 = y / r
    tan_theta3 = l3 * np.sin(theta2) / (l2 - l3 * np.cos(theta2))
    # theta3 = (np.arcsin(sin_theta3) + np.arctan(tan_theta3))

    return np.degrees(theta1), np.degrees(theta2), np.degrees(theta3)


def calculate_point_on_vector(initial_point, direction_vector, distance):
    # Normalize the direction vector to get a unit vector
    unit_vector = direction_vector / np.linalg.norm(direction_vector)
    # Calculate the new point
    new_point = initial_point + distance * unit_vector
    return new_point


def forwardkin(L1, L2, T1, T2):
    '''
    This function gives end effector position
    It takes 4 arguments:
    L1 = length of Link 1
    L2 = length of Link 2
    T1 = Initial Position of Link 1 in degrees
    T2 = Initial Position of Link 2 in degrees
    '''
    # Convert degrees into radian
    T1 = T1 * (math.pi) / 180
    T2 = T2 * (math.pi) / 180

    # Link coordinates of Arm 1
    x1 = L1 * math.cos(T1)
    y1 = L1 * math.sin(T1)

    # Link Coordinates of Arm 2
    x2 = x1 + L2 * math.cos(T1 + T2)
    y2 = y1 + L2 * math.sin(T1 + T2)

    Sol = {'Link1': (x1, y1), 'Link2': (x2, y2)}
    return (Sol)


def invkin(L1, L2, X, Y):
    '''
    This function gives Joint Angles and link endpoints for a 2 link planar manipulator
    It takes 4 arguments:
    L1 = length of Link 1
    L2 = length of Link 2
    X = End Effector Position in X axis
    Y = End effector Position in Y axis
    '''
    # Using Analytical Approach

    c = ((X ** 2 + Y ** 2 - L1 ** 2 - L2 ** 2) / (2 * L1 * L2))

    if c ** 2 <= 1:
        print('Target is reachable')

        s_p = math.sqrt((1 - (c ** 2)))
        s_n = -(s_p)
        # s = [s_p, s_n]

        K1 = L1 + c * L2
        K2_a = s_p * L2
        K2_b = s_n * L2
        # K2 = [K2_a, K2_b]

        # Possible Joint 2 angles (in radians)
        T2_a = math.atan2(s_p, c)
        T2_b = math.atan2(s_n, c)

        # Possible Joint 1 angles (in radians)
        T1_a = math.atan2(Y, X) - math.atan2(K2_a, K1)
        T1_b = math.atan2(Y, X) - math.atan2(K2_b, K1)

        # Angles converted to degrees
        T1_ad = T1_a * 180 / (math.pi)  # Converted to degrees
        T2_ad = T2_a * 180 / (math.pi)  # Converted to degrees
        T1_bd = T1_b * 180 / (math.pi)  # Converted to degrees
        T2_bd = T2_b * 180 / (math.pi)  # Converted to degrees

        Sol1 = forwardkin(L1, L2, T1_ad, T2_ad)
        Sol1['Pos'] = (T1_ad, T2_ad)
        Sol2 = forwardkin(L1, L2, T1_bd, T2_bd)
        Sol2['Pos'] = (T1_bd, T2_bd)
        return (Sol1, Sol2)
    else:
        print('Target is not reachable, adjust link length.')


def compute_local_coordinate_system(ASL, ASR, PSC):
    # Convert to NumPy arrays
    # ASL = np.array(ASL)
    # ASR = np.array(ASR)
    # PSC = np.array(PSC)

    # Compute Y-Axis (normalized vector from ASR to ASL)
    Y_axis = ASL - ASR
    Y_axis /= np.linalg.norm(Y_axis)

    # Compute midpoint of ASL and ASR
    midpoint = (ASL + ASR) / 2.0

    # Compute X-Axis (normalized vector from midpoint to PSC)
    X_axis = PSC - midpoint
    X_axis /= np.linalg.norm(X_axis)

    # Compute Z-Axis (cross product of X and Y to maintain orthogonality)
    Z_axis = np.cross(X_axis, Y_axis)
    Z_axis /= np.linalg.norm(Z_axis)

    # Ensure X is re-orthogonalized
    X_axis = np.cross(Y_axis, Z_axis)

    return X_axis, Y_axis, Z_axis
