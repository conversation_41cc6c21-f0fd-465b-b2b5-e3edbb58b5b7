import numpy as np
from scipy.optimize import least_squares
from sklearn.preprocessing import RobustScaler

import numpy as np

def farthest_point_sampling(points, k):
    """
    Select k points from 'points' using farthest point sampling.

    Parameters:
        points (np.ndarray): Array of shape (N, 3), where N is the number of points.
        k (int): Number of points to sample.

    Returns:
        sampled_indices (list): List of indices of the selected points.
        sampled_points (np.ndarray): Array of sampled points, shape (k, 3).
    """
    N = points.shape[0]
    sampled_indices = [np.random.randint(N)]  # Start with a random point
    distances = np.full(N, np.inf)

    for _ in range(1, k):
        last_point = points[sampled_indices[-1]]
        dist_to_last = np.linalg.norm(points - last_point, axis=1)
        distances = np.minimum(distances, dist_to_last)
        next_index = np.argmax(distances)
        sampled_indices.append(next_index)

    sampled_points = points[sampled_indices]
    return sampled_indices, sampled_points

# Example usage:
# points = np.array([...])  # Your (N, 3) array of sphere points
# k = 10  # Number of points to sample
# indices, sampled = farthest_point_sampling(points, k)

# Example usage:
# led_coords = np.array(shape=(N,3,3)) # N frames, 3 LEDs, each with x,y,z
# hjc = estimate_hip_joint_center(led_coords)
# print("Estimated Hip Joint Center:", hjc)
new_leds = [
    [[510.92799362, 472.4593299 , 285.87192765], [504.6303606 , 474.94873708, 286.67870624], [511.03245746, 484.35924659, 282.52650089]],
    [[515.12544925, 467.0233223 , 279.88713801], [510.19612999, 471.6743089 , 278.27538733], [519.31623235, 475.58456211, 270.56729957]],
    [[519.6129006 , 462.48981477, 282.79408791], [517.05614554, 467.87926823, 278.82947231], [528.47636642, 467.56958538, 275.17532903]],
    [[520.45817348, 465.50722625, 289.97301977], [518.09863282, 471.79017109, 288.52574372], [529.88217082, 473.12611275, 289.32114005]],
    [[515.86176019, 471.23579839, 290.93631362], [510.82526932, 475.48567427, 292.82936013], [520.51760468, 482.37077001, 292.77120322]],
    [[509.081337  , 471.73489182, 282.38990398], [502.79961252, 473.88481148, 284.3892834 ], [507.69006281, 482.98440177, 277.12511605]],
    [[508.64122524, 468.80538216, 277.87346174], [502.5355153 , 471.5700866 , 279.15553424], [507.00747768, 478.16668255, 267.84607239]],
    [[520.39620159, 461.81613825, 284.45137166], [518.45518697, 467.50372921, 280.30160144], [530.08326038, 466.52840965, 277.88877708]],
    [[521.47814222, 461.99345001, 288.34853193], [520.51822895, 468.50097439, 286.11232951], [532.31932317, 466.85578486, 286.14843237]],
]
F_points = np.array(new_leds)
F_points_reshaped = F_points.reshape(-1, 3)
print(f'F_points_reshaped {F_points_reshaped}')

scaler = RobustScaler()
F_points_scaled = scaler.fit_transform(F_points_reshaped)
print(f'F_points_scaled {F_points_scaled}')

F_points_scaled_3d = F_points_scaled.reshape(F_points.shape)
print(f'F_points_scaled_3d {F_points_scaled_3d}')

# F_points_scaled_3d_inverse = scaler.inverse_transform(F_points_scaled_3d.reshape(-1, 3)).reshape(F_points.shape)
# print(f'F_points_scaled_3d_inverse {F_points_scaled_3d_inverse}')

centroid_list = []
for pair in F_points_scaled_3d:
    centroid = calculate_centroid(pair)
    centroid_list.append(centroid)

centroid_array = np.array(centroid_list)
print(centroid_array)
x, y, z = centroid_array[:, 0], centroid_array[:, 1], centroid_array[:, 2]
p0 = [0, 0, 0, 1]
result = least_squares(sphere_residuals, p0, args=(x, y, z))
sphere_center = result.x[:3]

sphere_center_inverse = scaler.inverse_transform([sphere_center])[0]
print(f"🎯 Sphere center: {sphere_center_inverse}")